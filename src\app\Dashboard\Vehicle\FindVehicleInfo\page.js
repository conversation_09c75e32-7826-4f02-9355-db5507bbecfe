'use client';

import { useState } from 'react';
import { API_URL_Vehicleinfo } from "@/app/Dashboard/Components/ApiUrl/ApiUrls";
import Header from '../../Components/Header';
import Sidebar from '../../Components/Sidebar';
export default function DVLAViewer() {
  const [regNumber, setRegNumber] = useState('');
  const [vehicle, setVehicle] = useState(null);
  const [loading, setLoading] = useState(false);

  const fetchVehicle = async () => {
    setLoading(true);
    setVehicle(null);

    try {
      const res = await fetch(API_URL_Vehicleinfo, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ registrationNumber: regNumber }),
      });

      const data = await res.json();
      if (data.status === 200) {
        setVehicle(data.result);
      } else {
        alert(data.message || 'Vehicle not found');
      }
    } catch (err) {
      alert('Error fetching data');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="h-[100vh] overflow-hidden">
    <Header className="min-w-full" />
    <div className="flex gap-4">
      <Sidebar />
      <div
        className="w-[80%] xl:w-[85%] h-screen flex flex-col justify-start overflow-y-auto pr-4"
        style={{
          height: 'calc(100vh - 90px)',
        }}
      >
        <h1 className="text-2xl font-bold mb-4 mt-4">DVLA Vehicle Lookup</h1>

        <div className="flex items-center gap-2 mb-6">
          <input
            type="text"
            placeholder="Enter Registration Number"
            value={regNumber}
            onChange={(e) => setRegNumber(e.target.value)}
            className="border p-2 rounded w-full"
          />
          <button
            onClick={fetchVehicle}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            {loading ? 'Searching...' : 'Search'}
          </button>
        </div>

        {vehicle && (
          <table className="w-full border border-gray-300 text-left bg-white shadow rounded">
            <tbody>
              {Object.entries(vehicle).map(([key, value]) => (
                <tr key={key} className="border-b border-gray-200">
                  <td className="font-medium capitalize p-2">
                    {key.replace(/([A-Z])/g, ' $1')}
                  </td>
                  <td className="p-2">{String(value)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
    </div>
  </div>
  );
}
