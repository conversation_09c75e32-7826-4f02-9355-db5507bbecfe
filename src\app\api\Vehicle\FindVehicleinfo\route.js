import { NextResponse } from "next/server";

// POST route to fetch vehicle details from DVLA API
export async function POST(request) {
  try {
    const body = await request.json();
    const { registrationNumber } = body;

    if (!registrationNumber) {
      return NextResponse.json({ message: "Registration number is required", status: 400 });
    }

    const response = await fetch("https://driver-vehicle-licensing.api.gov.uk/vehicle-enquiry/v1/vehicles", {
      method: "POST",
      headers: {
        "x-api-key": "tj4QCfAP1N2lEjebkHBtm3H0P3ChN3yM55vPlZOR",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ registrationNumber }),
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json({ message: "Failed to fetch vehicle info", details: data, status: response.status });
    }

    return NextResponse.json({ result: data, status: 200 });
  } catch (error) {
    console.error("Error calling DVLA API:", error);
    return NextResponse.json({ message: "Internal Server Error", status: 500 });
  }
}
