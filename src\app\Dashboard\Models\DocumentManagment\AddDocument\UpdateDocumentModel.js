"use client";

import React, { useState, useEffect } from "react";
import { toast } from "react-toastify";
import axios from "axios";
import { API_URL_Document } from "../../../Components/ApiUrl/ApiUrls";
import Image from "next/image";
import { useRouter } from "next/navigation";

const UpdateDocumentModel = ({ isOpen, onClose, documentData, fetchData }) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [formData, setFormData] = useState({
    documentname: "",
    entityType: [], // Changed to array for multiple selection
    isActive: false,
  });

  const entityOptions = [
    { value: "Vehicle", label: "Vehicle" },
    { value: "Driver", label: "Drivers" },
    { value: "Admins", label: "Admins" }
  ];

  useEffect(() => {
    if (documentData && isOpen) {
      setFormData({
        documentname: documentData.documentname || "",
        entityType: Array.isArray(documentData.entityType) ? documentData.entityType : [],
        isActive: documentData.isActive !== undefined ? documentData.isActive : false,
      });
      // Clear errors when opening modal with new data
      setErrors({});
    }
  }, [documentData, isOpen]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const handleEntityTypeChange = (entityValue) => {
    setFormData(prev => {
      const currentEntityTypes = [...prev.entityType];
      const index = currentEntityTypes.indexOf(entityValue);

      if (index > -1) {
        // Remove if already selected
        currentEntityTypes.splice(index, 1);
      } else {
        // Add if not selected
        currentEntityTypes.push(entityValue);
      }

      return {
        ...prev,
        entityType: currentEntityTypes
      };
    });

    // Clear entity type error when user makes selection
    if (errors.entityType) {
      setErrors(prev => ({
        ...prev,
        entityType: ""
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Validate document name
    if (!formData.documentname || formData.documentname.trim() === "") {
      newErrors.documentname = "Please add a document type.";
    }

    // Validate entity selection
    if (!formData.entityType || formData.entityType.length === 0) {
      newErrors.entityType = "Please select at least one entity.";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const token = localStorage.getItem("token");

      if (!token) {
        toast.error("Authentication token not found. Please login again.");
        router.push("/");
        return;
      }

      const response = await axios.put(
        `${API_URL_Document}/${documentData._id}`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data.success) {
        toast.success(response?.data?.message || "Document type has been updated.");
        fetchData();
        onClose();
        setErrors({});
      } else {
        // Handle server-side validation errors
        const errorMessage = response?.data?.error || response?.data?.message || "Failed to update document";

        // Check if it's a duplicate error and set appropriate field error
        if (errorMessage.includes("already exists")) {
          setErrors({ duplicate: errorMessage });
        } else if (errorMessage.includes("Please add a document type")) {
          setErrors({ documentname: errorMessage });
        } else if (errorMessage.includes("Please select at least one entity")) {
          setErrors({ entityType: errorMessage });
        } else {
          toast.warn(errorMessage);
        }
      }
    } catch (err) {
      console.error("Error updating document:", err);

      if (err.response?.status === 401) {
        toast.error("Authentication failed. Please login again.");
        router.push("/");
      } else {
        const errorMessage = err.response?.data?.error || err.response?.data?.message || err.message || "Failed to update document";

        // Handle specific validation errors from server
        if (errorMessage.includes("already exists")) {
          setErrors({ duplicate: errorMessage });
        } else if (errorMessage.includes("Please add a document type")) {
          setErrors({ documentname: errorMessage });
        } else if (errorMessage.includes("Please select at least one entity")) {
          setErrors({ entityType: errorMessage });
        } else {
          toast.error(errorMessage);
        }
      }
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50 z-50">
      <div className="bg-white p-[50px] w-[600px] rounded-xl shadow-lg max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold">
            Update Document Type
          </h2>

          <Image
            width={15}
            height={15}
            alt="cross"
            src="/crossIcon.svg"
            className="cursor-pointer"
            onClick={() => {
              onClose();
              setErrors({});
            }}
          />
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            {/* Entity Selection */}
            <div>
              <label className="text-sm font-medium mb-3 block">
                Select Entity: <span className="text-red-600">*</span>
              </label>
              <div className="flex flex-wrap gap-6">
                {entityOptions.map((option) => (
                  <label key={option.value} className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.entityType.includes(option.value)}
                      onChange={() => handleEntityTypeChange(option.value)}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                    />
                    <span className="text-sm">{option.label}</span>
                  </label>
                ))}
              </div>
              {errors.entityType && (
                <p className="text-red-500 text-xs mt-1">{errors.entityType}</p>
              )}
            </div>

            {/* Document Type Name */}
            <div>
              <label
                htmlFor="documentname"
                className="text-sm font-medium mb-2 block"
              >
                Document Type Name <span className="text-red-600">*</span>
              </label>
              <input
                type="text"
                id="documentname"
                name="documentname"
                value={formData?.documentname}
                onChange={handleChange}
                className={`mt-1 block w-full p-3 border rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.documentname ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter document type name"
              />
              {errors.documentname && (
                <p className="text-red-500 text-xs mt-1">{errors.documentname}</p>
              )}
            </div>

            {/* Verification Requirement */}
            <div>
              <label className="text-sm font-medium mb-3 block">
                Document verification is required:
              </label>
              <div className="space-y-2">
                <label className="flex items-center gap-3 cursor-pointer">
                  <input
                    type="radio"
                    name="isActive"
                    checked={formData.isActive === true}
                    onChange={() => setFormData(prev => ({ ...prev, isActive: true }))}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 focus:ring-2"
                  />
                  <span className="text-sm">Yes</span>
                </label>
                <label className="flex items-center gap-3 cursor-pointer">
                  <input
                    type="radio"
                    name="isActive"
                    checked={formData.isActive === false}
                    onChange={() => setFormData(prev => ({ ...prev, isActive: false }))}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 focus:ring-2"
                  />
                  <span className="text-sm">No</span>
                </label>
              </div>
            </div>

            {/* Display duplicate error if exists */}
            {errors.duplicate && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <p className="text-red-600 text-sm">{errors.duplicate}</p>
              </div>
            )}
          </div>

          <div className="flex gap-4 justify-end pt-4">
            <button
              type="button"
              onClick={() => {
                onClose();
                setErrors({});
              }}
              className="py-2 px-6 border border-[#313342] bg-white text-[#313342] rounded-md hover:bg-gray-50 focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-300"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-[#313342] text-white rounded-md hover:bg-gray-600 focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-300 py-2 px-8 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={loading}
            >
              {loading ? "Updating..." : "Update"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UpdateDocumentModel;
