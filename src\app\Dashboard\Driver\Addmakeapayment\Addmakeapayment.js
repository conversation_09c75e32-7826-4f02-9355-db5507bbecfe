"use client";
import axios from "axios";
import { useEffect, useState } from "react";
import { API_URL_Driver, API_URL_DriverMoreInfonano, API_URL_Driver_Vehicle_Allotment } from "@/app/Dashboard/Components/ApiUrl/ApiUrls";

import {
  getCompanyName,
  getUserId,
  getUserName, getflag, getcompanyId,
} from "@/utils/storageUtils";
import Image from "next/image";

const Addmakeapayment = ({ isOpen, onClose,
  fetchData,
  Id
}) => {
  const [formData, setFormData] = useState({
    driverId: "",
    driverName: "",
    vehicle: "",
    vehicleId: "",
    registrationNumber: "",
    startDate: null,
    cost: 0,
    pay: null,
    description: "",
  });

  const [loading, setLoading] = useState(false);
  useEffect(() => {
    const fetchDriverData = async () => {
      if (!Id) return;

      try {
        const { data } = await axios.get(`${API_URL_Driver}/${Id}`);
        const response = await axios.get(`${API_URL_Driver_Vehicle_Allotment}/${Id}`
        );
        console.log("Fetched Data:", data);

        setFormData((prevData) => ({
          ...prevData,
          driverId: response?.data?.result[0]?.driverId,
          driverName: response?.data?.result[0]?.driverName,
          vehicle: response?.data?.result[0]?.vehicle,
          vehicleId: response?.data?.result[0]?.vehicleId,
          registrationNumber: response?.data?.result[0]?.registrationNumber
        }));
      } catch (err) {
        console.error(err.response?.data?.message || "Failed to fetch driver data");
      }
    };

    fetchDriverData(); 
  }, [Id]);

  useEffect(() => {
    const companyName = getCompanyName();
    const userName = getUserName();
    const storedcompanyName = companyName || userName;
    const userId = getUserId();
    const flag = getflag();
    const compID = getcompanyId();


    if (storedcompanyName && userId) {
      if (storedcompanyName.toLowerCase() === "superadmin" && flag === "true") {
        setFormData((prevData) => ({
          ...prevData,
          adminCompanyName: storedcompanyName,
          companyId: compID,
        }));
      } else {
        setFormData((prevData) => ({
          ...prevData,
          adminCompanyName: storedcompanyName,
          companyId: userId,
        }));
      }
    }
  }, []);

  useEffect(() => {
    const today = new Date().toISOString().split("T")[0];
    setFormData((prevData) => ({
      ...prevData,
      startDate: today,
    }));
  }, []);


  const handleChange = (e) => {
    const { name, value, type } = e.target;

    setFormData((prevData) => ({
      ...prevData,
      [name]: type === "number" ? Number(value) : value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    if (!Id) {
      setLoading(false);
      return;
    }

    try {
      const response = await axios.post(`${API_URL_DriverMoreInfonano}`, formData);


      if (response.status === 200) {
        fetchData();
        const today = new Date().toISOString().split("T")[0];
        setFormData((prevData) => ({
          ...prevData,
          pay: null,
          startDate: today,
          description: ""
        }));
        onClose();
      } else {
        console.error("Failed to update record:", response?.data);
      }
    } catch (error) {
      console.error("Error updating record:", error?.response?.data || error?.message);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50 z-50">
      <div className="bg-white p-[50px] w-[528px] rounded-xl shadow-lg h-[500px]">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">
            Add Payment
          </h2>

          <Image width={15} height={15} alt="cross" src="/crossIcon.svg" className="cursor-pointer" onClick={() => {
            onClose();

          }} />
        </div>
        <form onSubmit={handleSubmit} className="space-y-2">
          <div className="w-[428px]">
            <div className="flex gap-1 items-center justify-start">
              <label
                htmlFor="firstName"
                className="text-[10px]"
              >
                Driver Name <span className="text-red-600">*</span>
              </label>
            </div>
            <input
              type="text"
              id="driverName"
              name="driverName"
              value={formData?.driverName}
              onChange={handleChange}
              className="mt-1 block w-full p-1 border border-[#42506666]  rounded shadow focus:ring-blue-500 focus:border-blue-500"
              required
              placeholder="Driver Name"
              disabled
            />
          </div>
          
          <div className="w-[428px]">
            <div className="flex gap-1 items-center justify-start">
              <label htmlFor="pay" className="text-[10px]">
                Driver Payment <span className="text-red-600">*</span>
              </label>
            </div>

            <div className="flex items-center border border-[#42506666] rounded shadow px-2">
              <span className="text-gray-600">£</span>
              <input
                type="number"
                id="pay"
                name="pay"
                value={formData?.pay || ""}
                min={0}
                onChange={handleChange}
                className="mt-1 block w-full p-1 border-none focus:ring-blue-500 focus:border-blue-500 outline-none"
                required
              />
            </div>
          </div>
          <div className="w-[428px]">
            <div className="flex gap-1 items-center justify-start">
              <label
                htmlFor="firstName"
                className="text-[10px]"
              >Date <span className="text-red-600">*</span>
              </label>
            </div>
            

            <input
              type="date"
              id="startDate"
              name="startDate"
              value={formData?.startDate}
              className="mt-1 block w-full p-1 border border-[#42506666] rounded shadow focus:ring-blue-500 focus:border-blue-500"
              required
              readOnly
            />
          </div>

          <div className="w-full">
            <label
              htmlFor="description"
              className="text-[10px]"
            >
              Description
            </label>
            <textarea
              id="description"
              name="description"
              value={formData?.description}
              onChange={handleChange}
              className="mt-1 block w-full p-2 border border-[#42506666] rounded-[4px] shadow focus:ring-blue-500 focus:border-blue-500"
              rows="2"
            ></textarea>
          </div>


          <div className="flex gap-[10px] justify-start">
            <button
              type="button"
              onClick={onClose}
              className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
            >
              Cancel
            </button>
            
            <button
              type="submit"
              className={`bg-[#313342] text-white rounded-4 hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 py-1 px-8 ${loading || !formData?.driverName || !formData.pay ? "opacity-75 cursor-not-allowed" : ""
                }`}
              disabled={loading || !formData.driverName}
            >
              {loading ? "Submitting..." : "Submit"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Addmakeapayment;