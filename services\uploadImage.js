/**
 * 🖼️  IMAGE UPLOAD SERVICEs

 * A robust image upload utility that handles file uploads to Cloudinary with
 * graceful fallback mechanisms. This service ensures that image uploads never
 * fail completely by providing default placeholder images when uploads fail.
 * 
 * Features:
 * • Cloudinary integration for cloud-based image storage
 * • Automatic fallback to default placeholder image
 * • Buffer-based file processing for memory efficiency
 * • Promise-based upload with proper error handling
 * • Consistent return format for easy integration
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 * @since 2024
 * ═══════════════════════════════════════════════════════════════════════════════
 */

import cloudinary from "@middlewares/cloudinary.js";

// Default fallback image
const DEFAULT_IMAGE_URL =
  "https://media.istockphoto.com/id/1409329028/vector/no-picture-available-placeholder-thumbnail-icon-illustration-design.jpg?s=1024x1024&w=is&k=20&c=Bs1RdueQnaAcO888WBIQsC6NvA7aVTzeRVzSd8sJfUg=";
const DEFAULT_IMAGE_ID = "123456789";

// Global reusable upload function
export async function uploadImage(file) {
  try {
    if (!file) {
      return {
        imageFile: DEFAULT_IMAGE_URL,
        imagePublicId: DEFAULT_IMAGE_ID,
      };
    }

    const buffer = Buffer.from(await file.arrayBuffer());

    const result = await new Promise((resolve, reject) => {
      cloudinary.uploader
        .upload_stream({ resource_type: "auto" }, (error, uploadResult) => {
          if (error) {
            reject(new Error("Error uploading image: " + error.message));
          } else {
            resolve(uploadResult);
          }
        })
        .end(buffer);
    });

    return {
      imageFile: result.secure_url,
      imagePublicId: result.public_id,
    };
  } catch (err) {
    console.error(err);
    return {
      imageFile: DEFAULT_IMAGE_URL,
      imagePublicId: DEFAULT_IMAGE_ID,
    };
  }
}
