"use client";
import { API_URL_Vehicle, API_URL_VehicleMOT, API_URL_VehicleRoadTex, API_URL_VehicleService, API_URL_Vehicleinfo } from "@/app/Dashboard/Components/ApiUrl/ApiUrls";
import axios from "axios";
import { toast } from "react-toastify";
import Select from "react-select";
import React, { useEffect, useState } from "react";
import {
  fetchLocalAuth,
  fetchManfacturer,
  fetchTransmission,
  fetchType,
  fetchFuelType,
  fetchCarModel,
  fetchSupplier
} from "../../Components/DropdownData/taxiFirm/taxiFirmService";

import {
  getCompanyName,
  getUserId,
  getUserName, getflag, getcompanyId, getUserRole
} from "@/utils/storageUtils";
import Image from "next/image";
const AddVehicleModel = ({ isOpen, onClose, fetchData }) => {
  const [imagePreview, setImagePreview] = useState(null);
  const [local, setLocal] = useState([]);
  const [selectedManufacturer, setSelectedManufacturer] = useState([]);
  const [carModel, setCarModel] = useState([]);
  const [transmission, setTransmission] = useState([]);
  const [type, setType] = useState([]);
  const [fueltype, setFuelType] = useState([]);
  const [step, setStep] = useState(1);
  const [maintenance, setMaintenance] = useState(false);
  const [selfFitSetting, setSelfFitSetting] = useState(false);
  const [fileInputs, setFileInputs] = useState([]);
  const [files, setFiles] = useState([]);
  const [previews, setPreviews] = useState([]);
  const [selectedCarmodel, setseletedCarmodel] = useState([]);
  const nextStep = () => setStep(step + 1);
  const prevStep = () => setStep(step - 1);
  const [suppliers, setSuppliers] = useState([]);
  const [imagePreviews, setImagePreviews] = useState([]);

  const [regNumber, setRegNumber] = useState('');

  const initialVehicleData = {
    manufacturer: "",
    model: "",
    year: "",
    type: "",
    engineType: "",
    fuelType: "",
    transmission: "",
    drivetrain: "",
    exteriorColor: "",
    interiorColor: "",
    // 
    dateOfLastV5CIssued: '',
    co2Emissions: '',
    taxStatus: "",
    motStatus: "",
    markedForExport: "",
    typeApproval: "",
    wheelplan: "",
    monthOfFirstRegistration: "",
    // 
    height: "",
    width: "",
    length: "",
    passengerCapacity: "",
    LocalAuthority: "",
    cargoCapacity: "",
    horsepower: "",
    torque: "",
    topSpeed: "",
    fuelEfficiency: "",
    safetyFeatures: [],
    techFeatures: [],
    towingCapacity: "",
    price: "",
    registrationNumber: "",
    vehicleStatus: "",
    warrantyInfo: "",
    adminCreatedBy: "",
    adminCompanyName: "",
    enginesize: "",
    chasisnumber: "",
    vehicleSite: "",
    fleetEntryDate: "",
    milesOnFleetEntry: "",
    plannedFleetExit: "",
    milesOnFleetExit: "",
    actualExitDate: "",
    milesAtActualExit: "",
    doors: "",
    color: "",
    editablecolor: "",
    roadTaxDate: "",
    roadTaxCycle: "",
    motDueDate: "",
    motCycle: "",
    seats: "",
    abiCode: "",
    nextServiceDate: "",
    nextServiceMiles: "",
    roadTaxCost: "",
    listPrice: "",
    purchasePrice: "",
    insuranceValue: "",
    departmentCode: "",
    maintenance: false,
    issues_damage: "",
    damage_image: [],
    recovery: "",
    organization: "",
    repairStatus: "",
    jobNumber: "",
    memo: "",

    parts: [
      {
        partNumber: "",
        partName: "",
        partprice: "",
        partsupplier: "",
      },
    ],

    TestDate: "",
    PlateExpiryDate: "",
    Insurance: "",
    insurancePolicyNumber: "",
    PDFofPolicy: "",
    defect: "",
    Defectdate: "",
    defectstatus: "",
    defectdescription: "",
    defectaction: "",

    additionalInfo: "",
    RPCExpiryDate: "",
    TailLiftExpiryDate: "",
    forkLiftNumber: "",
    ForkLiftInspectionDate: "",
    ForkLiftInspectionNumberNotes: "",
    cardocuments: [],
    isActive: false,
    imageFiles: [],
    companyId: null,
  }
  const [vehicleData, setVehicleData] = useState(initialVehicleData)

  // console.log("damage_image", initialVehicleData.damage_image)
  const pageonerequiredfeilds = [
    "registrationNumber",
    "fuelType",
    "year",
    "exteriorColor",
  ];
  const pagetworequiredfeilds = [
    "safetyFeatures",
    "techFeatures",
    "price",
    "registrationNumber",
  ];
  const pagefiverequiredfeilds = [
    "warrantyInfo",
  ];

  const areFieldsFilled = (fields) =>
    fields.every((field) => vehicleData[field] !== "");
  const isNextDisabled1st = !areFieldsFilled(pageonerequiredfeilds);
  const isNextDisabled2nd = !areFieldsFilled(pagetworequiredfeilds);
  const isNextDisabled5th = !areFieldsFilled(pagefiverequiredfeilds);

  useEffect(() => {
    const storedCompanyName = getCompanyName();
    const storedSuperadmin = getUserRole();
    if (storedCompanyName) {
      setVehicleData((prevData) => ({
        ...prevData,
        adminCompanyName: storedCompanyName,
      }));
    }

    const fetchData = async () => {
      try {
        const storedCompanyName = getCompanyName();
        const localAuthData = await fetchLocalAuth();
        const manufacturerData = await fetchManfacturer();
        const ModelData = await fetchCarModel();
        const transmissionData = await fetchTransmission();
        const typeData = await fetchType();
        const fueltypeData = await fetchFuelType();
        const supplier = await fetchSupplier();
        const currentCompanyName = storedCompanyName;

        const filteredLocalAuth =
          storedSuperadmin === "superadmin"
            ? localAuthData.Result
            : localAuthData.Result.filter(
              (localAuth) =>
                localAuth.adminCompanyName === currentCompanyName ||
                localAuth.adminCompanyName === "superadmin"
            );

        const filteredsupplier =
          storedSuperadmin === "superadmin"
            ? supplier.Result
            : supplier.Result.filter(
              (supplier) =>
                supplier.adminCompanyName === currentCompanyName ||
                supplier.adminCompanyName === "superadmin"
            );

        const filteredManufacturer =
          storedSuperadmin === "superadmin"
            ? manufacturerData.Result
            : manufacturerData.Result.filter(
              (manufacturer) =>
                manufacturer.adminCompanyName === currentCompanyName ||
                manufacturer.adminCompanyName === "superadmin"
            );

        const filteredCarModel =
          storedSuperadmin === "superadmin"
            ? ModelData.result
            : ModelData.result.filter(
              (model) =>
                model.adminCompanyName === currentCompanyName ||
                model.adminCompanyName === "superadmin"
            );
        const filteredTransmission =
          storedSuperadmin === "superadmin"
            ? transmissionData.Result
            : transmissionData.Result.filter(
              (transmission) =>
                transmission.adminCompanyName === currentCompanyName ||
                transmission.adminCompanyName === "superadmin"
            );
        const filteredType =
          storedSuperadmin === "superadmin"
            ? typeData.Result
            : typeData.Result.filter(
              (type) =>
                type.adminCompanyName === currentCompanyName ||
                type.adminCompanyName === "superadmin"
            );
        const filteredFuelType =
          storedSuperadmin === "superadmin"
            ? fueltypeData.Result
            : fueltypeData.Result.filter(
              (fueltype) =>
                fueltype.adminCompanyName === currentCompanyName ||
                fueltype.adminCompanyName === "superadmin"
            );

        setLocal(filteredLocalAuth);
        setSelectedManufacturer(filteredManufacturer);
        setCarModel(filteredCarModel);
        setTransmission(filteredTransmission);
        setType(filteredType);
        setFuelType(filteredFuelType);
        setSuppliers(filteredsupplier);

      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchData();
  }, [vehicleData.adminCompanyName]);

  useEffect(() => {

    const companyName = getCompanyName();
    const userName = getUserName();
    const cleanValue = (val) =>
      val && val !== "undefined" && val !== "null" ? val : null;

    const storedcompanyName = cleanValue(companyName) || cleanValue(userName);

    const userId = getUserId();
    const flag = getflag();
    const compID = getcompanyId();
    if (storedcompanyName && userId) {
      if (storedcompanyName.toLowerCase() === "superadmin" && flag === "true") {
        setVehicleData((prevData) => ({
          ...prevData,
          adminCompanyName: storedcompanyName,
          companyId: compID
        }));
      }
    } else {
      setVehicleData((prevData) => ({
        ...prevData,
        adminCompanyName: storedcompanyName,
        companyId: userId,
      }));
    }
  }, []);

  const handleChange = (e) => {
    const { name, value, type, checked, files } = e.target;

    if (type === "file" && name === "PDFofPolicy") {
      const file = files[0];
      setVehicleData((prevData) => ({
        ...prevData,
        [name]: file,
      }));

      if (file && file.type.startsWith("image/")) {
        const reader = new FileReader();
        reader.onloadend = () => {
          setImagePreview(reader.result);
        };
        reader.readAsDataURL(file);
      } else {
        setImagePreview(null);
      }
      return;
    }

    if (type === "file" && name === "damage_image") {
      const fileArray = Array.from(files);

      const previews = fileArray.map((file) => ({
        file,
        preview: URL.createObjectURL(file),
      }));

      setVehicleData((prevData) => ({
        ...prevData,
        damage_image: previews,
      }));

      return;
    }



    if (type === "file" && name === "imageFiles") {
      const allFiles = Array.from(files);

      const filteredFiles = allFiles.filter((file) => file.size <= 1024 * 1024);
      const oversizedFiles = allFiles.filter((file) => file.size > 1024 * 1024);

      if (oversizedFiles.length > 0) {
        toast.warn("Please select files less than 1MB");
      }

      setVehicleData((prevData) => ({
        ...prevData,
        imageFiles: filteredFiles,
      }));

      const previewPromises = filteredFiles.map((file) => {
        return new Promise((resolve) => {
          const reader = new FileReader();
          reader.onloadend = () => {
            resolve(reader.result);
          };
          reader.readAsDataURL(file);
        });
      });

      Promise.all(previewPromises).then((results) => {
        setImagePreviews(results);
      });

      return;
    }

    if (type === "checkbox") {
      setVehicleData((prevData) => ({
        ...prevData,
        [name]: checked,
      }));
      return;
    }

    if (name === "manufacturer") {
      const filteredCarModel = carModel.filter(
        (model) => model.makemodel === value
      );
      setseletedCarmodel(filteredCarModel);
      setVehicleData((prevData) => ({
        ...prevData,
        [name]: value,
        model: filteredCarModel,
      }));
      return;
    }

    setVehicleData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleRemoveImage = (index) => {
    const updatedImages = [...vehicleData.damage_image];
    updatedImages.splice(index, 1);

    setVehicleData((prevData) => ({
      ...prevData,
      damage_image: updatedImages,
    }));

    if (updatedImages.length === 0) {
      const input = document.getElementById('damage_image');
      if (input) input.value = '';
    }
  };

  const handleRemovePDFPreview = () => {
    setVehicleData((prevData) => ({
      ...prevData,
      PDFofPolicy: null,
    }));
    setImagePreview(null);

    const input = document.querySelector('input[name="PDFofPolicy"]');
    if (input) input.value = '';
  };
  
  const [useCustomManufacturer, setUseCustomManufacturer] = useState(false);
  
const fetchVehicle = async () => {
  try {
    const res = await fetch(API_URL_Vehicleinfo, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ registrationNumber: regNumber }),
    });

    const data = await res.json();

    if (data.status === 200) {
      const v = data.result;

      const apiMake = v?.make || "";

      const isInDropdown = selectedManufacturer.some(
        (m) => m.isActive && m.name.toLowerCase() === apiMake.toLowerCase()
      );

      setUseCustomManufacturer(!isInDropdown); 

      setVehicleData((prev) => ({
        ...prev,
        registrationNumber: v?.registrationNumber || "",
        manufacturer: apiMake,
        year: v?.yearOfManufacture?.toString() || "",
        enginesize: v?.engineCapacity?.toString() || "",
        fuelType: v?.fuelType?.toLowerCase() || "",
        exteriorColor: v?.colour || "",
        motDueDate: v?.motExpiryDate || "",
        taxStatus: v?.taxStatus || '',
        dateOfLastV5CIssued: v?.dateOfLastV5CIssued ? v?.dateOfLastV5CIssued.slice(0, 10) : '',
        co2Emissions: v?.co2Emissions || '',
        motStatus: v?.motStatus || '',
        markedForExport: v?.markedForExport?.toString() || '',
        typeApproval: v?.typeApproval || '',
        wheelplan: v?.wheelplan || '',
        monthOfFirstRegistration: v?.monthOfFirstRegistration || '',
      }));
    } else {
      const error = data?.details?.errors?.[0];
      if (error) {
        toast.error(error.title || error.detail || 'Vehicle not found');
      }
    }
  } catch (err) {
    console.log(err);
  }
};

  const [selectedOptions, setSelectedOptions] = useState(null);
  const [selectedOptionstech, setSelectedtech] = useState(null);

  const options = [
    { value: "airbags", label: "Airbags" },
    { value: "abs", label: "ABS" },
    { value: "stability control", label: "Stability Control" },
    { value: "traction control", label: "Traction Control" },
    { value: "blind spot monitoring", label: "Blind Spot Monitoring" },
    { value: "lane departure warning", label: "Lane Departure Warning" },
    { value: "adaptive cruise control", label: "Adaptive Cruise Control" },
    { value: "rearview camera", label: "Rearview Camera" },
    { value: "parking sensors", label: "Parking Sensors" },
    {
      value: "automatic emergency braking",
      label: "Automatic Emergency Braking",
    },
  ];

  const featurestech = [
    { value: "navigation", label: "Navigation" },
    { value: "bluetooth", label: "Bluetooth" },
    { value: "backup_camera", label: "Backup Camera" },
    { value: "adaptive_headlights", label: "Adaptive Headlights" },
    { value: "lane_keep_assist", label: "Lane Keep Assist" },
    { value: "parking_assist", label: "Parking Assist" },
    { value: "smartphone_integration", label: "Smartphone Integration" },
    { value: "voice_recognition", label: "Voice Recognition" },
    { value: "keyless_entry", label: "Keyless Entry" },
    { value: "rear_seat_entertainment", label: "Rear Seat Entertainment" },
  ];

  console.log("selected car model", selectedCarmodel)
  const handleChangesafty = (selected) => {
    setSelectedOptions(selected);
    const selectedValues = selected.map((option) => option.value);
    console.log("Selected Values:", selectedValues);
    setVehicleData((prevData) => ({
      ...prevData,
      safetyFeatures: selectedValues,
    }));
  };

  const handleChangestech = (selected) => {
    setSelectedtech(selected);
    const selectedValues = selected.map((option) => option.value);
    setVehicleData((prevData) => ({
      ...prevData,
      techFeatures: selectedValues,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const formData = new FormData();

    for (const key in vehicleData) {
      if (
        key === "imageFiles" ||
        key === "damage_image" ||
        key === "cardocuments"
      ) {
        vehicleData[key].forEach((file) => {
          formData.append(key, file);
        });
      } else if (typeof vehicleData[key] === "object") {
        for (const subKey in vehicleData[key]) {
          formData.append(`${key}[${subKey}]`, vehicleData[key][subKey]);
        }
      } else {
        formData.append(key, vehicleData[key]);
      }
    }

    try {

      console.log("vehicle Data", vehicleData)

      const response = await axios.post(API_URL_Vehicle, vehicleData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      if (response?.data?.success) {
        toast.success(response?.data?.message);
        try {
          const vehicleData = response?.data?.vehicle;
          if (vehicleData?.motDueDate) {
            await sendMotData({
              VehicleName: vehicleData?.model,
              registrationNumber: vehicleData?.registrationNumber,
              VehicleId: vehicleData?._id,
              motCurrentDate: "",
              motDueDate: vehicleData?.motDueDate,
              motCycle: vehicleData?.motCycle,
              motStatus: "done",
              VehicleStatus: vehicleData?.isActive,
              asignto: "N/A",
              motPending_Done: "0",
              adminCreatedBy: "",
              adminCompanyName: vehicleData?.adminCompanyName,
              adminCompanyId: "",
            });
          }

          if (vehicleData?.nextServiceDate && vehicleData?.nextServiceMiles) {
            await sendServiceData({
              VehicleName: vehicleData?.model,
              registrationNumber: vehicleData?.registrationNumber,
              VehicleId: vehicleData?._id,
              serviceCurrentDate: "",
              serviceDueDate: vehicleData?.nextServiceDate,
              serviceStatus: "done",
              VehicleStatus: vehicleData?.isActive,
              servicemailes: vehicleData?.nextServiceMiles,
              asignto: "N/A",
              servicePending_Done: "0",
              adminCreatedBy: "",
              adminCompanyName: vehicleData?.adminCompanyName,
              adminCompanyId: "",
            });
          }

          if (
            vehicleData?.roadTaxDate &&
            vehicleData?.roadTaxCycle &&
            vehicleData?.roadTaxCost
          ) {
            await sendRoadTaxData({
              VehicleName: vehicleData?.model,
              registrationNumber: vehicleData?.registrationNumber,
              VehicleId: vehicleData?._id,
              roadtexCurrentDate: "",
              roadtexDueDate: vehicleData?.roadTaxDate,
              roadtexCycle: vehicleData?.roadTaxCycle,
              VehicleStatus: vehicleData?.roadTaxCost,
              roadtexStatus: "done",
              VehicleStatus: vehicleData?.isActive,
              asignto: "N/A",
              roadtexPending_Done: "0",
              adminCreatedBy: "",
              adminCompanyName: vehicleData?.adminCompanyName,
              adminCompanyId: "",
            });
          }
        } catch (error) {
          console.error("Error sending data:", error);
          toast.error("Failed to send data");
        }

        fetchData();
        onClose();
        resetForm();
        setVehicleData(initialVehicleData)
        setFileInputs([]);
        setFiles([]);
      } else {
        toast.error(response.data.error);
      }
    } catch (error) {
      console.error("Error submitting vehicle data:", error);
    }
  };

  const handleAddMoreParts = () => {
    setVehicleData((prevData) => ({
      ...prevData,
      parts: [
        ...prevData.parts,
        { partNumber: "", partName: "", partprice: "", partsupplier: "" },
      ],
    }));
  };
  const handleRemovePart = (index) => {
    setVehicleData((prevData) => {
      const updatedParts = [...prevData.parts];
      updatedParts.splice(index, 1);
      return {
        ...prevData,
        parts: updatedParts,
      };
    });
  };

  const handlePartChange = (index, e) => {
    const { name, value } = e.target;
    const updatedParts = [...vehicleData?.parts];
    updatedParts[index][name] = value;

    setVehicleData((prev) => ({
      ...prev,
      parts: updatedParts,
    }));
  };

  const sendMotData = async (motData) => {
    try {
      const res = await axios.post(API_URL_VehicleMOT, motData);
      console.log("MOT Data sent successfully:", res.data);
    } catch (error) {
      console.error("Failed to send MOT data:", error);
    }
  };

  const sendServiceData = async (serviceData) => {
    try {
      const res = await axios.post(API_URL_VehicleService, serviceData);
      console.log("Service Data sent successfully:", res.data);
    } catch (error) {
      console.error("Failed to send Service data:", error);
    }
  };

  const sendRoadTaxData = async (roadTaxData) => {
    try {
      const res = await axios.post(API_URL_VehicleRoadTex, roadTaxData);
      console.log("Road Tax Data sent successfully:", res.data);
    } catch (error) {
      console.error("Failed to send Road Tax data:", error);
    }
  };

  const resetForm = () => {
    setStep(1);

  };

  const handleMaintenanceToggle = (e) => {
    const { checked } = e.target;
    setMaintenance(!maintenance);
    setVehicleData((prevData) => ({
      ...prevData,
      maintenance: checked,
    }));
  };

  const handleSelfFitsettingToggle = () => {
    setSelfFitSetting(!selfFitSetting);
  };

  const addFileInput = () => {
    if (fileInputs.length < 5) {
      setFileInputs([...fileInputs, `input-${Date.now()}`]);
    } else {
      alert("You Not Added More Then 5 Files");
    }
  };

  const handleFileChange = (event, index) => {
    const selectedFile = event.target.files[0];
    setFiles((prevFiles) => {
      const updatedFiles = [...prevFiles];
      updatedFiles[index] = selectedFile;
      return updatedFiles;
    });

    setVehicleData((prevData) => ({
      ...prevData,
      cardocuments: [...prevData.cardocuments, selectedFile],
    }));

    setPreviews((prevPreviews) => {
      const updatedPreviews = [...prevPreviews];
      updatedPreviews[index] = selectedFile
        ? URL.createObjectURL(selectedFile)
        : null; 
      return updatedPreviews;
    });
  };

  const handleImageClick = (index) => {
    document.getElementsByName("cardocuments")[index].click();
  };
  const cancleimages = () => {
    setFileInputs([]);
    setFiles([]);
  };
  const removeFileInput = (idx) => {
    setFileInputs((prevInputs) =>
      prevInputs.filter((_, index) => index !== idx)
    );

    setFiles((prevFiles) => {
      return prevFiles.filter((_, index) => index !== idx);
    });

    setPreviews((prevPreviews) => {
      return prevPreviews.filter((_, index) => index !== idx);
    });
  };

  if (!isOpen) return null;

  const years = Array.from({ length: 2012 - 1999 }, (_, index) => 2013 + index);

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50 z-50 ">
      <div className="bg-white p-12 rounded-xl shadow-lg w-full max-w-4xl overflow-y-auto max-h-screen">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">
            Vehicle Form
          </h2>

          <Image width={15} height={15} alt="cross" src="/crossIcon.svg" className="cursor-pointer" onClick={() => {
            onClose();
            setVehicleData(initialVehicleData)
            setStep(1);
          }} />
        </div>

        {step === 1 && (

          <div className="flex items-center gap-2 mb-6">
            <input
              type="text"
              placeholder="Enter Registration Number"
              value={regNumber}
              onChange={(e) => setRegNumber(e.target.value)}
              className="w-full p-2 border border-[#42506666] rounded shadow"
            />

            <button
              onClick={fetchVehicle}
              className="bg-[#313342] text-white rounded-4 hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 py-2 px-8"
            >
              Search
            </button>

          </div>)}

        <form onSubmit={handleSubmit} className=" mx-auto p-6 ">

          {step === 1 && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-x-2 gap-y-4">

                <div>
                  <div className="flex gap-1">
                    <label
                      className="text-[10px]"
                    >
                      Registration Number <span className="text-red-600">*</span>
                    </label>
                  </div>
                  <input
                    type="text"
                    name="registrationNumber"
                    value={vehicleData?.registrationNumber}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    required placeholder="Registration Number"
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Road Tax Due Date</label>
                  </div>
                  <input
                    type="date"
                    name="roadTaxDate"
                    value={vehicleData?.roadTaxDate}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    required
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label htmlFor="year" className="text-[10px]">
                      Year<span className="text-red-600">*</span>
                    </label>
                  </div>
                  <select
                    name="year"
                    value={vehicleData?.year}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    required
                  >
                    <option value="" disabled>Select Year</option>
                    {years?.map((year) => (
                      <option className=" text-xs" key={year} value={year}>{year}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Engine Size (cc)</label>
                  </div>
                  <input
                    type="number"
                    name="enginesize"
                    value={vehicleData?.enginesize}
                    onChange={handleChange}
                    placeholder="Enter Engine Size"
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label
                      htmlFor="Fuel_Type"
                      className="text-[10px]"
                    >
                      Fuel Type <span className="text-red-600">*</span>
                    </label>
                  </div>
                  <select
                    name="fuelType"
                    value={vehicleData?.fuelType}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    required
                  >
                    <option value="" disabled>
                      Select fuel type
                    </option>

                    {fueltype
                      .filter((fuel) => fuel.isActive) 
                      .map((fuelType) => (
                        <option key={fuelType?._id} value={fuelType?.name}>
                          {fuelType?.name}
                        </option>
                      ))}
                  </select>
                </div>

                <div>
                  <div className="flex gap-1">
                    <label
                      className="text-[10px]"
                    >
                      Exterior Color <span className="text-red-600">*</span>
                    </label>
                  </div>
                  <input
                    type="text"
                    name="exteriorColor"
                    value={vehicleData?.exteriorColor}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    placeholder="e.g., Red, Blue"
                    required
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">MOT Due Date</label>
                  </div>
                  <input
                    type="date"
                    name="motDueDate"
                    value={vehicleData?.motDueDate}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Tax Status</label>
                  </div>
                  <input
                    type="text"
                    name="taxStatus"
                    value={vehicleData?.taxStatus}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    required
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">MOT Status</label>
                  </div>
                  <input
                    type="text"
                    name="motStatus"
                    value={vehicleData?.motStatus}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    required
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Co2 Emissions</label>
                  </div>
                  <input
                    name="co2Emissions" type="text" value={vehicleData?.co2Emissions} onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    required
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Marked For Export</label>
                  </div>
                  <input
                    type="text"
                    name="markedForExport"
                    value={vehicleData?.markedForExport}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    required
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Type Approval</label>
                  </div>
                  <input
                    type="text"
                    name="typeApproval"
                    value={vehicleData?.typeApproval}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    required
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Date of last V5 C Issued</label>
                  </div>
                  <input
                    type="date" name="dateOfLastV5CIssued" value={vehicleData?.dateOfLastV5CIssued} onChange={handleChange}

                    className="w-full p-2 border border-[#42506666] rounded shadow"
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Wheel Plan</label>
                  </div>
                  <input
                    type="text"
                    name="wheelplan"
                    value={vehicleData?.wheelplan}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    required
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Month of First Registration</label>
                  </div>
                  <input
                    type="text"
                    name="monthOfFirstRegistration"
                    value={vehicleData?.monthOfFirstRegistration}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    required
                  />
                </div>
              </div>

              <div className="mt-6 flex gap-2 justify-end">
                <button
                  type="button"
                  onClick={() => {
                    onClose();
                    resetForm();
                    setVehicleData(initialVehicleData)
                    cancleimages();
                  }}
                  className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                >
                  Cancel
                </button>
                <button
                  onClick={nextStep}
                  className={`bg-[#313342] text-white rounded-4 hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 py-1 px-8 ${isNextDisabled1st
                    ? "bg-gray-400 text-white cursor-not-allowed"
                    : "bg-custom-bg text-white hover:bg-gray-600"
                    }`}
                  disabled={isNextDisabled1st}
                >
                  Next
                </button>
              </div>
            </>
          )}

          {step === 2 && (
            <>
              <div>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  
<div>
  <div className="flex gap-1">
    <label htmlFor="manufacturer" className="text-[10px]">
      Make <span className="text-red-600">*</span>
    </label>
  </div>

  {!useCustomManufacturer ? (
    <select
      id="manufacturer"
      name="manufacturer"
      value={vehicleData?.manufacturer}
      onChange={handleChange}
      className="w-full p-2 border border-[#42506666] rounded shadow"
      required
    >
      <option value="">Select Manufacturer</option>
      {selectedManufacturer
        .filter((m) => m.isActive)
        .map((m) => (
          <option key={m?._id} value={m?.name}>
            {m?.name}
          </option>
        ))}
    </select>
  ) : (
    <input
      type="text"
      name="manufacturer"
      value={vehicleData?.manufacturer}
      onChange={handleChange}
      className="w-full p-2 border border-[#42506666] rounded shadow"
      placeholder="Enter manufacturer"
      required
    />
  )}
</div>

                  <div>
                    <div className="flex gap-1">
                      <label htmlFor="model" className="text-[10px]">
                        Model<span className="text-red-600">*</span>
                      </label>
                    </div>
                    <select
                      name="model"
                      value={vehicleData?.model || ""}
                      onChange={handleChange}
                      className="w-full p-2 border border-[#42506666] rounded shadow bg-white"
                      required
                    >
                      <option value="">Select a model</option>
                      {selectedCarmodel
                        .filter((Car) => Car.isActive) 
                        .map((Carmodel) => (
                          <option key={Carmodel?._id} value={Carmodel?.name}>
                            {Carmodel?.name}
                          </option>
                        ))}
                    </select>
                  </div>

                  <div>
                    <div className="flex gap-1">
                      <label
                        className="text-[10px]"
                      >
                        Transmission <span className="text-red-600">*</span>
                      </label>
                    </div>
                    <select
                      name="transmission"
                      value={vehicleData?.transmission}
                      onChange={handleChange}
                      className="w-full p-2 border border-[#42506666] rounded shadow"
                      placeholder="e.g., Automatic"
                      required
                    >
                      <option value="" disabled>
                        Select Transmission
                      </option>

                      {transmission
                        .filter((tran) => tran.isActive) 
                        .map((trans) => (
                          <option key={trans?._id} value={trans?.name}>
                            {trans?.name}
                          </option>
                        ))}

                    </select>
                  </div>

                  <div>
                    <div className="flex gap-1">
                      <label
                        className="text-[10px]"
                      >
                        Passenger Capacity <span className="text-red-600">*</span>
                      </label>
                    </div>
                    <select
                      name="passengerCapacity"
                      value={vehicleData?.passengerCapacity}
                      onChange={handleChange}
                      className="w-full p-2 border border-[#42506666] rounded shadow"
                      required
                    >
                      <option value="" disabled>
                        Select Capacity
                      </option>

                      {["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"].map((val, i) => (
                        <option key={i} value={val}>
                          {val}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <div className="flex gap-1">
                      <label
                        className="text-[10px]"
                      >
                        Cargo Capacity (liters)
                      </label>
                    </div>
                    <input
                      type="text"
                      name="cargoCapacity"
                      value={vehicleData?.cargoCapacity}
                      onChange={handleChange}
                      className="w-full p-2 border border-[#42506666] rounded shadow"
                      placeholder="Cargo Capacity"
                    />
                  </div>

                  <div>
                    <div className="flex gap-1">

                      <label
                        className="text-[10px]"
                      >
                        Horsepower
                      </label>
                    </div>
                    <input
                      type="number"
                      name="horsepower"
                      value={vehicleData?.horsepower}
                      onChange={handleChange}
                      className="w-full p-2 border border-[#42506666] rounded shadow" placeholder="Horsepower"
                    />
                  </div>

                  <div>
                    <div className="flex gap-1">
                      <label className="text-[10px]">
                        Fuel Efficiency
                      </label>
                    </div>

                    <input
                      type="text"
                      name="fuelEfficiency"
                      value={vehicleData?.fuelEfficiency}
                      onChange={handleChange}
                      className="w-full p-2 border border-[#42506666] rounded shadow"
                      placeholder="e.g., 25 MPG"
                    />
                  </div>

                  <div>
                    <div className="flex gap-1">
                      <label
                        className="text-[10px]"
                      >
                        Price (£) <span className="text-red-600">*</span>
                      </label>
                    </div>
                    <input
                      type="number"
                      name="price"
                      value={vehicleData?.price}
                      onChange={handleChange}
                      className="w-full p-2 border border-[#42506666] rounded shadow"
                      required placeholder="Price"
                    />
                  </div>

                  <div>
                    <div>
                      <label htmlFor="taxiFirm" className="text-[10px]">
                        Local Authority
                      </label>

                    </div>
                    <select
                      id="LocalAuthority"
                      name="LocalAuthority"
                      value={vehicleData?.LocalAuthority}
                      onChange={handleChange}
                      className="w-full p-2 border border-[#42506666] rounded shadow"
                      required
                    >
                      <option value="">Select Local Authority</option>
                      {local
                        .filter((a) => a.isActive) 
                        .map((auth) => (
                          <option key={auth?._id} value={auth?.name}>
                            {auth?.name}
                          </option>
                        ))}
                    </select>
                  </div>

                  <div>
                    <div className="flex gap-1">
                      <label
                        htmlFor="body_Type"
                        className="text-[10px]"
                      >
                        Body Type <span className="text-red-600">*</span>
                      </label>
                    </div>

                    <select
                      name="type"
                      value={vehicleData?.type}
                      onChange={handleChange}
                      className="w-full p-2 border border-[#42506666] rounded shadow"
                      required
                    >
                      <option value="" disabled>
                        Select type
                      </option>

                      {type
                        .filter((t) => t.isActive) 
                        .map((ty) => (
                          <option key={ty?._id} value={ty?.name}>
                            {ty?.name}
                          </option>
                        ))}
                    </select>
                  </div>

                  <div>
                    <div className="flex gap-1">
                      <label
                        className="text-[10px]"
                      >
                        Drivetrain
                      </label>
                    </div>
                    <select
                      name="drivetrain"
                      value={vehicleData?.drivetrain}
                      onChange={handleChange}
                      className="w-full p-2 border border-[#42506666] rounded shadow"
                    >
                      <option value="" disabled>
                        Select drivetrain
                      </option>
                      <option value="FWD">Front-wheel drive (FWD)</option>
                      <option value="RWD">Rear-wheel drive (RWD)</option>
                      <option value="AWD">All-wheel drive (AWD)</option>
                      <option value="4WD">Four-wheel drive (4WD)</option>
                    </select>
                  </div>

                  <div>
                    <div className="flex gap-1">
                      <label
                        htmlFor="body_Type"
                        className="text-[10px]"
                      >
                        Engine Type <span className="text-red-600">*</span>
                      </label>
                    </div>
                    <input
                      type="text"
                      name="engineType"
                      value={vehicleData?.engineType}
                      onChange={handleChange}
                      className="w-full p-2 border border-[#42506666] rounded shadow"
                      placeholder="e.g., 2.5L 4-Cylinder"
                      required
                    />
                  </div>
                </div>

                <div>
                  <div>
                    <label
                      className="text-[10px]"
                    >
                      Safety Features
                    </label>
                  </div>
                  <div className="">
                    <Select
                      isMulti
                      options={options}
                      value={selectedOptions}
                      onChange={handleChangesafty}
                      placeholder="Select features..."
                      className="react-select w-full border border-[#42506666] rounded shadow"
                      classNamePrefix="select"
                    />
                  </div>
                </div>

                <div className="">
                  <div>
                    <label
                      className="text-[10px]"
                    >
                      Technology Features
                    </label>
                  </div>
                  <div className="">

                    <Select
                      isMulti
                      options={featurestech}
                      value={selectedOptionstech}
                      onChange={handleChangestech}
                      placeholder="Select Tech..."
                      className="react-select w-full border border-[#42506666] rounded  shadow"
                      classNamePrefix="select"
                    />
                  </div>
                </div>
              </div>

              <div className="mt-6 flex gap-[10px] justify-between">
                <button
                  onClick={prevStep}
                  className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                >
                  Back
                </button>
                <div className="flex justify-end gap-2">
                  <button
                    type="button"
                    onClick={() => {
                      onClose();
                      resetForm();
                      setVehicleData(initialVehicleData)
                      cancleimages();
                    }}
                    className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={nextStep}
                    className={`bg-[#313342] text-white rounded-4 hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 py-1 px-8 ${isNextDisabled2nd
                      ? "bg-gray-400 text-white cursor-not-allowed"
                      : "bg-custom-bg text-white hover:bg-gray-600"
                      }`}             
                  >
                    Next
                  </button>
                </div>
              </div>
            </>
          )}

          {step === 3 && (
            <>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-4">

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">
                      Vin / Chasis Number
                    </label>
                  </div>
                  <input
                    type="text"
                    name="chasisnumber"
                    value={vehicleData?.chasisnumber}
                    onChange={handleChange}
                    placeholder="Enter Chasis Number"
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label
                      className="text-[10px]"
                    >
                      Interior Color
                    </label>
                  </div>
                  <input
                    type="text"
                    name="interiorColor"
                    value={vehicleData?.interiorColor}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    placeholder="e.g., Black, Beige"
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Height(mm)</label>
                  </div>
                  <input
                    type="number"
                    name="height"
                    value={vehicleData?.height}
                    onChange={handleChange}
                    placeholder="Height in millimeter"
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Width(mm)</label>
                  </div>
                  <input
                    type="number"
                    name="width"
                    value={vehicleData?.width}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    placeholder="Width in millimeter"
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Length(in)</label>
                  </div>
                  <input
                    type="number"
                    name="length"
                    value={vehicleData?.length}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    placeholder="Length in inch"
                  />
                </div>

                <div>
                  <div>
                    <label className="text-[10px]">Doors</label>
                  </div>
                  <input
                    type="number"
                    name="doors"
                    value={vehicleData?.doors}
                    onChange={handleChange}
                    placeholder="2"
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                  />
                </div>

                <div>
                  <label className="text-[10px]">Road Tax Cycle</label>
                  <select
                    name="roadTaxCycle"
                    value={vehicleData?.roadTaxCycle}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                  >
                    <option value="">Select Cycle</option>
                    <option value="3months">3 Months</option>
                    <option value="6months">6 Months</option>
                    <option value="1year">1 Year</option>
                  </select>
                </div>

                <div className="relative">
                  <label className="text-[10px]">Road Tax Cost</label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-600">£</span>
                    <input
                      type="number"
                      name="roadTaxCost"
                      value={vehicleData?.roadTaxCost}
                      onChange={handleChange}
                      className="w-full p-2 pl-6 border border-[#42506666] rounded shadow"
                      placeholder="Enter road tax cost"
                    />
                  </div>
                </div>



                <div>
                  <label className="text-[10px]">MOT Cycle</label>
                  <select
                    name="motCycle"
                    value={vehicleData?.motCycle}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                  >
                    <option value="">Select Cycle</option>
                    <option value="3months">3 Months</option>
                    <option value="6months">6 Months</option>
                    <option value="1year">1 Year</option>
                  </select>
                </div>

                <div>
                  <label className="text-[10px]">ABI Code</label>
                  <input
                    type="text"
                    name="abiCode"
                    value={vehicleData?.abiCode}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    placeholder="Enter ABI Code"
                  />
                </div>

                <div>
                  <label className="text-[10px]">
                    Next Service Date
                  </label>
                  <input
                    type="date"
                    name="nextServiceDate"
                    value={vehicleData?.nextServiceDate}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                  />
                </div>
                <div>
                  <label className="text-[10px]">
                    Next Service Miles
                  </label>
                  <input
                    type="number"
                    name="nextServiceMiles"
                    value={vehicleData?.nextServiceMiles}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    placeholder="Enter miles for next service"
                  />
                </div>

              </div>

              <div className="mt-6 flex  justify-between">
                <button
                  onClick={prevStep}
                  className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                >
                  Back
                </button>
                <div className="flex justify-end gap-[10px]">
                  <button
                    type="button"
                    onClick={() => {
                      onClose();
                      resetForm();
                      cancleimages();
                    }}
                    className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                  >
                    Close
                  </button>
                  <button
                    onClick={nextStep}
                    className="bg-[#313342] text-white rounded-4 hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 py-1 px-8"
                  >
                    Next
                  </button>
                </div>
              </div>
            </>
          )}

          {step === 4 && (
            <>
              <h2 className="font-bold mb-4">
                Financials Information
              </h2>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-x-2 ">
                <div className="mb-1">
                  <label className="text-[10px]">
                    List Price (P11D)
                  </label>
                  <input
                    type="number"
                    name="listPrice"
                    value={vehicleData?.listPrice}
                    onChange={handleChange}
                    placeholder="List Price"
                    className="w-full p-2 border border-[#42506666] rounded  shadow"
                  />
                </div>

                <div className="mb-1">
                  <label className="text-[10px]">
                    Purchase Price
                  </label>
                  <input
                    type="number"
                    name="purchasePrice"
                    value={vehicleData?.purchasePrice}
                    onChange={handleChange}
                    placeholder="Purchase Price"
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                  />
                </div>

                <div className="mb-1">
                  <label className="text-[10px]">
                    Insurance Value
                  </label>
                  <input
                    type="number"
                    name="insuranceValue"
                    value={vehicleData?.insuranceValue}
                    onChange={handleChange}
                    placeholder="Insurance Value"
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                  />
                </div>

                <div className="mb-1">
                  <label className="text-[10px]">
                    Department Code
                  </label>
                  <input
                    type="text"
                    name="departmentCode"
                    value={vehicleData?.departmentCode}
                    onChange={handleChange}
                    placeholder="Department Code"
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                  />
                </div>
              </div>

              <div className="flex items-center justify-start gap-2 my-3">
                <label className="block text-gray-700 font-semibold">
                  Maintenance Record (if any )
                </label>
                <input
                  type="checkbox"
                  name="maintenance"
                  checked={maintenance}
                  onChange={handleMaintenanceToggle}
                />
              </div>
              {maintenance && (
                <>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-4">
                    <div className="mb-4 col-span-2">
                      <label className="text-[10px]">
                        Issues / Damages
                      </label>
                      <input
                        type="text"
                        name="issues_damage"
                        value={vehicleData?.issues_damage}
                        onChange={handleChange}
                        placeholder="Describe"
                        className="w-full p-2 border border-[#42506666] rounded shadow"
                      />
                    </div>
                    <div className="mb-4 col-span-1">
                      <label
                        htmlFor="useravatar"
                        className="text-[10px]"
                      >

                      </label>
                      <input
                        type="file"
                        id="damage_image"
                        name="damage_image"
                        accept="image/*"
                        onChange={handleChange}
                        className=" block w-48 text-[8px] text-gray-400 file:mr-4 file:py-1 p-2 file:px-4 file:rounded-lg file:border file:text-[10px] file:font-semibold file:bg-white hover:file:bg-blue-100 border border-[#0885864D] rounded-[10px] border-dashed "
                      />
                    </div>
                    {vehicleData?.damage_image?.length > 0 && (
                      <div className="mt-2 flex gap-2 flex-wrap">
                        {vehicleData?.damage_image?.map((item, index) => (
                          <div key={index} className="relative w-24 h-24">
                            <Image fill
                              src={item?.preview}
                              alt={`Damage Preview ${index + 1}`}
                              className="w-full h-full object-cover rounded border"
                            />
                            <button
                              type="button"
                              onClick={() => handleRemoveImage(index)}
                              className="absolute top-[-6px] right-[-6px] bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center shadow-md hover:bg-red-600"
                            >
                              ×
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mb-2">
                    <div className="mb-4">
                      <label className="text-[10px]">
                        Recovery
                      </label>
                      <input
                        type="text"
                        name="recovery"
                        value={vehicleData?.recovery}
                        onChange={handleChange}
                        placeholder="Recovery"
                        className="w-full p-2 border border-[#42506666] rounded shadow"
                      />
                    </div>

                    <div className="mb-4">
                      <label className="text-[10px]">
                        Repair Status
                      </label>
                      <input
                        type="text"
                        name="repairStatus"
                        value={vehicleData?.repairStatus}
                        onChange={handleChange}
                        placeholder="Repair status"
                        className="w-full p-2 border border-[#42506666] rounded shadow"
                      />
                    </div>

                    <div className="mb-4">
                      <label className="text-[10px]">
                        Job Number
                      </label>
                      <input
                        type="text"
                        name="jobNumber"
                        value={vehicleData?.jobNumber}
                        onChange={handleChange}
                        placeholder="Job number"
                        className="w-full p-2 border border-[#42506666] rounded shadow"
                      />
                    </div>

                    <div className="mb-4">
                      <label className="text-[10px]">
                        Organization
                      </label>
                      <select
                        name="organization"
                        value={vehicleData?.organization}
                        onChange={handleChange}
                        className="w-full p-2 border border-[#42506666] rounded shadow"
                      >
                        <option value="">Select Organization</option>
                        <option value="Organization1">Organization 1</option>
                        <option value="Organization2">Organization 2</option>
                      </select>
                    </div>

                    <div className="mb-4">
                      <label className="text-[10px]">
                        Memo
                      </label>
                      <input
                        type="text"
                        name="memo"
                        value={vehicleData?.memo}
                        onChange={handleChange}
                        placeholder="Memo for repair"
                        className="w-full p-2 border border-[#42506666] rounded shadow"
                      />
                    </div>
                  </div>
                  <h3 className="font-semibold text-gray-700 mb-2">Parts</h3>

                  {vehicleData?.parts?.map((part, index) => (
                    <div
                      key={index}
                      className="grid grid-cols-2 md:grid-cols-2 gap-2 mt-4 mb-2 relative"
                    >
                      <div>
                        <input
                          type="text"
                          name="partNumber"
                          value={part?.partNumber}
                          onChange={(e) => handlePartChange(index, e)}
                          placeholder="Part Number"
                          className="w-full p-2 border border-[#42506666] rounded shadow mb-2"
                        />
                      </div>

                      <div>
                        <input
                          type="text"
                          name="partName"
                          value={part?.partName}
                          onChange={(e) => handlePartChange(index, e)}
                          placeholder="Part Name"
                          className="w-full p-2 border border-[#42506666] rounded shadow mb-2"
                        />
                      </div>

                      <div>
                        <input
                          type="number"
                          name="partprice"
                          value={part?.partprice}
                          onChange={(e) => handlePartChange(index, e)}
                          placeholder="Price"
                          className="w-full p-2 border border-[#42506666] rounded shadow mb-2"
                        />
                      </div>

                      <div>
                        <select
                          name="partsupplier"
                          value={part?.partsupplier}
                          onChange={(e) => handlePartChange(index, e)}
                          className="w-full p-2 border border-[#42506666] rounded shadow mb-2"
                        >
                          <option value="">Select Supplier</option>
                          {suppliers
                            ?.filter((sup) => sup.isActive === true)
                            .map((supplier) => (
                              <option key={supplier._id} value={supplier.name}>
                                {supplier.name}
                              </option>
                            ))}
                        </select>
                      </div>
                      {index !== 0 && (
                        <button
                          type="button"
                          onClick={() => handleRemovePart(index)}
                          className="absolute -top-5 -right-5 text-red-500 hover:text-red-700"
                          title="Remove this part"

                        >
                          ❌
                        </button>
                      )}

                    </div>
                  ))}

                  <button
                    type="button"
                    onClick={handleAddMoreParts}
                    className="text-xs px-3 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
                  >
                    + Add More Parts
                  </button>


                </>
              )}
              <h2 className="font-bold mb-4">Commercial Vehicles</h2>

              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-4 mb-2">
                <div>
                  <label className="text-[10px]">RPC Expiry Date</label>
                  <input
                    type="date"
                    name="RPCExpiryDate"
                    value={vehicleData?.RPCExpiryDate}
                    onChange={handleChange}
                    className="w-full border border-[#42506666] rounded shadow p-2"
                  />
                </div>

                <div>
                  <label className="text-[10px]">
                    Tail-Lift Expiry Date
                  </label>
                  <input
                    type="date"
                    name="TailLiftExpiryDate"
                    value={vehicleData?.TailLiftExpiryDate}
                    onChange={handleChange}
                    className="w-full border border-[#42506666] rounded shadow p-2"
                  />
                </div>

                <div>
                  <label className="text-[10px]">
                    Fork Lift Inspection Date
                  </label>
                  <input
                    type="date"
                    name="ForkLiftInspectionDate"
                    value={vehicleData?.ForkLiftInspectionDate}
                    onChange={handleChange}
                    className="w-full border border-[#42506666] rounded shadow p-2"
                  />
                </div>

                <div>
                  <label className="text-[10px]">
                    Fork Lift Inspection Number/Notes
                  </label>
                  <input
                    type="text"
                    name="ForkLiftInspectionNumberNotes"
                    value={vehicleData?.ForkLiftInspectionNumberNotes}
                    onChange={handleChange}
                    placeholder="Inspection number or notes"
                    className="w-full border border-[#42506666] rounded shadow p-2"
                  />
                </div>
              </div>
              <div className="flex items-center">
                <label className="text-[10px]">
                  <input
                    type="checkbox"
                    name="selfFitSetting"
                    onChange={handleSelfFitsettingToggle}
                    checked={selfFitSetting}
                    className="mr-2"
                  />
                  Self-Fit Setting
                </label>
              </div>

              {selfFitSetting && (
                <div>
                  <label className="block font-semibold">
                    Additional Info
                  </label>
                  <textarea
                    name="additionalInfo"
                    value={vehicleData?.additionalInfo}
                    onChange={handleChange}
                    placeholder="Enter any additional info"
                    className="w-full border border-[#42506666] rounded shadow p-2"
                    rows="2"
                  />
                </div>
              )}

              <div className="mt-6 flex gap-2 justify-between">
                <button
                  onClick={prevStep}
                  className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                >
                  Back
                </button>
                <div className="flex justify-end gap-2">
                  <button
                    type="button"
                    onClick={() => {
                      onClose();
                      resetForm();
                      cancleimages();
                    }}
                    className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                  >
                    Close
                  </button>
                  <button
                    onClick={nextStep}
                    className="bg-[#313342] text-white rounded-4 hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 py-1 px-8"
                  >
                    Next
                  </button>
                </div>
              </div>
            </>
          )}

          {step === 5 && (
            <>
              <h2 className="font-bold">Local Authority</h2>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-4 mb-2">

                <div>
                  <label className="text-[10px]">Test Date</label>
                  <input
                    type="date"
                    name="TestDate"
                    value={vehicleData?.TestDate}
                    onChange={handleChange}
                    className="w-full border border-[#42506666] rounded-[4px] p-2"
                  />
                </div>

                <div>
                  <label className="text-[10px]">
                    Plate Expiry Date
                  </label>
                  <input
                    type="date"
                    name="PlateExpiryDate"
                    value={vehicleData?.PlateExpiryDate}
                    onChange={handleChange}
                    className="w-full border border-[#42506666] rounded-[4px] p-2"
                  />
                </div>

                <div>
                  <label className="text-[10px]">Insurance</label>
                  <input
                    type="text"
                    name="Insurance"
                    value={vehicleData?.Insurance}
                    onChange={handleChange}
                    placeholder="Enter insurance details"
                    className="w-full border border-[#42506666] rounded-[4px] p-2"
                  />
                </div>

                <div>
                  <label className="text-[10px]">
                    Insurance Policy Number:
                  </label>
                  <input
                    type="text"
                    name="insurancePolicyNumber"
                    value={vehicleData?.insurancePolicyNumber}
                    onChange={handleChange}
                    placeholder="Enter policy number"
                    className="w-full border border-[#42506666] rounded-[4px] p-2"
                  />
                </div>

                <div>
                  <label className="text-[10px]">
                    Picture or PDF of Policy:
                  </label>

                  <input
                    type="file"
                    name="PDFofPolicy"
                    onChange={handleChange}
                    accept="application/pdf, image/*"
                    className="block w-48 text-[8px] text-gray-400 file:mr-4 file:py-1 p-2 file:px-4 file:rounded-lg file:border file:text-[10px] file:font-semibold file:bg-white hover:file:bg-blue-100 border border-[#0885864D] rounded-[10px] border-dashed "
                  />

                </div>

                <div>
                  {imagePreview && (
                    <div className="relative w-32 h-20 mt-2">
                      {vehicleData?.PDFofPolicy?.type === "application/pdf" ? (
                        <embed
                          src={imagePreview}
                          type="application/pdf"
                          className="w-full h-full border rounded"
                        />
                      ) : (
                        <Image fill
                          src={imagePreview}
                          alt="Policy Preview"
                          className="w-full h-full object-cover border rounded"
                        />
                      )}
                      <button
                        type="button"
                        onClick={handleRemovePDFPreview}
                        className="absolute top-[-6px] right-[-6px] bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center shadow-md hover:bg-red-600"
                      >
                        ×
                      </button>
                    </div>
                  )}
                </div>

              </div>
              <h2 className="font-bold">Defect Details</h2>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 my-2">
                <div>
                  <label className="text-[10px]">Defect</label>
                  <input
                    type="text"
                    name="defect"
                    value={vehicleData?.defect}
                    onChange={handleChange}
                    placeholder="Enter defect name"
                    className="w-full border border-[#42506666] rounded-[4px] p-2"
                  />
                </div>

                <div>
                  <label className="text-[10px]">Date</label>
                  <input
                    type="date"
                    name="Defectdate"
                    value={vehicleData?.Defectdate}
                    onChange={handleChange}
                    className="w-full border border-[#42506666] rounded-[4px] p-2"
                  />
                </div>

                <div>
                  <label className="text-[10px]">Status</label>
                  <select
                    name="defectstatus"
                    value={vehicleData?.defectstatus}
                    onChange={handleChange}
                    className="w-full border border-[#42506666] rounded-[4px] p-2"
                  >
                    <option value="" disabled>
                      Select status
                    </option>
                    <option value="Pending">Pending</option>
                    <option value="InProgress">In Progress</option>
                    <option value="Resolved">Resolved</option>
                  </select>
                </div>

              </div>
              <div className="grid grid-cols-2 md:grid-cols-2 gap-2 ">
                <div>
                  <label className="text-[10px]">Description</label>
                  <textarea
                    name="defectdescription"
                    value={vehicleData?.defectdescription}
                    onChange={handleChange}
                    placeholder="Enter a brief description of the defect"
                    className="w-full border border-[#42506666] rounded-[4px] p-2 resize-none"
                    rows="2"
                  />
                </div>

                <div>
                  <label className="text-[10px]">Action</label>
                  <textarea
                    name="defectaction"
                    value={vehicleData?.defectaction}
                    onChange={handleChange}
                    placeholder="Describe the action taken or needed"
                    className="w-full border border-[#42506666] rounded-[4px] p-2 resize-none"
                    rows="2"
                  />
                </div>
              </div>
              <div>

                <h2 className="block font-semibold">Car Documents  </h2>
                <div>

                  <div className="image-file-inputs flex gap-5">
                    <Image width={100} height={100}
                      src="https://www.freeiconspng.com/uploads/file-add-icon-20.png"
                      alt="Add new file input"
                      onClick={addFileInput}
                      className="cursor-pointer mt-3 w-20 h-20 border-2 border-dashed border-gray-400 hover:border-gray-600"
                    />
                    <div className="flex gap-2">
                      {fileInputs.map((inputId, index) => {
                        return (
                          <div
                            key={inputId}
                            className="mt-2  relative"
                          >
                            <Image width={100} height={100}
                              src={
                                previews[index] ||
                                "https://via.placeholder.com/150"
                              }
                              alt={
                                files[index]
                                  ? files[index].name
                                  : "Click to select a file"
                              }
                              className="avatar-preview w-20 h-20 cursor-pointer rounded-md border border-gray-300 hover:border-gray-500 transition"
                              onClick={() => handleImageClick(index)}
                            />
                            <input
                              name="cardocuments"
                              type="file"
                              onChange={(e) => handleFileChange(e, index)}
                              style={{ display: "none" }} 
                            />
                            {!files[index] && (
                              <div
                                className="text-red-500 absolute top-0 right-0 cursor-pointer hover:bg-red-500 hover:text-white rounded-md w-5 bg-transparent h-5 text-center mb-2"
                                onClick={() => removeFileInput(index)} 
                              >
                                ✖
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-4 mb-2">
                <div className="flex flex-col">
                  <div>
                    <label className="text-[10px]">
                      Warranty Information <span className="text-red-600">*</span>
                    </label>
                  </div>
                  <textarea
                    name="warrantyInfo"
                    value={vehicleData?.warrantyInfo}
                    onChange={handleChange}
                    className="border border-[#42506666] rounded-[4px]-lg p-3 resize-none"
                    placeholder="e.g., 3 years or 36,000 miles"
                    required
                  />
                </div>

                <div>
                  <label className="text-[10px]">Vehicle Images</label>
                  <input
                    type="file"
                    id="imageFiles"
                    name="imageFiles"
                    onChange={handleChange}
                    className="mt-1 block w-48 text-[8px] text-gray-400 file:mr-4 file:py-1 p-2 file:px-4 file:rounded-lg file:border file:text-[10px] file:font-semibold file:bg-white hover:file:bg-blue-100 border border-[#0885864D] rounded-[10px] border-dashed "
                    multiple

                  />
                  <span className="block text-red-500 mt-2 text-xs">
                    Selected image should be less than 1MB
                  </span>

                  {imagePreviews.length > 0 && (
                    <div className="grid grid-cols-4 gap-2 mt-2">
                      {imagePreviews?.map((src, index) => (
                        <Image width={100} height={100}
                          key={index}
                          src={src}
                          alt={`Preview ${index + 1}`}
                          className="w-20 h-20 object-cover rounded border"
                        />
                      ))}
                    </div>
                  )}
                </div>
              </div>

              <div>
                <label className="text-[10px]">Status </label>
                <div className="flex gap-4">
                  <label className="flex items-center gap-2">
                    <input
                      type="radio"
                      name="isActive"
                      value="true"
                      checked={vehicleData?.isActive === true}
                      onChange={() =>
                        handleChange({
                          target: { name: "isActive", value: true },
                        })
                      }
                      className="accent-green-500"
                    />
                    <span className="text-xs">Active</span>
                  </label>

                  <label className="flex items-center gap-2">
                    <input
                      type="radio"
                      name="isActive"
                      value="false"
                      checked={vehicleData.isActive === false}
                      onChange={() =>
                        handleChange({
                          target: { name: "isActive", value: false },
                        })
                      }
                      className="accent-red-500"
                    />
                    <span className="text-xs">Inactive</span>
                  </label>
                </div>
              </div>
              <div className="mt-6 flex gap-2 justify-between">
                <button
                  onClick={prevStep}
                  className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                >
                  Back
                </button>
                <div className="flex justify-end gap-2">
                  <button
                    type="button"
                    onClick={() => {
                      onClose();
                      resetForm();
                      cancleimages();
                    }}
                    className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                  >
                    Close
                  </button>
                  <button
                    className={`bg-[#313342] text-white rounded-4 hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 py-1 px-8 ${isNextDisabled5th
                      ? "bg-gray-400 text-white cursor-not-allowed"
                      : "bg-custom-bg text-white hover:bg-gray-600"
                      }`} disabled={isNextDisabled5th}           >
                    Submit
                  </button>
                </div>
              </div>
            </>
          )}
        </form>
      </div>
    </div>
  );
};

export default AddVehicleModel;