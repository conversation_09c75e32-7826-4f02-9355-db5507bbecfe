"use client";

import React, { useState, useEffect, useCallback } from "react";
import Header from "../../../Components/Header";
import Sidebar from "../../../Components/Sidebar";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import AddVehicleType from "../AddVehicleType/AddVehicleType";
import UpdateVehicleType from "../UpdateVehicleType/UpdateVehicleModel";
import axios from "axios";
import { API_URL_VehicleType } from "@/app/Dashboard/Components/ApiUrl/ApiUrls";
import { GetVehicleType } from "@/app/Dashboard/Components/ApiUrl/ShowApiDatas/ShowApiDatas";
import DeleteModal from "@/app/Dashboard/Components/DeleteModal";
import {
  getCompanyName,
  getUserName
} from "@/utils/storageUtils";
import Image from "next/image";
import {Count} from "@/utils/count";
import SearchAndAddBar from "@/utils/searchAndAddBar";
import Pagination from "@/utils/pagination";

const Page = () => {
  const [data, setData] = useState([]);
  const [baseFilteredData, setBaseFilteredData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [paginatedData, setPaginatedData] = useState([]);
  const [isMounted, setIsMounted] = useState(false);
  const [isOpenVehicleType, setIsOpenVehicleType] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState(null);
  const [isOpenVehicleUpdate, setIsOpenVehcleUpdate] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleteModalOpenId, setIsDeleteModalOpenId] = useState(null);
  const [itemperpage, setitemperpage] = useState(Count);

  const handleFilterChange = useCallback((filtered) => {
    setFilteredData(filtered);
  }, []);

  const handleItemsPerPageChange = useCallback((newItemsPerPage) => {
    setitemperpage(newItemsPerPage);
  }, []);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const fetchData = async () => {
    try {
      const { result } = await GetVehicleType();
      setData(result);
    } catch (error) {
      console.error("Error fetching data:", error);
      setData([]);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    const storedcompanyName = getCompanyName() || getUserName();
    
    if (data.length > 0) {
      let companyFilteredData;
      
      if (storedcompanyName === 'superadmin') {
        companyFilteredData = data;
      } else {
        companyFilteredData = data?.filter((item) =>
          item?.adminCompanyName?.toLowerCase() === storedcompanyName?.toLowerCase()
        );
      }
      
      setBaseFilteredData(companyFilteredData);
    } else {
      setBaseFilteredData([]);
    }
  }, [data]);

  useEffect(() => {
    setFilteredData(baseFilteredData);
  }, [baseFilteredData]);

  const isopendeletemodel = (id) => {
    setIsDeleteModalOpenId(id); 
    setIsDeleteModalOpen(true); 
  };

  const handleDelete = async (id) => {
    console.log("Deleting ID:", id);
    try {
      const response = await axios.delete(`${API_URL_VehicleType}/${id}`);
      const { data } = response;

      if (data.status === 200) {
        setData((prevData) => prevData?.filter((item) => item._id !== id));
        toast.success("Vehicle Type deleted successfully!");
      } else {
        toast.warn(data.message || "Failed to delete the Vehicle Type.");
      }
    } catch (error) {
      console.error("Error deleting Vehicle Type:", error);
      toast.error(
        error.response?.data?.message ||
        "An error occurred while deleting the Vehicle Type. Please try again."
      );
    }
  };

  const OpenVehicleTypeModle = () => {
    setIsOpenVehicleType(!isOpenVehicleType);
  };

  const handleEdit = (id) => {
    setSelectedUserId(id);
    setIsOpenVehcleUpdate(true);
  };

  const OpenVehicleUpdateModle = () => {
    setIsOpenVehcleUpdate(!isOpenVehicleUpdate);
  };

  if (!isMounted) {
    return null;
  }

  return (
    <div className="h-[100vh] overflow-hidden">
      <Header className="min-w-full" />
      <div className="flex gap-4">
        <Sidebar />
        <div className="w-[80%] xl:w-[85%] min-h-screen">
          <div
            className="justify-between mx-auto items-center  w-full overflow-y-auto pr-4 "
            style={{ height: "calc(100vh - 90px)" }}
          >
            <h1 className="text-[#313342] font-medium text-2xl py-5 pb-8  flex gap-2 items-center">
              <div className="myborder flex gap-3 border-2 border-t-0 border-l-0 border-r-0">
                <span className="opacity-65">Vehicle Setting</span>
                <div className="flex items-center gap-3 myborder2">
                  <span>
                    <Image width={2} height={4}
                      src="/setting_arrow.svg"
                      className="w-2 h-4 object-cover object-center  "
                    alt="arrow" />
                  </span>
                  <span>Vehicles Type</span>
                </div>
              </div>
            </h1>

            <div className="w-full py-5">
              <div className="drop-shadow-custom4 ">
                <SearchAndAddBar
                  data={baseFilteredData}
                  itemperpage={itemperpage}
                  onAddClick={OpenVehicleTypeModle}
                  addLabel="Add Vehicle Type"
                  onFilterChange={handleFilterChange}
                  onItemsPerPageChange={handleItemsPerPageChange}
                  searchPlaceholder="Search by vehicle type name..."
                />

                <div className="overflow-x-auto overflow-y-hidden custom-scrollbar">
                  <table className="w-full bg-white border  table-fixed">
                    <thead className="font-sans font-bold text-sm text-left">
                      <tr className="text-white bg-[#38384A]">
                        <th className="px-4 py-3 min-w-[150px] w-[150px] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                          Vehicle Type
                        </th>
                        <th className="px-4 py-3 min-w-[150px] w-[150px] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                          Description
                        </th>
                        <th className="px-4 py-3 min-w-[150px] w-[150px] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                          Status
                        </th>
                        <th className="px-4 py-3 min-w-[150px] w-[150px] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {paginatedData?.map((item) => (
                        <tr key={item?._id} className="border-b text-center">
                          <td className=" py-3 px-4 min-w-[150px] w-[150px] whitespace-normal break-all overflow-hidden">
                            {item?.name}
                          </td>
                          <td className="py-3 px-4 min-w-[150px] w-[150px] whitespace-normal break-all overflow-hidden">
                            {item?.description}
                          </td>
                          <td className="">
                            <span
                              className={`px-4 py-2 rounded-[22px] text-xs ${item?.isActive || item?.isActive === false
                                  ? "bg-[#38384A33]"
                                  : ""
                                }`}
                            >
                              {item.isActive ? "Active" : "Inactive"}
                            </span>

                          </td>
                          <td className="py-3 px-4 min-w-[150px] w-[150px] whitespace-normal break-all overflow-hidden">
                            <div className="flex gap-4 justify-center">
                              <div className="relative group">
                                <button
                                  onClick={() => handleEdit(item?._id)}
                                  className="text-blue-500 hover:text-blue-700"
                                >
                                  <Image width={25} height={25}
                                    src="/edit.png"
                                    alt="edit"
                                    className="w-6"
                                  />
                                </button>
                              </div>
                              <div className="relative group">
                                <button
                                  onClick={() => isopendeletemodel(item?._id)}
                                  className="text-red-500 hover:text-red-700"
                                >
                                  <Image width={25} height={25}
                                    src="/trash.png"
                                    alt="delete"
                                    className="w-6"
                                  />
                                </button>
                              </div>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                
                <Pagination
                  data={filteredData}
                  itemperpage={itemperpage}
                  onPageChange={(currentItems) => {
                    setPaginatedData(currentItems);
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <AddVehicleType
        isOpen={isOpenVehicleType}
        onClose={OpenVehicleTypeModle}
        fetchData={fetchData}
      />

      <UpdateVehicleType
        isOpen={isOpenVehicleUpdate}
        onClose={OpenVehicleUpdateModle}
        fetchData={fetchData}
        vehicleid={selectedUserId}
      />
      <DeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onDelete={handleDelete}
        Id={isDeleteModalOpenId}
      />
    </div>
  );
};

export default Page;
