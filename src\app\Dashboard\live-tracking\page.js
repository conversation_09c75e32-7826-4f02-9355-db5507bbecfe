'use client';
import { useEffect, useState, useRef } from "react";
import { io } from "socket.io-client";
import "leaflet/dist/leaflet.css";

// ✅ Use your live backend domain
const socket = io("http://127.0.0.1:4001", {
  transports: ["websocket"],
});

const Page = () => {
  const [gpsData, setGpsData] = useState([]);
  const [devices, setDevices] = useState([]);
  const [socketStatus, setSocketStatus] = useState("disconnected");
  const mapRef = useRef(null);
  const mapInstance = useRef(null);
  const markers = useRef({});

  useEffect(() => {
    let L; // Leaflet will be imported dynamically

    import("leaflet").then((leaflet) => {
      L = leaflet;

      // ✅ Fix for default markers in Leaflet with React/Next.js
      delete L.Icon.Default.prototype._getIconUrl;
      L.Icon.Default.mergeOptions({
        iconRetinaUrl:
          "https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png",
        iconUrl:
          "https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png",
        shadowUrl:
          "https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png",
      });

      // ✅ Initialize map only once
      if (!mapInstance.current && mapRef.current) {
        mapInstance.current = L.map(mapRef.current).setView(
          [20.5937, 78.9629], // Default India center
          5
        );

        L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
          attribution:
            '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        }).addTo(mapInstance.current);
      }

      // ✅ Socket event handlers
      socket.on("connect", () => {
        console.log("✅ Connected to WebSocket server");
        setSocketStatus("connected");
      });

      socket.on("disconnect", () => {
        console.log("❌ Disconnected from WebSocket server");
        setSocketStatus("disconnected");
      });

      socket.on("devices-update", (deviceList) => {
        setDevices(deviceList);
      });

      socket.on("gps-data", (data) => {
        console.log("📡 GPS Data:", data);

        // Keep only the latest 10 records
        setGpsData((prev) => [data, ...prev].slice(0, 10));

        // Update map if we have coordinates
        if (data.parsed && data.parsed.gps && data.parsed.gps.lat && data.parsed.gps.lng) {
          updateMap(L, data.imei, data.parsed);
        }
      });
    });

    return () => {
      socket.off("connect");
      socket.off("disconnect");
      socket.off("devices-update");
      socket.off("gps-data");
    };
  }, []);

  // ✅ Update markers dynamically
  const updateMap = (L, imei, parsedData) => {
    if (!mapInstance.current) return;

    const latlng = [parsedData.gps.lat, parsedData.gps.lng];

    if (markers.current[imei]) {
      // Update existing marker
      markers.current[imei].setLatLng(latlng);
    } else {
      // Create new marker
      markers.current[imei] = L.marker(latlng)
        .addTo(mapInstance.current)
        .bindPopup(
          `<b>Device ${imei}</b><br>
           Speed: ${parsedData.gps.speed} km/h<br>
           Time: ${parsedData.timestamp}`
        );
    }

    // Center map on this marker
    mapInstance.current.setView(latlng, 15);
    markers.current[imei].openPopup();
  };

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <h1 className="text-3xl font-bold text-center mb-6 text-gray-800">
        Teltonika GPS Tracker
      </h1>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Connection Status */}
        <div className="bg-white p-4 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-2">Connection Status</h2>
          <p
            className={
              socketStatus === "connected" ? "text-green-600" : "text-red-600"
            }
          >
            WebSocket:{" "}
            {socketStatus === "connected" ? "Connected" : "Disconnected"}
          </p>
          <p>Connected Devices: {devices.length}</p>
        </div>

        {/* Device List */}
        <div className="bg-white p-4 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-2">Connected Devices</h2>
          {devices.length === 0 ? (
            <p className="text-gray-600">No devices connected</p>
          ) : (
            <ul>
              {devices.map((device, index) => (
                <li key={index} className="mb-1">
                  <span className="font-medium">{device}</span>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>

      {/* Map Container */}
      <div className="bg-white p-4 rounded-lg shadow-md mb-6">
        <h2 className="text-xl font-semibold mb-4">Device Locations</h2>
        <div ref={mapRef} className="w-full h-96 rounded-md" />
      </div>

      {/* GPS Data Table */}
      <div className="bg-white p-4 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Latest GPS Data</h2>
        {gpsData.length === 0 ? (
          <p className="text-gray-600 text-center py-4">
            ⏳ Waiting for GPS data...
          </p>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-800 text-white">
                <tr>
                  <th className="py-2 px-4">IMEI</th>
                  <th className="py-2 px-4">Latitude</th>
                  <th className="py-2 px-4">Longitude</th>
                  <th className="py-2 px-4">Speed</th>
                  <th className="py-2 px-4">Timestamp</th>
                  <th className="py-2 px-4">Raw Data</th>
                </tr>
              </thead>
              <tbody>
                {gpsData.map((d, idx) => (
                  <tr
                    key={idx}
                    className="border-b text-center hover:bg-gray-50 transition"
                  >
                    <td className="py-2 px-4">{d.imei ?? "Unknown"}</td>
                    <td className="py-2 px-4">
                      {d.parsed?.gps?.lat ?? "N/A"}
                    </td>
                    <td className="py-2 px-4">
                      {d.parsed?.gps?.lng ?? "N/A"}
                    </td>
                    <td className="py-2 px-4">
                      {d.parsed?.gps?.speed
                        ? `${d.parsed.gps.speed} km/h`
                        : "N/A"}
                    </td>
                    <td className="py-2 px-4">
                      {d.parsed?.timestamp
                        ? new Date(d.parsed.timestamp).toLocaleString()
                        : "N/A"}
                    </td>
                    <td className="py-2 px-4 font-mono text-xs">
                      {d.raw ? `${d.raw.substring(0, 20)}...` : "N/A"}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default Page;
