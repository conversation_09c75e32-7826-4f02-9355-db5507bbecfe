"use client";

import React, { useState, useEffect, useCallback } from "react";
import { toast } from "react-toastify";
import Header from "../../../Components/Header";
import Sidebar from "../../../Components/Sidebar";
import AddRoadTexModal from "../AddRoadTaxModal/AddRoadTaxModal";
import { API_URL_VehicleRoadTex } from "../../../Components/ApiUrl/ApiUrls";
import { getCompanyName, getsuperadmincompanyname, getUserName } from "@/utils/storageUtils";
import axios from "axios";
import jsPDF from "jspdf";
import { isAuthenticated } from "@/utils/verifytoken";
import { useRouter } from "next/navigation";
import BackButton from "@/app/Dashboard/Components/BackButton";
import Image from "next/image";
import {Count} from "@/utils/count";
import SearchAndAddBar from "@/utils/searchAndAddBar";
import Pagination from "@/utils/pagination";


const Page = ({ params }) => {
  const router = useRouter();
  const addRoadTaxReportId = params?.AddRoadTaxReport;
  
  const [data, setData] = useState([]);
  const [baseFilteredData, setBaseFilteredData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [isOpenTitle, setIsOpenTitle] = useState(false);
  const [selectedCompanyName, setSelectedCompanyName] = useState("");
  const [itemperpage, setitemperpage] = useState(Count);
  const [paginatedData, setPaginatedData] = useState([]);
  const [selectedid, setselectedid] = useState(null);

  console.log(data);
  
  const handleFilterChange = useCallback((filtered) => {
    setFilteredData(filtered);
  }, []);

  const handleItemsPerPageChange = useCallback((newItemsPerPage) => {
    setitemperpage(newItemsPerPage);
  }, []);

  useEffect(() => {
    if (!isAuthenticated()) {
      router.push("/");
      return;
    }
  }, [router]);
  useEffect(() => {
    const companyNameFromStorage = getCompanyName();
    if (companyNameFromStorage) {
      setSelectedCompanyName(companyNameFromStorage);
    }
  }, []);

  const fetchData = useCallback(async () => {
    try {
      // Fetch all road tax data similar to AddMaintenanceReport pattern
      const response = await axios.get(`${API_URL_VehicleRoadTex}`);
      
      // Check both 'Result' and 'result' properties (API inconsistency)
      const allRoadTaxData = response?.data?.Result || response?.data?.result || [];
      
      if (Array.isArray(allRoadTaxData)) {
        // Filter road tax data by vehicle ID
        const vehicleRoadTaxData = allRoadTaxData.filter(roadTax => 
          roadTax.VehicleId === addRoadTaxReportId || 
          roadTax.vehicleId === addRoadTaxReportId ||
          roadTax._id === addRoadTaxReportId
        );
        setData(vehicleRoadTaxData);
      } else {
        console.warn("Road tax data is not an array:", allRoadTaxData);
        setData([]);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      setData([]);
    }
  }, [addRoadTaxReportId]);

  const handleDelete = async (id) => {
    try {
      const response = await axios.delete(`${API_URL_VehicleRoadTex}/${id}`);
      if (response?.data?.success) {
        setData((prev) => prev.filter((item) => item?._id !== id));
        toast.success(response?.data?.message);
      } else {
        toast.warn(response.data.message);
      }
    } catch (error) {
      console.error("Error deleting:", error);
    }
  };

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    const companyName = getCompanyName();
    const superAdminName = getsuperadmincompanyname();
    const userName = getUserName();
    const storedcompanyName = companyName || superAdminName || userName;

    if (data.length > 0) {
      let companyFilteredData;

      if (userName?.toLowerCase() === "superadmin") {
        companyFilteredData = data;
      } else {
        companyFilteredData = data?.filter(
          (item) =>
            item?.adminCompanyName?.toLowerCase() === "superadmin" ||
            item?.adminCompanyName?.toLowerCase() === storedcompanyName?.toLowerCase()
        );
      }

      setBaseFilteredData(companyFilteredData);
    } else {
      setBaseFilteredData([]);
    }
  }, [data]);

  useEffect(() => {
    setFilteredData(baseFilteredData);
  }, [baseFilteredData]);

  const toggleTitleModal = () => {
    setIsOpenTitle((prev) => !prev);
    setselectedid(addRoadTaxReportId);
  };

  const generatePDF = () => {
    const doc = new jsPDF();

    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text("Road Tax Records Report", 105, 10, { align: "center" });

    const reportDate = new Date().toLocaleDateString();
    doc.setFontSize(10);
    doc.setFont("helvetica", "normal");
    doc.text(`Report Generated: ${reportDate}`, 14, 20);

    const vehicleName = filteredData[0]?.VehicleName || "Name Unavailable";
    const vehicleRegistration = filteredData[0]?.registrationNumber || "Registration Unavailable";
    const companyName = selectedCompanyName || "Company Unavailable";

    doc.text(`Vehicle Name: ${vehicleName}`, 14, 25);
    doc.text(`Registration Number: ${vehicleRegistration}`, 14, 30);
    doc.text(`Company Name: ${companyName}`, 14, 35);

    const tableColumn = ["Current Date", "Due Date", "Tax Cycle", "Assign To", "Tax Status"];
    const columnWidths = [30, 30, 30, 40, 60]; 

    let startX = 14;
    let startY = 45;
    const lineHeight = 8;
    const pageHeight = doc?.internal?.pageSize?.height;

    doc.setFont("helvetica", "bold");
    tableColumn.forEach((column, index) => {
      const x = startX + columnWidths.slice(0, index).reduce((a, b) => a + b, 0);
      doc.text(column, x + 2, startY + lineHeight / 2, { align: "left" });
      doc.rect(x, startY, columnWidths[index], lineHeight);
    });

    let currentY = startY + lineHeight;
    doc.setFont("helvetica", "normal");

    filteredData.forEach((row) => {
      if (currentY + lineHeight > pageHeight - 20) {
        doc.addPage();
        currentY = 20;

        doc.setFont("helvetica", "bold");
        tableColumn.forEach((column, index) => {
          const x = startX + columnWidths.slice(0, index).reduce((a, b) => a + b, 0);
          doc.text(column, x + 2, currentY + lineHeight / 2, { align: "left" });
          doc.rect(x, currentY, columnWidths[index], lineHeight);
        });
        currentY += lineHeight;
        doc.setFont("helvetica", "normal");
      }

      const currentDate = row?.roadtexCurrentDate
        ? new Date(row.roadtexCurrentDate).toLocaleDateString()
        : "N/A";
      const dueDate = row.roadtexDueDate
        ? new Date(row.roadtexDueDate).toLocaleDateString()
        : "N/A";
      const cycle = row?.roadtexCycle || "N/A";
      const assignTo = row?.asignto || "N/A";
      const status = row?.roadtexStatus || "N/A";

      const rowData = [currentDate, dueDate, cycle, assignTo, status];

      rowData.forEach((cell, index) => {
        const x = startX + columnWidths.slice(0, index).reduce((a, b) => a + b, 0);
        doc.text(cell, x + 2, currentY + lineHeight / 2, { align: "left" });
        doc.rect(x, currentY, columnWidths[index], lineHeight);
      });

      currentY += lineHeight;
    });

    doc.save("Road_Tax_Report.pdf");
  };

  return (
    <div className="h-[100vh] overflow-hidden">
      <Header className="min-w-full" />
      <div className="flex gap-4 w-full">
        <Sidebar />
        <div
          className="w-[80%] xl:w-[85%] h-screen flex flex-col justify-start overflow-y-auto pr-4"
          style={{
            height: "calc(100vh - 90px)",
          }}
        >
          <h1 className="text-[#313342] font-medium text-2xl py-5 pb-8  flex gap-2 items-center">
            <div className="myborder flex gap-3 border-2 border-t-0 border-l-0 border-r-0">
              <span className="opacity-65">Vehicle</span>
              <div className="flex items-center gap-3 myborder2">
                <span>
                  <Image width={2} height={4}
                    src="/setting_arrow.svg"
                    className="w-2 h-4 object-cover object-center  "
                    alt="arrow" />
                </span>
                <span>Road Tax</span>
              </div>
            </div>
          </h1>

          <div className="py-5">
            <div className="drop-shadow-custom4">

              <div className="flex w-full py-2 px-2">
                <SearchAndAddBar
                  data={baseFilteredData}
                  itemperpage={itemperpage}
                  onFilterChange={handleFilterChange}
                  onItemsPerPageChange={handleItemsPerPageChange}
                  showSearch={false}
                  showAddButton={false}
                />
                <div className="flex justify-center gap-2 items-center">
                  <BackButton />
                  <button
                    onClick={generatePDF}
                    className={`w-[170px] font-sans text-sm font-semibold  px-5 h-10  border-[1px] rounded-lg border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500`}
                  >
                    Download Report
                  </button>
                  <button
                    onClick={toggleTitleModal}
                    className="w-[132px] font-sans  font-bold text-xs bg-[#313342] text-white rounded-lg hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 px-3 flex py-[12px] gap-2 items-center justify-center"
                  >
                    <Image width={15} height={15} src="/plus.svg" alt="Add Road Tax" />
                    Add Road Tax
                  </button>
                </div>
              </div>

              <div className="overflow-x-auto custom-scrollbar">
                <table className="w-full bg-white border table-auto">
                  <thead className="font-sans font-bold text-sm text-center bg-[#38384A] text-white">
                    <tr>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        Assigned To
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        Vehicle Name
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        Registration No
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        Road Tax Dates
                      </th>
                      <th className="py-3 px-4 min-w-[165px] w-[165px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        Road Tax Due Date
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        Road Tax Cycle
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        Road Tax Status
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="font-sans font-medium text-sm text-center">
                    {paginatedData?.length > 0 ? (
                      paginatedData?.map((row) => (
                        <tr key={row?._id} className="border-b hover:bg-gray-100 text-center">
                          <td className="py-2 px-4 text-center whitespace-normal break-all overflow-hidden">
                            {row?.asignto || "N/A"}
                          </td>
                          <td className="py-2 px-4 text-center whitespace-normal break-all overflow-hidden">
                            {row?.VehicleName}
                          </td>
                          <td className="py-2 px-4 text-center whitespace-normal break-all overflow-hidden">
                            {row?.registrationNumber}
                          </td>
                          <td className="py-2 px-4 text-start whitespace-normal break-all overflow-hidden">
                            {row?.roadtexCurrentDate
                              ? new Date(
                                row?.roadtexCurrentDate
                              ).toLocaleDateString()
                              : "N/A"}
                          </td>
                          <td className="py-2 px-4 text-start whitespace-normal break-all overflow-hidden">
                            {row?.roadtexDueDate
                              ? new Date(row.roadtexDueDate).toLocaleDateString()
                              : "N/A"}
                          </td>
                          <td className="py-2 px-4 text-center whitespace-normal break-all overflow-hidden">
                            {row?.roadtexCycle ? row.roadtexCycle.replace("month", " Month").replace("year", " Year") : "N/A"}
                          </td>
                          <td className="py-2 px-4 text-center whitespace-normal break-all overflow-hidden">
                            {row?.roadtexStatus || "N/A"}
                          </td>
                          <td className="py-2 px-4 text-center whitespace-normal break-all overflow-hidden">
                            <button
                              onClick={() => handleDelete(row._id)}
                              className="text-red-500 hover:text-red-700"
                            >
                              <Image width={25} height={25}
                                src="/trash.png"
                                alt="delete"
                                className="w-6"
                              />
                            </button>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan="8" className="py-8 text-center text-gray-500">
                          No road tax records found for this vehicle. Click &quot;Add Road Tax&quot; to create a new record.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              <Pagination
                data={filteredData}
                itemperpage={itemperpage}
                onPageChange={(currentItems) => {
                  setPaginatedData(currentItems);
                }}
              />
            </div>
          </div>
        </div>
      </div>
      <AddRoadTexModal
        isOpen={isOpenTitle}
        onClose={toggleTitleModal}
        fetchData={fetchData}
        selectedid={selectedid}
      />
    </div>
  );
};

export default Page;
