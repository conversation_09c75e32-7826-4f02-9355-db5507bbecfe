import { connect } from "@config/db.js";
import DriverMoreInfo from "@models/DriverMoreInfo/DriverMoreInfo.model.js";
import { catchAsyncErrors } from "@middlewares/catchAsyncErrors.js";
import { NextResponse } from "next/server";
import jwt from "jsonwebtoken";

export const POST = catchAsyncErrors(async (request) => {
  await connect();
  const data = await request.json();
   // Extract Token from Headers
   const authHeader = request.headers.get("authorization");
   if (!authHeader || !authHeader.startsWith("Bearer ")) {
     return NextResponse.json({ error: "Unauthorized", status: 401 });
   }

   // Decode JWT Token
   const token = authHeader.split(" ")[1];
   const decoded = jwt.verify(token,  process.env.JWT_SECRET); // Use your secret key
     // Extract User Details from Token
  const { driverId, driverName, adminCompanyName } = decoded;

  const { startDate, pay } = data;

  try {
    // Find the latest record for the driver
    const latestRecord = await DriverMoreInfo.findOne({
      driverId,
      driverName,
      adminCompanyName,
    }).sort({ createdAt: -1 });

    if (!latestRecord) {
      return NextResponse.json({
        error: "No previous records found. Payment cannot be processed.",
        status: 400,
      });
    }

    let total = latestRecord.totalamount || 0; // Ensure total is not undefined

    // Subtract payment from total
    if (pay > 0) {
      total -= pay;
    }

    // Convert startDate to Date object
    const date = new Date(startDate);

    // Create and save the new payment record
    const newDriverMoreInfo = new DriverMoreInfo({
      driverId: latestRecord.driverId,
      driverName: latestRecord.driverName,
      vehicle: latestRecord.vehicle,
      vehicleId: latestRecord.vehicleId,
      startDate: date,
      payment:0,
      cost:0, // Always 0
      pay,
      totalamount: total,
      registrationNumber: latestRecord.registrationNumber,
      description: "From Device",
      adminCompanyName: latestRecord.adminCompanyName,
      adminCompanyId: latestRecord.adminCompanyId, // Fixed incorrect field
    });

    const savedDriverMoreInfo = await newDriverMoreInfo.save();

    if (!savedDriverMoreInfo) {
      return NextResponse.json({
        error: "Payment not recorded",
        status: 400,
      });
    }

    return NextResponse.json({
      message: "Payment recorded successfully",
      success: true,
      savedDriverMoreInfo,
      status: 200,
    });
  } catch (error) {
    console.error("Error saving payment:", error);
    return NextResponse.json({
      error: "An error occurred while processing payment.",
      status: 500,
    });
  }
});
