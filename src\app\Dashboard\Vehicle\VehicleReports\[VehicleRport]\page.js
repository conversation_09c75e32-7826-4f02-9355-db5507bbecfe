"use client";

import React, { useState, useEffect } from "react";
import axios from "axios";
import { jsPDF } from "jspdf";
import Header from "../../../Components/Header";
import Sidebar from "../../../Components/Sidebar";
import { API_URL_Vehicle } from "@/app/Dashboard/Components/ApiUrl/ApiUrls";
import { isAuthenticated } from "@/utils/verifytoken";
import { useRouter } from "next/navigation";
import BackButton from "@/app/Dashboard/Components/BackButton";

import { FaEye, FaTimes } from "react-icons/fa";
import Image from "next/image";

const Page = ({ params }) => {
  const router = useRouter();
  const id = params?.VehicleRport;
  const [data, setData] = useState(null);
  const [isMounted, setIsMounted] = useState(false);

  const [showPdfModal, setShowPdfModal] = useState(false);
  const [pdfUrl, setPdfUrl] = useState(null);

  useEffect(() => {
    if (!isAuthenticated()) {
      router.push("/");
      return;
    }
  }, [router]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios.get(`${API_URL_Vehicle}/${id}`);
        const vehicleData = response?.data?.result;
        setData(vehicleData);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchData();
  }, [id]);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const generatePdf = () => {
    const doc = new jsPDF();
    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text("Vehicle Report", 105, 10, { align: "center" });

    const reportDate = new Date().toLocaleDateString();
    doc.setFontSize(10);
    doc.setFont("helvetica", "normal");
    doc.text(`Report Generated: ${reportDate}`, 14, 20);

    const vehicleName = data?.model || "N/A";
    const registrationNumber = data?.registrationNumber || "N/A";
    const companyName = "Encoderbytes";

    doc.text(`Vehicle Name: ${vehicleName}`, 14, 25);
    doc.text(`Registration No: ${registrationNumber}`, 14, 30);
    doc.text(`Company Name: ${companyName}`, 14, 35);

    const headers = [
      "Vehicle Name",
      "Registration No",
      "Vehicle Authority", 
      "Engine Type",
      "Drive Train",
      "Status",
    ];

    const startX = 14;
    const startY = 45;
    const rowHeight = 10;
    const columnWidths = [30, 35, 40, 27, 27, 25];

    let currentY = startY;

    doc.setFont("helvetica", "bold");
    headers.forEach((header, index) => {
      const x =
        startX + columnWidths.slice(0, index).reduce((a, b) => a + b, 0);
      doc.text(header, x + 2, currentY);
      doc.rect(x, currentY - 6, columnWidths[index], rowHeight);
    });

    currentY += rowHeight;

    const renderRow = (row) => {
      doc.setFont("helvetica", "normal");
      const rowData = [
        row?.model || "N/A",
        row?.registrationNumber || "N/A",
        row?.LocalAuthority || "N/A",
        row?.engineType || "N/A",
        row?.drivetrain || "N/A",
        row?.isActive ? "Active" : "Inactive",
      ];

      rowData.forEach((cell, index) => {
        const x =
          startX + columnWidths.slice(0, index).reduce((a, b) => a + b, 0);
        const wrappedText = doc.splitTextToSize(cell, columnWidths[index] - 2);
        doc.text(wrappedText, x + 2, currentY);
        doc.rect(
          x,
          currentY - 6,
          columnWidths[index],
          rowHeight * wrappedText.length
        );
      });

      currentY += rowHeight;

      if (currentY + rowHeight > doc.internal.pageSize.height - 10) {
        doc.addPage();
        currentY = startY;
        headers.forEach((header, index) => {
          const x =
            startX + columnWidths.slice(0, index).reduce((a, b) => a + b, 0);
          doc.text(header, x + 2, currentY);
          doc.rect(x, currentY - 6, columnWidths[index], rowHeight);
        });
        currentY += rowHeight;
      }
    };

    if (data) {
      renderRow(data);
    }

    const pdfBlob = doc.output('blob');
    const pdfObjectURL = URL.createObjectURL(pdfBlob);
    setPdfUrl(pdfObjectURL);
    setShowPdfModal(true);
  };

  const handleDownloadReport = () => {
    const doc = new jsPDF();
    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text("Vehicle Report", 105, 10, { align: "center" });

    const reportDate = new Date().toLocaleDateString();
    doc.setFontSize(10);
    doc.setFont("helvetica", "normal");
    doc.text(`Report Generated: ${reportDate}`, 14, 20);

    const vehicleName = data?.model || "N/A";
    const registrationNumber = data?.registrationNumber || "N/A";
    const companyName = "Encoderbytes";

    doc.text(`Vehicle Name: ${vehicleName}`, 14, 25);
    doc.text(`Registration No: ${registrationNumber}`, 14, 30);
    doc.text(`Company Name: ${companyName}`, 14, 35);

    const headers = [
      "Vehicle Name",
      "Registration No",
      "Vehicle Authority", 
      "Engine Type",
      "Drive Train",
      "Status",
    ];

    const startX = 14;
    const startY = 45;
    const rowHeight = 10;
    const columnWidths = [30, 35, 40, 27, 27, 25];

    let currentY = startY;

    doc.setFont("helvetica", "bold");
    headers.forEach((header, index) => {
      const x =
        startX + columnWidths.slice(0, index).reduce((a, b) => a + b, 0);
      doc.text(header, x + 2, currentY);
      doc.rect(x, currentY - 6, columnWidths[index], rowHeight);
    });

    currentY += rowHeight;

    const renderRow = (row) => {
      doc.setFont("helvetica", "normal");
      const rowData = [
        row?.model || "N/A",
        row?.registrationNumber || "N/A",
        row?.LocalAuthority || "N/A",
        row?.engineType || "N/A",
        row?.drivetrain || "N/A",
        row?.isActive ? "Active" : "Inactive",
      ];

      rowData.forEach((cell, index) => {
        const x =
          startX + columnWidths.slice(0, index).reduce((a, b) => a + b, 0);
        const wrappedText = doc.splitTextToSize(cell, columnWidths[index] - 2);
        doc.text(wrappedText, x + 2, currentY);
        doc.rect(
          x,
          currentY - 6,
          columnWidths[index],
          rowHeight * wrappedText.length
        );
      });

      currentY += rowHeight;

      if (currentY + rowHeight > doc.internal.pageSize.height - 10) {
        doc.addPage();
        currentY = startY;
        headers.forEach((header, index) => {
          const x =
            startX + columnWidths.slice(0, index).reduce((a, b) => a + b, 0);
          doc.text(header, x + 2, currentY);
          doc.rect(x, currentY - 6, columnWidths[index], rowHeight);
        });
        currentY += rowHeight;
      }
    };

    if (data) {
      renderRow(data);
    }

    doc.save("Vehicle_Report.pdf");
  };

  const closeModal = () => {
    setShowPdfModal(false);
    URL.revokeObjectURL(pdfUrl); 
    setPdfUrl(null);
  };

  if (!isMounted) return null;
  return (
    <div className="h-[100vh] overflow-hidden">
      <Header className="min-w-full" />
      <div className="flex gap-4">
        <Sidebar />
        <div
          className="w-[80%] xl:w-[85%] h-screen flex flex-col justify-start overflow-y-auto pr-4"
          style={{
            height: "calc(100vh - 90px)",
          }}
        >
          <h1 className="text-[#313342] font-medium text-2xl py-5 pb-8  flex gap-2 items-center">
            <div className="myborder flex gap-3 border-2 border-t-0 border-l-0 border-r-0">
              <span className="opacity-65">Vehicle</span>
              <div className="flex items-center gap-3 myborder2">
                <span>
                  <Image height={4} width={2}
                    src="/setting_arrow.svg"
                    className="w-2 h-4 object-cover object-center  "
                     alt="arrow"/>
                </span>
                <span>Car Details</span>
              </div>
            </div>
          </h1>
          <div className="py-5">
            <div className="drop-shadow-custom4">
              <div className="w-full flex justify-end py-4">
                <div className="flex gap-4 items-center">
                  <BackButton />
                  <button
                    onClick={handleDownloadReport}
                    className="bg-[#38384A] text-white px-4 py-2 rounded-lg hover:bg-[#4f4f66]"
                  >
                    Download Report
                  </button>
                </div>
              </div>
              <div className="overflow-x-auto custom-scrollbar">
                <table className="w-full bg-white border table-auto">
                  <thead className="font-sans font-bold text-sm text-center">
                    <tr className="text-white bg-[#38384A]">
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Vehicle Name
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Registration N0
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Local Authority
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Engine Type
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Drive Train
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Status
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        View
                      </th>
                    </tr>
                  </thead>
                  <tbody className="font-sans font-medium text-sm">
                    {data ? (
                      <tr key={data?._id} className="border-b text-center">
                        <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%]  whitespace-normal break-all overflow-hidden">
                          {data?.model || "N/A"}
                        </td>
                        <td className="py-3  min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                          {data?.registrationNumber || "N/A"}
                        </td>
                        <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                          {data?.LocalAuthority || "N/A"}
                        </td>
                        <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                          {data?.engineType || "N/A"}
                        </td>
                        <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                          {data?.drivetrain || "N/A"}
                        </td>
                        <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                          {data?.isActive ? "Active" : "Inactive"}
                        </td>
                        <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                          <button
                            onClick={generatePdf}
                            className="text-lg"
                          >
                           <FaEye />
                          </button>
                        </td>
                      </tr>
                    ) : (
                      <tr>
                        <td colSpan="6" className="text-center py-4">
                          No data available
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>


            </div>

            {showPdfModal && pdfUrl && (
              <div className="fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex justify-center items-center z-50">
                <div className="relative bg-white rounded-md shadow-lg w-4/5 h-4/5">
                  <button
                    onClick={closeModal}
                    className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
                  >
                    <FaTimes />
                  </button>
                  <iframe
                    src={pdfUrl}
                    className="w-full h-full"
                    title="Vehicle Report"
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Page;
