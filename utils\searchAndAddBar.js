'use client'
import { useState, useEffect, useRef } from "react";
import Image from "next/image";
import {Count, MaxItems} from './count';

const SearchAndAddBar = ({
  data = [],
  onAddClick,
  addLabel = "Add",
  onFilterChange,
  onItemsPerPageChange, 
  itemperpage,   
  showDropdown = true,
  showSearch = true,
  showAddButton = true,
  searchPlaceholder = "Search...", 
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const onFilterChangeRef = useRef(onFilterChange);
  useEffect(() => {
    onFilterChangeRef.current = onFilterChange;
  }, [onFilterChange]);

  useEffect(() => {
    const filtered = data.filter((item) => {
      const searchLower = searchTerm.toLowerCase();
      
      if (item?.email && item.email.toLowerCase().includes(searchLower)) {
        return true;
      }
      
      if (item?.name && item.name.toLowerCase().includes(searchLower)) {
        return true;
      }
      
      if (item?.makemodel && item.makemodel.toLowerCase().includes(searchLower)) {
        return true;
      }
      
      if (item?.First_Name || item?.lastName) {
        const fullName = `${item?.First_Name || ''} ${item?.lastName || ''}`.toLowerCase();
        if (fullName.includes(searchLower)) {
          return true;
        }
      }
      
      if (item?.manufacturer && item.manufacturer.toLowerCase().includes(searchLower)) {
        return true;
      }
      
      return false;
    });
    if (onFilterChangeRef.current) {
      onFilterChangeRef.current(filtered);
    }
  }, [searchTerm, data]);

  return (
    <div className="flex justify-between w-full py-2 px-2">
      <div className="flex flex-wrap justify-between flex-col sm:flex-row sm:items-center gap-3 w-full">
        <div className="flex justify-between gap-7 items-center">
          {showDropdown && (
            <div className="md:flex gap-3 hidden items-center">
              <div className="font-sans font-medium text-sm">Show</div>
              <div>
                <select
                  value={itemperpage}
                  onChange={(e) => {
                    const newValue = Number(e.target.value);
                    if (onItemsPerPageChange) {
                      onItemsPerPageChange(newValue);
                    }
                  }}
                  className="rounded-lg w-16 px-1 h-8 bg-[#E0E0E0] focus:outline-none"
                >
                  <option disabled value={0}>Select</option>
                  {Array.from({ length: MaxItems }, (_, i) => (i + 1) * Count).map((number) => (
                    <option key={number} value={number}>
                      {number}
                    </option>
                  ))}
                </select>
              </div>
              <div className="font-sans font-medium text-sm">Entries</div>
            </div>
          )}
          {showSearch && (
            <div className="flex justify-center">
              <div className="relative">
                <Image width={15} height={15} src="/search.svg" className="absolute left-3 top-2" alt="search" />
                <input
                  type="text"
                  placeholder={searchPlaceholder}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="border rounded-lg pl-10 sm:px-10 py-1 border-[#9E9E9E] text-[#9E9E9E]  focus:outline-none focus:ring focus:ring-indigo-200"
                />
              </div>
            </div>
          )}
        </div>
        {showAddButton && (
          <button
            onClick={onAddClick}
            className="w-auto font-sans font-bold text-xs bg-[#313342] text-white rounded-lg hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 px-3 flex py-[10px] gap-2"
          >
            <Image width={15} height={15} src="/plus.svg" alt={addLabel} className="" />
            {addLabel}
          </button>
        )}
      </div>
    </div>
  );
};

export default SearchAndAddBar;