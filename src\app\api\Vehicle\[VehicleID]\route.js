import { connect } from "@config/db.js";
import Vehicle from "@models/Vehicle/Vehicle.Model.js";
import DriverVehicleAllotment from "@models/DriverVehicleAllotment/DriverVehicleAllotment.Model.js";
import DriverMoreInfo from "@models/DriverMoreInfo/DriverMoreInfo.model.js";
import VehicleMOT from "@models/VehicleMOT/VehicleMOT.Model.js";
import VehicleRoadTax from "@models/VehicleRoadTax/VehicleRoadTax.Model.js";
import VehicleService from "@models/VehicleService/VehicleService.Model.js";
import { NextResponse } from "next/server";
import cloudinary from "@middlewares/cloudinary.js";
import { uploadImage } from "services/uploadImage.js"; // Import the reusable upload function
import fs from "fs";
import path from "path";

const UPLOAD_DIR = path.join(process.cwd(), "public", "uploads");

export async function PUT(request, context) {
  try {
    await connect(); // Connect to the database
    if (!fs.existsSync(UPLOAD_DIR)) {
      fs.mkdirSync(UPLOAD_DIR, { recursive: true });
    }
    const id = context.params.VehicleID;
    const formDataObject = await request.formData(); // Ensure to await formData
    const imageFiles = formDataObject.getAll("imageFiles[]"); // Get all image files
    const safetyFeatures = formDataObject.getAll("safetyFeatures[]"); // Get all safety features
    const techFeatures = formDataObject.getAll("techFeatures[]"); // Get all tech features
    const damage_image = formDataObject.getAll("damage_image[]"); // Get all files
    const cardocument = formDataObject.getAll("cardocuments[]"); // Get all files
    const pdfofpolicy = formDataObject.get("PDFofPolicy[]"); // Get all files
    const parts = []; // To store Cloudinary URLs and IDs
    const partMap = {}; // group parts by index: e.g., partMap[0] = { partNumber: ..., ... }

    // Group parts by index
    for (let [key, value] of formDataObject.entries()) {
      const match = key.match(/^parts\[(\d+)\]\[(\w+)\]$/);
      if (match) {
        const index = match[1];
        const field = match[2];

        if (!partMap[index]) {
          partMap[index] = {};
        }

        partMap[index][field] = value;
      }
    }

    // Convert grouped map to array
    for (let index in partMap) {
      parts.push({
        partNumber: partMap[index].partNumber || "",
        partName: partMap[index].partName || "",
        partprice: partMap[index].partprice || "",
        partsupplier: partMap[index].partsupplier || ""
      });
    }

    const images = []; // To store Cloudinary URLs and IDs
    const damageImage = [];
    const cardocuments = [];

    let PDFofPolicyUrl = "";
    let PDFofPolicyPublicId = "";

    const vehicle = await Vehicle.findById(id); // Fetch vehicle by ID directly
    if (!vehicle) {
      return NextResponse.json({ error: "Vehicle not found", status: 404 });
    }

    //ok
    //  1: Handle PDF upload to Cloudinary
    if (!pdfofpolicy) {
      // Keep existing PDF values
      PDFofPolicyUrl = vehicle.PDFofPolicyUrl;
      PDFofPolicyPublicId = vehicle.PDFofPolicyPublicId;
    } else {
      const { imageFile, imagePublicId } = await uploadImage(pdfofpolicy);

      PDFofPolicyUrl = imageFile;
      PDFofPolicyPublicId = imagePublicId;
    }// pdf upload till here

    
    //ok
    //  2: Handle 10 image of vehicle uploads to Cloudinary
    for (const file of imageFiles) {
      if (file.size > 0) {
        try {
          const { imageFile, imagePublicId } = await uploadImage(file);

          images.push({
            url: imageFile,
            publicId: imagePublicId,
          });

          console.log("Image added to list (vehicle images):", {
            url: imageFile,
            publicId: imagePublicId,
          });
        } catch (err) {
          console.error("Error uploading vehicle image:", err.message);
        }
      }
    }

    if (images.length > 0) {
      console.log("Images present, starting update process", vehicle.images);

      // Optionally: Delete old images from Cloudinary
      for (let i = 0; i < vehicle.images.length; i++) {
        if (vehicle.images[i].publicId) {
          console.log(
            "Old image publicId to delete (vehicle images):",
            vehicle.images[i].publicId
          );

          try {
            const deleteResponse = await cloudinary.uploader.destroy(
              vehicle.images[i].publicId
            );
            console.log("Delete response (vehicle images):", deleteResponse);

            console.log(
              "Old image deleted from Cloudinary (vehicle images):",
              vehicle.images[i].publicId
            );
          } catch (err) {
            console.error(
              "Error deleting old vehicle image from Cloudinary:",
              err.message
            );
          }
        }
      }

      // Update the vehicle with new images
      vehicle.images = images;
      console.log("Vehicle updated with new images:", vehicle.images);
    }// vehicle 10 images till here


    //ok
    //  3: for damage_image variables
    for (const file of damage_image) {
      console.log("Processing file line 204:", file.name); // Log the name of each file

      if (file.size > 0) {
        try {
          const { imageFile, imagePublicId } = await uploadImage(file);

          console.log("Successfully uploaded Damage File:", imageFile);

          damageImage.push({
            url: imageFile,
            publicId: imagePublicId,
          });

          console.log("Image added to list line 229:", {
            url: imageFile,
            publicId: imagePublicId,
          });
        } catch (err) {
          console.error("Error uploading damage image:", err.message);
        }
      }
    }

    if (damageImage.length > 0) {
      console.log("Images present, starting update process", vehicle.damageImage);

      // Optionally: Delete old images from Cloudinary
      for (let i = 0; i < vehicle.damageImage.length; i++) {
        if (vehicle.damageImage[i].publicId) {
          console.log(
            "Old image publicId to delete: line 243",
            vehicle.damageImage[i].publicId
          );

          try {
            const deleteResponse = await cloudinary.uploader.destroy(
              vehicle.damageImage[i].publicId
            );
            console.log("Delete response: line 256", deleteResponse);

            console.log(
              "Old image deleted from Cloudinary: line 246",
              vehicle.damageImage[i].publicId
            );
          } catch (err) {
            console.error(
              "Error deleting old image from Cloudinary:",
              err.message
            );
          }
        }
      }

      // Update the vehicle with new images
      vehicle.damageImage = damageImage; // Directly assign the new images array
      console.log(
        "Vehicle updated with new images: line 255",
        vehicle.damageImage
      );
    }// damage image till here



    // ok
    //  4: for car documents variables
    for (const file of cardocument) {
      console.log("Processing file line 204:", file.name); // Log the name of each file

      if (file.size > 0) {
        try {
          const { imageFile, imagePublicId } = await uploadImage(file);

          console.log("Successfully uploaded CarDocument:", imageFile);

          cardocuments.push({
            url: imageFile,
            publicId: imagePublicId,
          });

          console.log("Image added to list line 229:", {
            url: imageFile,
            publicId: imagePublicId,
          });
        } catch (err) {
          console.error("Error uploading CarDocument:", err.message);
        }
      }
    }

    if (cardocuments.length > 0) {
      console.log(
        "Images present, starting update process",
        vehicle.cardocuments
      );

      // Optionally: Delete old images from Cloudinary
      for (let i = 0; i < vehicle.cardocuments.length; i++) {
        if (vehicle.cardocuments[i].publicId) {
          console.log(
            "Old image publicId to delete CarDocument",
            vehicle.cardocuments[i].publicId
          );

          try {
            const deleteResponse = await cloudinary.uploader.destroy(
              vehicle.cardocuments[i].publicId
            );
            console.log("Delete response CarDocuments", deleteResponse);

            console.log(
              "Old image deleted from Cloudinary CarDocuments:",
              vehicle.cardocuments[i].publicId
            );
          } catch (err) {
            console.error(
              "Error deleting old CarDocument from Cloudinary:",
              err.message
            );
          }
        }
      }

      // Update the vehicle with new images
      vehicle.cardocuments = cardocuments;
      console.log(
        "Vehicle updated with new CarDocuments:",
        vehicle.cardocuments
      );
    }// car documents till here


    // Update vehicle properties
    vehicle.PDFofPolicyUrl = PDFofPolicyUrl || vehicle.PDFofPolicyUrl;
    vehicle.PDFofPolicyPublicId =
      PDFofPolicyPublicId || vehicle.PDFofPolicyPublicId;
    vehicle.manufacturer =
      formDataObject.get("manufacturer") || vehicle.manufacturer;
    vehicle.model = formDataObject.get("model") || vehicle.model;
    vehicle.year = formDataObject.get("year") || vehicle.year;
    vehicle.vehicleStatus =
      formDataObject.get("vehicleStatus") || vehicle.vehicleStatus;
    vehicle.type = formDataObject.get("type") || vehicle.type;
    vehicle.engineType = formDataObject.get("engineType") || vehicle.engineType;
    vehicle.fuelType = formDataObject.get("fuelType") || vehicle.fuelType;
    vehicle.transmission =
      formDataObject.get("transmission") || vehicle.transmission;
    vehicle.drivetrain = formDataObject.get("drivetrain") || vehicle.drivetrain;
    vehicle.exteriorColor =
      formDataObject.get("exteriorColor") || vehicle.exteriorColor;
    vehicle.interiorColor =
      formDataObject.get("interiorColor") || vehicle.interiorColor;
    vehicle.height = formDataObject.get("height") || vehicle.height;
    vehicle.width = formDataObject.get("width") || vehicle.width;
    vehicle.length = formDataObject.get("length") || vehicle.length;
    vehicle.passengerCapacity =
      formDataObject.get("passengerCapacity") || vehicle.passengerCapacity;
    vehicle.cargoCapacity =
      formDataObject.get("cargoCapacity") || vehicle.cargoCapacity;
    vehicle.horsepower = formDataObject.get("horsepower") || vehicle.horsepower;
    vehicle.torque = formDataObject.get("torque") || vehicle.torque;
    vehicle.topSpeed = formDataObject.get("topSpeed") || vehicle.topSpeed;
    vehicle.towingCapacity =
      formDataObject.get("towingCapacity") || vehicle.towingCapacity;
    vehicle.fuelEfficiency =
      formDataObject.get("fuelEfficiency") || vehicle.fuelEfficiency;
    vehicle.safetyFeatures =
      safetyFeatures.length > 0 ? [...safetyFeatures] : vehicle.safetyFeatures;
    vehicle.parts =
      parts.length > 0 ? [...parts] : vehicle.parts;
    vehicle.techFeatures =
      techFeatures.length > 0 ? [...techFeatures] : vehicle.techFeatures;
    vehicle.price = formDataObject.get("price") || vehicle.price;
    vehicle.registrationNumber =
      formDataObject.get("registrationNumber") || vehicle.registrationNumber;
    vehicle.warrantyInfo =
      formDataObject.get("warrantyInfo") || vehicle.warrantyInfo;
    vehicle.isActive =
      formDataObject.get("isActive") || vehicle.isActive;
    vehicle.adminCreatedBy =
      formDataObject.get("adminCreatedBy") || vehicle.adminCreatedBy;
    vehicle.adminCompanyName =
      formDataObject.get("adminCompanyName") || vehicle.adminCompanyName;
    // new fields
    vehicle.enginesize = formDataObject.get("enginesize") || vehicle.enginesize;
    vehicle.chasisnumber =
      formDataObject.get("chasisnumber") || vehicle.chasisnumber;
    vehicle.vehicleSite =
      formDataObject.get("vehicleSite") || vehicle.vehicleSite;
    vehicle.fleetEntryDate =
      formDataObject.get("fleetEntryDate") || vehicle.fleetEntryDate;
    vehicle.milesOnFleetEntry =
      formDataObject.get("milesOnFleetEntry") || vehicle.milesOnFleetEntry;
    vehicle.plannedFleetExit =
      formDataObject.get("plannedFleetExit") || vehicle.plannedFleetExit;
    vehicle.milesOnFleetExit =
      formDataObject.get("milesOnFleetExit") || vehicle.milesOnFleetExit;
    vehicle.actualExitDate =
      formDataObject.get("actualExitDate") || vehicle.actualExitDate;
    vehicle.milesAtActualExit =
      formDataObject.get("milesAtActualExit") || vehicle.milesAtActualExit;
    vehicle.doors = formDataObject.get("doors") || vehicle.doors;
    vehicle.color = formDataObject.get("color") || vehicle.color;
    vehicle.editablecolor =
      formDataObject.get("editablecolor") || vehicle.editablecolor;
    vehicle.roadTaxDate =
      formDataObject.get("roadTaxDate") || vehicle.roadTaxDate;
    vehicle.roadTaxCycle =
      formDataObject.get("roadTaxCycle") || vehicle.roadTaxCycle;
    vehicle.motDueDate = formDataObject.get("motDueDate") || vehicle.motDueDate;
    vehicle.motCycle = formDataObject.get("motCycle") || vehicle.motCycle;
    vehicle.seats = formDataObject.get("seats") || vehicle.seats;
    vehicle.abiCode = formDataObject.get("abiCode") || vehicle.abiCode;
    vehicle.nextServiceDate =
      formDataObject.get("nextServiceDate") || vehicle.nextServiceDate;
    vehicle.nextServiceMiles =
      formDataObject.get("nextServiceMiles") || vehicle.nextServiceMiles;
    vehicle.roadTaxCost =
      formDataObject.get("roadTaxCost") || vehicle.roadTaxCost;
    vehicle.listPrice = formDataObject.get("listPrice") || vehicle.listPrice;
    vehicle.purchasePrice =
      formDataObject.get("purchasePrice") || vehicle.purchasePrice;
    vehicle.insuranceValue =
      formDataObject.get("insuranceValue") || vehicle.insuranceValue;
    vehicle.departmentCode =
      formDataObject.get("departmentCode") || vehicle.departmentCode;
    vehicle.departmentCode =
      formDataObject.get("departmentCode") || vehicle.departmentCode;
    vehicle.maintenance =
      formDataObject.get("maintenance") !== undefined
        ? formDataObject.get("maintenance") === "true"
        : vehicle.maintenance;

    vehicle.issues_damage =
      formDataObject.get("issues_damage") || vehicle.issues_damage;
    vehicle.recovery = formDataObject.get("recovery") || vehicle.recovery;
    vehicle.organization =
      formDataObject.get("organization") || vehicle.organization;
    vehicle.repairStatus =
      formDataObject.get("repairStatus") || vehicle.repairStatus;
    vehicle.jobNumber = formDataObject.get("jobNumber") || vehicle.jobNumber;
    vehicle.memo = formDataObject.get("memo") || vehicle.memo;
    vehicle.partNumber = formDataObject.get("partNumber") || vehicle.partNumber;
    vehicle.partName = formDataObject.get("partName") || vehicle.partName;
    vehicle.partprice = formDataObject.get("partprice") || vehicle.partprice;
    vehicle.partsupplier =
      formDataObject.get("partsupplier") || vehicle.partsupplier;
    vehicle.TestDate = formDataObject.get("TestDate") || vehicle.TestDate;
    vehicle.PlateExpiryDate =
      formDataObject.get("PlateExpiryDate") || vehicle.PlateExpiryDate;
    vehicle.Insurance = formDataObject.get("Insurance") || vehicle.Insurance;
    vehicle.insurancePolicyNumber =
      formDataObject.get("insurancePolicyNumber") ||
      vehicle.insurancePolicyNumber;
    vehicle.defect = formDataObject.get("defect") || vehicle.defect;
    vehicle.Defectdate = formDataObject.get("Defectdate") || vehicle.Defectdate;
    vehicle.defectstatus =
      formDataObject.get("defectstatus") || vehicle.defectstatus;
    vehicle.defectdescription =
      formDataObject.get("defectdescription") || vehicle.defectdescription;
    vehicle.defectaction =
      formDataObject.get("defectaction") || vehicle.defectaction;
    vehicle.additionalInfo =
      formDataObject.get("additionalInfo") || vehicle.additionalInfo;
    vehicle.RPCExpiryDate =
      formDataObject.get("RPCExpiryDate") || vehicle.RPCExpiryDate;
    vehicle.TailLiftExpiryDate =
      formDataObject.get("TailLiftExpiryDate") || vehicle.TailLiftExpiryDate;
    vehicle.forkLiftNumber =
      formDataObject.get("forkLiftNumber") || vehicle.forkLiftNumber;
    vehicle.ForkLiftInspectionDate =
      formDataObject.get("ForkLiftInspectionDate") ||
      vehicle.ForkLiftInspectionDate;
    // new field
    vehicle.ForkLiftInspectionNumberNotes =
      formDataObject.get("ForkLiftInspectionNumberNotes") ||
      vehicle.ForkLiftInspectionNumberNotes;

    vehicle.dateOfLastV5CIssued =
      formDataObject.get("dateOfLastV5CIssued") ||
      vehicle.dateOfLastV5CIssued;

    vehicle.co2Emissions =
      formDataObject.get("co2Emissions") ||
      vehicle.co2Emissions;

    vehicle.taxStatus =
      formDataObject.get("taxStatus") ||
      vehicle.taxStatus;

    vehicle.motStatus =
      formDataObject.get("motStatus") ||
      vehicle.motStatus;

    vehicle.markedForExport =
      formDataObject.get("markedForExport") ||
      vehicle.markedForExport;

    vehicle.typeApproval =
      formDataObject.get("typeApproval") ||
      vehicle.typeApproval;

    vehicle.wheelplan =
      formDataObject.get("wheelplan") ||
      vehicle.wheelplan;

    vehicle.monthOfFirstRegistration =
      formDataObject.get("monthOfFirstRegistration") ||
      vehicle.monthOfFirstRegistration;

    // Save the updated vehicle
    await vehicle.save();

    return NextResponse.json({
      message: "Vehicle details updated successfully",
      vehicle,
      status: 200,
    });
  } catch (error) {
    console.error("Error updating vehicle details:", error);
    return NextResponse.json({
      error: error.message || "Failed to update vehicle details",
      status: 500,
    });
  }
}

export async function GET(request, context) {
  try {
    // Connect to the database
    await connect();

    // Extract the product ID from the request parameters
    const id = context.params.VehicleID;

    // Attempt to find the user by ID
    const Find_User = await Vehicle.findById(id);

    // Check if the user was found
    if (Find_User) {
      // User found, return the user data
      return NextResponse.json({ result: Find_User, status: 200 });
    } else {
      // If no user found, try to find by _id in an array
      const find_user_all = await Vehicle.find({ _id: id });

      // Check if there are any records found
      if (find_user_all.length > 0) {
        // Return all records as a JSON response
        return NextResponse.json({ result: find_user_all, status: 200 });
      }

      // No records found
      return NextResponse.json({ result: "No User Found", status: 404 });
    }
  } catch (error) {
    console.error("Error retrieving product:", error);
    // Return an error response
    return NextResponse.json({ message: "Internal Server Error", status: 500 });
  }
}
// DELETE handler for deleting a vehicle and associated image
export const DELETE = async (request, { params }) => {
  try {
    // Connect to the database
    await connect();

    const { VehicleID } = params; // Access the VehicleID from params

    // Find and delete the vehicle by its ID
    const deletedVehicle = await Vehicle.findById({ _id: VehicleID });
    if (!deletedVehicle) {
      return NextResponse.json({
        error: "Vehicle not found",
        status: 404,
      });
    }

    const deleted = await Vehicle.findByIdAndDelete({ _id: VehicleID });
    let VehicleAllotment = await DriverVehicleAllotment.deleteMany({
      vehicleId: deletedVehicle._id,
    });
    let alldelete = await DriverMoreInfo.deleteMany({
      vehicleId: deletedVehicle._id,
    });
    let allmot = await VehicleMOT.deleteMany({
      VehicleId: deletedVehicle._id,
    });
    let allroadtex = await VehicleRoadTax.deleteMany({
      VehicleId: deletedVehicle._id,
    });
    let allservice = await VehicleService.deleteMany({
      VehicleId: deletedVehicle._id,
    });

    console.log(
      deleted,
      VehicleAllotment,
      alldelete,
      allmot,
      allroadtex,
      allservice
    );

    // Get the image public ID from the deleted vehicle object
    const imagesPublicIdd = deletedVehicle.images.publicId;
    const damageImagePublicIddd = deletedVehicle.damageImage.publicId;
    const PDFofPolicyPublicId = deletedVehicle.PDFofPolicyPublicId;
    const cardocumentPublicId = deletedVehicle.cardocuments;
    console.log("imagesPublicIdd Public ID:", imagesPublicIdd);
    console.log("damageImagePublicIddd Public ID:", damageImagePublicIddd);
    console.log("PDFofPolicyPublicId Public ID:", PDFofPolicyPublicId);
    console.log("cardocuments Public ID:", cardocumentPublicId);

    // If the vehicle has an associated image, delete it from Cloudinary
    if (
      imagesPublicIdd &&
      damageImagePublicIddd &&
      PDFofPolicyPublicId &&
      cardocumentPublicId
    ) {
      try {
        const cloudinaryResponse1 = await cloudinary.uploader.destroy(
          imagesPublicIdd
        );
        const cloudinaryResponse2 = await cloudinary.uploader.destroy(
          damageImagePublicIddd
        );
        const cloudinaryResponse3 = await cloudinary.uploader.destroy(
          PDFofPolicyPublicId
        );
        const cloudinaryResponse4 = await cloudinary.uploader.destroy(
          cardocumentPublicId
        );

        console.log(`Cloudinary response: ${cloudinaryResponse1.result}`);
        console.log(`Cloudinary response: ${cloudinaryResponse2.result}`);
        console.log(`Cloudinary response: ${cloudinaryResponse3.result}`);
        console.log(`Cloudinary response: ${cloudinaryResponse4.result}`);
      } catch (error) {
        console.error("Failed to delete image from Cloudinary:", error);
      }
    }

    // Return success response
    return NextResponse.json({
      message: "Vehicle deleted successfully",
      success: true,
      status: 200,
    });
  } catch (error) {
    console.error("Error deleting vehicle:", error);
    return NextResponse.json({
      error: "An error occurred while deleting the vehicle",
      status: 500,
    });
  }
};
