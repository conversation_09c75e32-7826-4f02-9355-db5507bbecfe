"use client";

import React, { useState, useEffect } from "react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Header from "../../../Components/Header";
import Sidebar from "../../../Components/Sidebar";
import {
  API_URL_DriverMoreInfo,
  API_URL_DRIVERTOTAL
} from "../../../Components/ApiUrl/ApiUrls";
import { getCompanyName } from "@/utils/storageUtils";
import axios from "axios";
import { isAuthenticated } from "@/utils/verifytoken";
import { useRouter } from "next/navigation";
import DeleteModal from "@/app/Dashboard/Components/DeleteModal";
import BackButton from "@/app/Dashboard/Components/BackButton";

import Addmakeapayment from "@/app/Dashboard/Driver/Addmakeapayment/Addmakeapayment"
import AddCost from "@/app/Dashboard/Driver/AddCost/AddCost"

const Page = ({ params }) => {
  const router = useRouter();
  const id = params?.id;
  const [isMounted, setIsMounted] = useState(false);
  const [data, setData] = useState([]);
  const [totalAmount, settotalamount] = useState("");
  const [selectedCompanyName, setSelectedCompanyName] = useState("");
  const [selectedUserId, setSelectedUserId] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemperpage, setitemperpage] = useState(15);
  const [isOpenPayment, setIsOpenPayment] = useState(false);
  const [isOpenAddCost, setIsOpenAddCost] = useState(false);


  const OpenPaymentModle = () => {
    setSelectedUserId(id);
    setIsOpenPayment(!isOpenPayment);
  };
  const OpenAddCostModle = () => {
    setSelectedUserId(id);
    setIsOpenAddCost(!isOpenAddCost);
  };


  useEffect(() => {
    if (!isAuthenticated()) {
      router.push("/");
      return;
    }
  }, [router]);
  useEffect(() => {
    setIsMounted(true);
    const companyNameFromStorage = getCompanyName();
    if (companyNameFromStorage) {
      setSelectedCompanyName(companyNameFromStorage);
    }
  }, []);

  const fetchData = async () => {
    try {
      const response = await axios.get(`${API_URL_DriverMoreInfo}/${id}`);
      const driverId = response?.data?.result[0]?.driverId;
      const drivertotal = await axios.get(`${API_URL_DRIVERTOTAL}/${id}`, {
        params: {
          driverId,
        },
      });
      const { data } = response;

      settotalamount(drivertotal?.data)
      setData(data?.result);
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Failed to fetch data");
    }
  };


  const handleDelete = async (id) => {
    try {
      const response = await axios.delete(`${API_URL_DriverMoreInfo}/${id}`);
      const { success, message } = response?.data;


      if (success) {
        toast.success(message);
        fetchData();
      } else {
        toast.warn(message);
      }
    } catch (error) {
      console.error("Error deleting title:", error);
      toast.error("Failed to delete title");
    }
  };


  useEffect(() => {
    fetchData();
    const interval = setInterval(() => {
      console.log("call again total")
      fetchData();
    }, 1 * 60 * 1000); 
  
    return () => clearInterval(interval);
  }, []);

  if (!isMounted) return null;

  function formatDate(dateString) {
    const dateObject = new Date(dateString);
    return `${(dateObject.getMonth() + 1)
      .toString()
      .padStart(2, "0")}/${dateObject
        .getDate()
        .toString()
        .padStart(2, "0")}/${dateObject.getFullYear()}`;
  }


  const totalPages = Math.ceil(data.length / itemperpage);
  const currentData = data.slice(
    (currentPage - 1) * itemperpage,
    currentPage * itemperpage
  );

  return (
    <div className="h-[100vh] overflow-hidden">
      <Header className="min-w-full" />
      <div className="flex gap-4">
        <Sidebar />
        <div
          className="w-[80%] xl:w-[85%] h-screen flex flex-col justify-start overflow-y-auto pr-4"
          style={{
            height: "calc(100vh - 90px)",
          }}
        >
          <h1 className="text-[#313342] font-medium text-2xl py-5 underline decoration-[#AEADEB] underline-offset-8">
            Driver Info
          </h1>
          <div className="py-5">
            <div className="drop-shadow-custom4">
              <div className="flex justify-between w-full py-2 px-2">
                <div className="flex flex-wrap justify-between flex-col sm:flex-row sm:items-center gap-3 w-full">
                  <div className="w-full flex justify-between flex-wrap gap-4">
                    <div className=" flex gap-7 items-center">
                      <div className="md:flex gap-3 hidden items-center">
                        <div className="font-sans font-medium text-sm">Show</div>
                       
                        <div>
                          <select
                            value={itemperpage}
                            onChange={(e) => {
                              setitemperpage(Number(e.target?.value));
                              setCurrentPage(1);
                            }}
                            className="rounded-lg w-16 px-1 h-8 bg-[#E0E0E0] focus:outline-none"
                          >
                            <option disabled value={0}>Select</option>
                            {Array.from({ length: 6 }, (_, i) => (i + 1) * 5).map((number) => (
                              <option key={number} value={number}>
                                {number}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>

                    </div>
                    <div className="flex gap-2">
                      <button
                        onClick={OpenAddCostModle}
                        className="w-[156px] md:w-auto font-sans font-bold text-xs bg-[#313342] text-white rounded-lg hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 px-3 flex py-[10px] gap-2"
                      >
                        Cost/Debit
                      </button>
                      <button
                        onClick={OpenPaymentModle}
                        className="w-[156px] md:w-auto font-sans font-bold text-xs bg-[#313342] text-white rounded-lg hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 px-3 flex py-[10px] gap-2"
                      >
                        make a payment/Credit
                      </button>
                      <BackButton />
                    </div>
                  </div>
                </div>
              </div>


              <div className="overflow-x-auto custom-scrollbar">
                <table className="w-full border table-auto">
                  <thead className="font-sans font-bold text-sm text-center bg-[#38384A]">
                    <tr className="text-white ">
                      <th className="py-3 px-4 min-w-[150px] text-white bg-custom-bg w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                        Driver Name
                      </th>
                      <th className="py-3 px-4 min-w-[150px] text-white bg-custom-bg w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                        Payment Cycle
                      </th>
                      <th className="py-3 px-4 min-w-[150px] text-white  bg-custom-bg w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                        Description
                      </th>
                      <th className="py-3 px-4 min-w-[150px] text-white  bg-custom-bg w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                        Dates
                      </th>
                      <th className="py-3 px-4 min-w-[150px] text-white  bg-custom-bg w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                        Debit
                      </th>

                      <th className="py-3 px-4 min-w-[150px] text-white  bg-custom-bg w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                        Credit
                      </th>

                    </tr>
                  </thead>

                  <tbody className="font-sans font-medium text-sm">
                    {currentData.length > 0 ? (
                      [...currentData]
                        .filter(
                          (row) =>
                            row.adminCompanyName &&
                            row.adminCompanyName.toLowerCase() === selectedCompanyName.toLowerCase()
                        )
                        .filter((row) => row.startDate)
                        .sort((a, b) => new Date(a.startDate) - new Date(b.startDate)) 
                        .map((row) => (
                          <tr key={row._id} className="border-b text-center">
                            <td className="py-3 px-4 whitespace-normal break-all overflow-hidden">
                              {row.driverName}
                            </td>

                            <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                              {row.paymentcycle === "perday" ? "Per Day" : row.paymentcycle === "perweek" ? "Per Week" : row.paymentcycle}
                            </td>


                            <td className="py-3 px-4 whitespace-normal break-all overflow-hidden">
                              {row?.description}
                            </td>

                            <td className="py-3 px-4 whitespace-normal break-all overflow-hidden">
                              {formatDate(row?.startDate)}
                            </td>

                            <td className="py-3 px-4 whitespace-normal break-all overflow-hidden">
                              {row.payment ? `£ ${row?.payment}` : null}
                            </td>

                            <td className="py-3 px-4 whitespace-normal break-all overflow-hidden">
                              {row.pay ? `£ ${row?.pay}` : null}
                            </td>
                          </tr>
                        ))
                    ) : (
                      <tr>
                        <td colSpan="8" className="py-3 px-4 text-center text-gray-500">
                          No data available
                        </td>
                      </tr>
                    )}

                    {totalAmount && (
                      <tr className="font-bold text-center">
                        <td className="py-3 px-4">Total</td>
                        <td className="py-3 px-4"></td>
                        <td className="py-3 px-4"></td>
                        <td className="py-3 px-4">£ {totalAmount?.totalPayment}</td>
                        <td className="py-3 px-4">£ {totalAmount?.totalPay}</td>
                        <td className="py-3 px-4">{`Remain  £ ${totalAmount?.remainingAmount}`}</td>
                      </tr>
                    )}
                  </tbody>


                </table>
              </div>

              <div className="flex justify-center py-5 font-montserrat font-medium text-[12px]">
                <nav>
                  <ul className="flex items-center gap-3">
                    <li>
                      <button
                        onClick={() =>
                          setCurrentPage((prev) => Math.max(prev - 1, 1))
                        }
                        disabled={currentPage === 1}
                        className={`h-8 px-2 border rounded-lg ${currentPage === 1
                          ? "opacity-50 cursor-not-allowed"
                          : "bg-white"
                          }`}
                      >
                        Previous
                      </button>
                    </li>

                    {totalPages > 1 && (
                      <>
                        {totalPages <= 3 ? (
                          Array.from(
                            { length: totalPages },
                            (_, index) => index + 1
                          ).map((page) => (
                            <li key={page}>
                              <button
                                onClick={() => setCurrentPage(page)}
                                className={`h-8 w-8 border rounded-lg ${currentPage === page
                                  ? "bg-custom-bg text-white"
                                  : "bg-white"
                                  }`}
                              >
                                {page}
                              </button>
                            </li>
                          ))
                        ) : (
                          <>
                            {currentPage === 1 && (
                              <>
                                <li>
                                  <button className="h-8 w-8 border rounded-lg bg-custom-bg text-white">
                                    1
                                  </button>
                                </li>
                                <li>
                                  <span className="px-2">...</span>
                                </li>
                                <li>
                                  <button
                                    onClick={() => setCurrentPage(totalPages)}
                                    className="h-8 w-8 border rounded-lg bg-white"
                                  >
                                    {totalPages}
                                  </button>
                                </li>
                              </>
                            )}
                            {currentPage > 1 && currentPage < totalPages && (
                              <>
                                <li>
                                  <button className="h-8 w-8 border rounded-lg bg-custom-bg text-white">
                                    {currentPage}
                                  </button>
                                </li>
                                <li>
                                  <span className="px-2">...</span>
                                </li>
                                <li>
                                  <button
                                    onClick={() => setCurrentPage(totalPages)}
                                    className="h-8 w-8 border rounded-lg bg-white"
                                  >
                                    {totalPages}
                                  </button>
                                </li>
                              </>
                            )}
                            {currentPage === totalPages && (
                              <li>
                                <button className="h-8 w-8 border rounded-lg bg-custom-bg text-white">
                                  {totalPages}
                                </button>
                              </li>
                            )}
                          </>
                        )}
                      </>
                    )}

                    <li>
                      <button
                        onClick={() =>
                          setCurrentPage((prev) =>
                            Math.min(prev + 1, totalPages)
                          )
                        }
                        disabled={currentPage === totalPages}
                        className={`h-8 px-2 border rounded-lg ${currentPage === totalPages
                          ? "opacity-50 cursor-not-allowed"
                          : "bg-white"
                          }`}
                      >
                        Next
                      </button>
                    </li>
                  </ul>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Addmakeapayment
        isOpen={isOpenPayment}
        onClose={OpenPaymentModle}
        fetchData={fetchData}
        Id={selectedUserId}
      />
      <AddCost
        isOpen={isOpenAddCost}
        onClose={OpenAddCostModle}
        fetchData={fetchData}
        Id={selectedUserId}
      />
      <DeleteModal
        onDelete={handleDelete}
      />
    </div>
  );
};

export default Page;
