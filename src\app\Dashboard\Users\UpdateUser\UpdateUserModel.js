"use client";
import React, { useEffect, useState } from "react";
import axios from "axios";
import { API_URL_USER } from "../../Components/ApiUrl/ApiUrls";
import { toast } from "react-toastify";
import { AiOutlineEye, AiOutlineEyeInvisible } from "react-icons/ai";
import {
  getCompanyName,
  getUserId,
  getUserName, getflag, getcompanyId
} from "@/utils/storageUtils";
import { useRef } from "react"
import Image from "next/image";

const UpdateUserModel = ({ isOpen, onClose, fetchData, userId }) => {
  const [step, setStep] = useState(1);
  const nextStep = () => setStep(step + 1);
  const prevStep = () => setStep(step - 1);
  const [showPasswords, setShowPasswords] = useState(false);
  const [formData, setFormData] = useState({
    title: "",
    firstName: "",
    lastName: "",
    email: "",
    tel1: "",
    tel2: 0,
    postcode: "",
    postalAddress: "",
    permanentAddress: "",
    city: "",
    county: "",
    accessLevel: "",
    dateOfBirth: "",
    position: "",
    reportsTo: "",
    username: "",
    password: "",
    passwordExpires: "",
    passwordExpiresEvery: "",
    confirmpassword: "",
    companyName: "",
    CreatedBy: "",
    useravatar: null,
    isActive: false,
    role: "user", 
    userId: "",
    companyId: "",
    Postcode: "",
    BuildingAndStreetOne: "",
    BuildingAndStreetTwo: "",
    Town_City: "",
    Country: "",
  });
  const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[^A-Za-z\d])[A-Za-z\d\S]{6,}$/;
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/;
  const [validation, setValidation] = useState({
    emailValid: false,
    passwordMatch: false,
    passwordValid: false,
    dateOfBirthValid: true,
    passwordExpiresvalid: false
  });

  const [imagePreview, setImagePreview] = useState(null);
  useEffect(() => {
    const storedcompanyName = (() => {
      const name1 = getCompanyName();
      if (name1) return name1;

      const name2 = getUserName();
      if (name2) return name2;

    })();
    const userId = getUserId()
    const flag = getflag();
    const compID = getcompanyId();
    if (storedcompanyName && userId) {
      if (storedcompanyName.toLowerCase() === "superadmin" && flag === "true" && compID) {
        setFormData((prevData) => ({
          ...prevData,
          companyName: storedcompanyName,
          companyId: compID,
        }));
      } else {
        setFormData((prevData) => ({
          ...prevData,
          companyName: storedcompanyName,
          companyId: userId,
        }));
      }
    } else {
      console.error("Missing required fields:", { storedcompanyName, userId, flag, compID });
    }
  }, []);
  const fetchUserData = async () => {
    try {
      const res = await axios.get(`${API_URL_USER}/${userId}`);
      const adminData = res?.data?.result;

      setFormData({
        title: adminData?.title,
        firstName: adminData?.firstName,
        lastName: adminData?.lastName,
        tel1: adminData?.tel1,
        tel2: adminData?.tel2,
        postcode: adminData?.postcode,
        postalAddress: adminData?.postalAddress,
        permanentAddress: adminData?.permanentAddress,
        city: adminData?.city,
        county: adminData?.county,
        accessLevel: adminData?.accessLevel,
        dateOfBirth: adminData?.dateOfBirth,
        position: adminData?.position,
        reportsTo: adminData?.reportsTo,
        passwordExpires: adminData?.passwordExpires,
        passwordExpiresEvery: adminData?.repopasswordExpiresEveryrtsTo,
        companyName: "",
        username: adminData?.username,
        email: adminData?.email,
        password: adminData?.confirmpassword,
        confirmpassword: adminData?.confirmpassword,
        useravatar: adminData?.useravatar,
        isActive: false,
        role: adminData?.role,
        Postcode: adminData?.Postcode,
        BuildingAndStreetOne: adminData?.BuildingAndStreetOne,
        BuildingAndStreetTwo: adminData?.BuildingAndStreetTwo,
        Town_City: adminData?.Town_City,
        Country: adminData?.Country,
      });

      setImagePreview(adminData?.useravatar);
    } catch (error) {
      console.error("Error fetching user data:", error);
    }
  };

  useEffect(() => {
    if (userId) {
      fetchUserData();
    }
  }, [userId]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    let updatedValue = type === 'checkbox' ? checked : value;

    if (name === 'username') {
      updatedValue = updatedValue.replace(/\s/g, '');
    }
    
    setFormData((prevData) => ({
      ...prevData,
      [name]: updatedValue,
    }));

    if (name === 'email') {
      setValidation((prev) => ({
        ...prev,
        emailValid: emailRegex.test(updatedValue),
      }));
    }

    if (name === 'password' || name === 'confirmpassword') {
      const password = name === 'password' ? updatedValue : formData?.password;
      const confirmPassword = name === 'confirmpassword' ? updatedValue : formData?.confirmpassword;

      setValidation((prev) => ({
        ...prev,
        passwordValid: passwordRegex.test(password),
        passwordMatch: password === confirmPassword,
      }));
    }

    if (name === 'dateOfBirth') {
      const selectedDate = new Date(value);
      const currentDate = new Date();
      const minAgeDate = new Date();
      minAgeDate.setFullYear(currentDate.getFullYear() - 18);

      const isValid = selectedDate <= minAgeDate;
      setValidation((prevValidation) => ({
        ...prevValidation,
        dateOfBirthValid: isValid,
      }));
    }


    if (name === 'passwordExpires') {
      const selectedDate = new Date(value);
      const currentDate = new Date();

      currentDate.setHours(0, 0, 0, 0);
      selectedDate.setHours(0, 0, 0, 0);

      const isValid = selectedDate >= currentDate;

      setValidation((prevValidation) => ({
        ...prevValidation,
        passwordExpiresvalid: isValid,
      }));

      if (!isValid) {
        setFormData((prevData) => ({
          ...prevData,
          passwordExpires: "",
        }));
      }
    }

  };

  const fileInputRef = useRef(null);
  const handleRemoveImage = () => {
    setImagePreview(null);
    setFormData((prevData) => ({
      ...prevData,
      useravatar: null,
    }));

    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    setFormData((prevData) => ({
      ...prevData,
      useravatar: file,
    }));
    setImagePreview(URL.createObjectURL(file));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const formDataToSend = new FormData();

    for (const key in formData) {
      formDataToSend.append(key, formData[key]);
    }

    if (formData.useravatar) {
      formDataToSend.append("useravatar", formData?.useravatar);
    }

    try {
      const response = await fetch(`${API_URL_USER}/${userId}`, {
        method: "PUT", 
        body: formDataToSend,
      });

      const data = await response.json();
      toast.success(data?.message);
      resetform();
      onClose();
      fetchData();
      setStep(1);
    } catch (error) {
      console.error("Error updating user:", error);
    }
  };

  useEffect(() => {
    setValidation((prev) => ({
      ...prev,
      emailValid: emailRegex.test(formData?.email),
      passwordValid: passwordRegex.test(formData?.password),
      passwordMatch: formData?.password === formData?.confirmpassword,
      passwordExpiresvalid: new Date(formData?.passwordExpires) >= new Date(new Date().setHours(0, 0, 0, 0)),
    }));
  }, [formData]);


  const resetform = () => {
    setStep(1);

  };
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50 z-50">
      <div className="bg-white px-12 py-7 rounded-xl shadow-lg w-full max-w-3xl overflow-y-auto max-h-screen">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">
            Update User
          </h2>

          <Image height={15} width={15} alt="crossIcon" src="/crossIcon.svg" className="cursor-pointer" onClick={() => {
            onClose();
            fetchUserData();
          }} />

        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {step === 1 && (
            <>
              <div>
                <div className="grid grid-cols-2 sm:grid-cols-2 gap-3">
                  <div>
                    <div className="flex gap-1 items-center justify-start">
                      <label
                        htmlFor="taxiFirm"
                        className="text-[10px]"
                      >
                        Title <span className="text-red-600">*</span>
                      </label>
                    </div>

                    <select
                      id="title"
                      name="title"
                      value={formData?.title}
                      onChange={handleChange}
                      className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow"
                      required
                    >
                      <option value="">Select Title</option>
                      <option value="Mr">Mr</option>
                      <option value="Miss">Miss</option>
                      <option value="Miss">Miss</option>
                      <option value="Mrs">Mrs</option>
                    </select>
                  </div>

                  <div>
                    <div className="flex gap-1 items-center justify-start">
                      <label
                        htmlFor="firstName"
                        className="text-[10px]"
                      >
                        First Name <span className="text-red-600">*</span>
                      </label>
                    </div>

                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      value={formData?.firstName}
                      onChange={handleChange}
                      className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow"
                      required
                      placeholder="First name"
                    />
                  </div>

                  <div>
                    <div className="flex gap-1 items-center justify-start">
                      <label
                        htmlFor="lastName"
                        className="text-[10px]"
                      >
                        Last Name <span className="text-red-600">*</span>
                      </label>
                    </div>

                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      value={formData?.lastName}
                      onChange={handleChange}
                      className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow"
                      required
                      placeholder="Last name"
                    />
                  </div>

                  <div>
                    <label htmlFor="email" className="text-[10px]">
                      Email <span className="text-red-600">*</span>
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData?.email}
                      onChange={handleChange}
                      className={`block w-full p-2 border rounded shadow focus:outline-none ${formData?.email
                        ? validation?.emailValid
                          ? "border-green-500"
                          : "border-red-500"
                        : "border-[#42506666]"
                        }`}
                      placeholder="Enter your email"
                      required
                    />
                    {formData.email && (
                      <p className={`text-[9px] ${validation?.emailValid ? "text-green-700" : "text-red-700"}`}>
                        {validation?.emailValid ? "Valid email format" : "Enter a valid email"}
                      </p>
                    )}
                  </div>

                  <div>
                    <div className="flex gap-1 items-center justify-start">
                      <label
                        htmlFor="tel1"
                        className="text-[10px]"
                      >
                        Phone number <span className="text-red-600">*</span>
                      </label>
                    </div>

                    <input
                      type="text"
                      id="tel1"
                      name="tel1"
                      value={formData?.tel1}
                      onChange={handleChange}
                      className="mt-1 block w-full p-2 border border-[#42506666] rounded"
                      required
                    />
                  </div>
                </div>

                <h2 className="font-bold mt-4">Address</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                  <div>
                    <div className="flex gap-1 items-center justify-start">
                      <label
                        htmlFor="Building&Street"
                        className="text-[10px]"
                      >
                        Building and Street (Line 1of 2)
                      </label>
                    </div>

                    <input
                      type="text"
                      id="Building&Street"
                      name="BuildingAndStreetOne"
                      value={formData?.BuildingAndStreetOne}
                      onChange={handleChange}
                      className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow focus:ring-blue-500 focus:border-blue-500"
                      required placeholder="Building and street"
                    />
                  </div>

                  <div>
                    <div className="flex gap-1">
                      <label
                        htmlFor="Building&Street2"
                        className="text-[10px] "
                      >
                        Building and Street (Line 2 of 2)
                      </label>
                    </div>

                    <input
                      type="text"
                      id="Building&Street2"
                      name="BuildingAndStreetTwo"
                      value={formData?.BuildingAndStreetTwo}
                      onChange={handleChange}
                      className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow focus:ring-blue-500 focus:border-blue-500"
                      required placeholder="Building and street"
                    />
                  </div>

                  <div>
                    <div className="flex gap-1">
                      <label htmlFor="Town_City" className="text-[10px]">
                        Town/City
                      </label>
                    </div>

                    {formData.countyOption === 'other' ? (
                      <input
                        type="text"
                        id="Town_City"
                        name="Town_City"
                        placeholder="Enter Town/City"
                        value={formData?.Town_City}
                        onChange={handleChange}
                        className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                    ) : (
                      <select
                        id="Town_City"
                        name="Town_City"
                        value={formData?.Town_City}
                        onChange={handleChange}
                        className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        required
                      >
                        <option value="">Select Town/City</option>
                        <option value="London">London</option>
                        <option value="Birmingham">Birmingham</option>
                        <option value="Manchester">Manchester</option>
                        <option value="Glasgow">Glasgow</option>
                        <option value="Liverpool">Liverpool</option>
                        <option value="Leeds">Leeds</option>
                        <option value="Sheffield">Sheffield</option>
                        <option value="Edinburgh">Edinburgh</option>
                        <option value="Bristol">Bristol</option>
                        <option value="Cardiff">Cardiff</option>
                        <option value="Nottingham">Nottingham</option>
                        <option value="Leicester">Leicester</option>
                        <option value="Coventry">Coventry</option>
                        <option value="Kingston upon Hull">Kingston upon Hull</option>
                        <option value="Newcastle upon Tyne">Newcastle upon Tyne</option>
                        <option value="Brighton">Brighton</option>
                        <option value="Southampton">Southampton</option>
                        <option value="Portsmouth">Portsmouth</option>
                        <option value="Derby">Derby</option>
                        <option value="Stoke-on-Trent">Stoke-on-Trent</option>
                        <option value="Wolverhampton">Wolverhampton</option>
                        <option value="Plymouth">Plymouth</option>
                        <option value="Aberdeen">Aberdeen</option>
                        <option value="Norwich">Norwich</option>
                        <option value="Luton">Luton</option>
                      </select>
                    )}
                  </div>

                  <div>
                    <div className="flex gap-1">
                      <label htmlFor="Country" className="text-[10px]">
                        Country
                      </label>
                    </div>

                    <select
                      id="Country"
                      name="countyOption"
                      value={formData?.countyOption || 'UK'}
                      onChange={(e) => {
                        const selected = e.target.value;
                        setFormData((prev) => ({
                          ...prev,
                          countyOption: selected,
                          Country: selected === 'UK' ? 'United Kingdom' : '',
                        }));
                      }}
                      className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      required
                    >
                      <option value="UK">UK</option>
                      <option value="other">Other</option>
                    </select>

                    {formData.countyOption === 'other' && (
                      <input
                        type="text"
                        name="Country"
                        placeholder="Enter country"
                        value={formData?.Country}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            Country: e.target.value,
                          }))
                        }
                        className="mt-2 block w-full p-2 border border-[#42506666] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                    )}
                  </div>

                  <div>
                    <div className="flex gap-1">
                      <label
                        htmlFor="companyName"
                        className="text-[10px]"
                      >
                        Postcode
                      </label>
                    </div>

                    <input
                      type="text"
                      id="Building&Street"
                      name="Postcode"
                      value={formData?.Postcode}
                      onChange={handleChange}
                      className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow focus:ring-blue-500 focus:border-blue-500"
                      required placeholder="Postcode"
                    />
                  </div>
                </div>

                <div className="mt-6 flex justify-between">
                  <button
                    type="button"
                    onClick={() => {
                      onClose();
                      fetchUserData();
                    }}
                    className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={nextStep}
                    className={`bg-[#313342] text-white rounded-4 hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 py-1 px-8 ${formData?.firstName && formData?.lastName && formData?.title
                      ? "bg-custom-bg text-white hover:bg-gray-600"
                      : "bg-gray-300 text-gray-500 cursor-not-allowed"
                      }`}
                    disabled={!formData.firstName || !formData.lastName || !formData.title}
                  >
                    Next
                  </button>
                </div>
              </div>
            </>
          )}

          {step === 2 && (
            <>
              <div>
                <h3 className="font-bold mb-4">Security</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">

                  <div>
                    <div className="flex gap-1">
                      <label
                        htmlFor="county"
                        className="text-[10px]"
                      >
                        Date Of Birth
                      </label>
                    </div>

                    <input
                      type="date"
                      id="dateOfBirth"
                      name="dateOfBirth"
                      value={formData?.dateOfBirth}
                      onChange={handleChange}
                      className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    />
                    {!validation?.dateOfBirthValid && (
                      <span className="text-[10px] text-red-500">Date of Birth cannot be in the future.</span>
                    )}
                  </div>

                  <div>
                    <div className="flex gap-1 items-center justify-start">
                      <label
                        htmlFor="taxiFirm"
                        className="text-[10px]"
                      >
                        Position <span className="text-red-600">*</span>
                      </label>
                    </div>
                    <input
                      type="text"
                      id="position"
                      name="position"
                      value={formData?.position}
                      onChange={handleChange}
                      className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      required placeholder="Position"
                    />
                  </div>

                  <div>
                    <div className="flex gap-1 items-center justify-start">
                      <label
                        htmlFor="reportsTo"
                        className="text-[10px]"
                      >
                        Reports To
                      </label>
                    </div>
                    <input
                      type="text"
                      id="reportsTo"
                      name="reportsTo"
                      value={formData?.reportsTo}
                      onChange={handleChange}
                      className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Reports To"
                    />
                  </div>

                  <div className="relative">
                    <div className="flex gap-1 items-center justify-start">
                      <label
                        htmlFor="reportsTo"
                        className="text-[10px]"
                      >
                        Password <span className="text-red-600">*</span>
                      </label>
                    </div>
                    <input
                      type={showPasswords ? "text" : "password"}
                      id="password"
                      name="password"
                      value={formData?.password}
                      onChange={handleChange}
                      className={`mt-1 block w-full p-2 border rounded focus:outline-none ${formData?.password
                        ? validation?.passwordValid
                          ? "border-green-500"
                          : "border-red-500"
                        : "border-[#42506666]"
                        }`}
                      placeholder="Enter password"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPasswords((prev) => !prev)}
                      className="absolute right-2 top-7"
                    >
                      {showPasswords ? <AiOutlineEye size={20} /> : <AiOutlineEyeInvisible size={20} />}
                    </button>
                    {formData?.password && (
                      <p className={`text-[9px] ${validation?.passwordValid ? "text-green-700" : "text-red-700"}`}>
                        {validation?.passwordValid
                          ? "Strong password"
                          : "Use 6 or more characters with a mix of letters, numbers & symbols"}
                      </p>
                    )}
                  </div>

                  <div>
                    <div className="flex gap-1 items-center justify-start">
                      <label
                        htmlFor="taxiFirm"
                        className="text-[10px]"
                      >
                        Username <span className="text-red-600">*</span>
                      </label>
                    </div>
                    <input
                      type="text"
                      id="username"
                      name="username"
                      value={formData?.username}
                      onChange={handleChange}
                      className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      required placeholder="Username"
                    />
                  </div>

                  <div className="relative">
                    <div className="flex gap-1 items-center justify-start">
                      <label
                        htmlFor="taxiFirm"
                        className="text-[10px]"
                      >
                        Confirm Password <span className="text-red-600">*</span>
                      </label>
                    </div>
                    <input
                      type={showPasswords ? "text" : "password"}
                      id="confirmpassword"
                      name="confirmpassword"
                      value={formData?.confirmpassword}
                      onChange={handleChange}
                      className={`mt-1 block w-full p-2 border rounded focus:outline-none ${formData?.confirmpassword
                        ? validation?.passwordMatch
                          ? "border-green-500"
                          : "border-red-500"
                        : "border-[#42506666]"
                        }`}
                      placeholder="Confirm password"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPasswords((prev) => !prev)}
                      className="absolute right-2 top-7"
                    >
                      {showPasswords ? <AiOutlineEye size={20} /> : <AiOutlineEyeInvisible size={20} />}
                    </button>
                    {formData?.confirmpassword && (
                      <p className={`text-[9px] ${validation?.passwordMatch ? "text-green-700" : "text-red-700"}`}>
                        {validation?.passwordMatch ? "Passwords match" : "Passwords do not match"}
                      </p>
                    )}
                  </div>

                  <div>
                    <div className="flex gap-1 items-center justify-start">
                      <label
                        htmlFor="passwordExpires"
                        className="text-[10px]"
                      >
                        Password Expires <span className="text-red-600">*</span>
                      </label>
                    </div>

                    <input
                      type="date"
                      name="passwordExpires"
                      value={formData?.passwordExpires}
                      onChange={handleChange}
                      min={new Date().toISOString().split("T")[0]}
                      className="mt-1 block w-full p-2 border border-[#42506666] rounded"
                    />
                    {!validation?.passwordExpiresvalid && (
                      <span className="text-[9px] text-red-700">Password Expires cannot be in the past.</span>
                    )}
                  </div>

                  <div>
                    <label className="text-[10px]">Status</label>
                    <div className="flex gap-4 p-2">
                      <label className="flex items-center gap-2">
                        <input
                          type="radio"
                          name="isActive"
                          value="true"
                          checked={formData.isActive === true}
                          onChange={() =>
                            handleChange({
                              target: { name: "isActive", value: true },
                            })
                          }
                          className="accent-green-500"
                        />
                        <span className="text-xs">Active</span>
                      </label>

                      <label className="flex items-center gap-2">
                        <input
                          type="radio"
                          name="isActive"
                          value="false"
                          checked={formData.isActive === false}
                          onChange={() =>
                            handleChange({
                              target: { name: "isActive", value: false },
                            })
                          }
                          className="accent-red-500"
                        />
                        <span className="text-xs">Inactive</span>
                      </label>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <label
                      htmlFor="useravatar"
                      className="text-[10px]"
                    >
                      Upload Image:
                    </label>
                    <input
                      type="file"
                      id="useravatar"
                      name="useravatar"
                      accept="image/*"
                      onChange={handleFileChange}
                      className="mt-1 block w-48 text-[8px] text-gray-400 file:mr-4 file:py-1 p-2 file:px-4 file:rounded-lg file:border file:text-[10px] file:font-semibold file:bg-white hover:file:bg-blue-100 border border-[#0885864D] rounded-[10px] border-dashed "
                    />
                  </div>
                  <div>
                    {imagePreview && (
                      <div className="relative w-32 h-20 group">
                        <Image
                          width={100} height={100}
                          src={imagePreview}
                          alt="Avatar Preview"
                          className="w-full h-full object-cover rounded"
                        />
                        <button
                          type="button"
                          onClick={handleRemoveImage}
                          className="absolute top-0 right-0 bg-white rounded-full p-1 text-xs font-bold text-red-500 hover:bg-red-200"
                        >
                          ❌
                        </button>
                      </div>
                    )}
                  </div>

                  <div>
                    <label className="block text-[10px]">Role</label>
                    <div className="flex gap-4">
                      <label className="flex items-center gap-2">
                        <input
                          type="radio"
                          name="role"
                          value="admin"
                          checked={formData?.role === "admin"}
                          onChange={handleChange}
                          className="accent-blue-500"
                        />
                        <span className="text-xs">Admin</span>
                      </label>

                      <label className="flex items-center gap-2">
                        <input
                          type="radio"
                          name="role"
                          value="user"
                          checked={formData?.role === "user"}
                          onChange={handleChange}
                          className="accent-blue-500"
                        />
                        <span className="text-xs">User</span>
                      </label>
                    </div>
                  </div>

                </div>

                <div className="mt-6 flex gap-2 justify-between">
                  <div>
                    <button
                      onClick={prevStep}
                      className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                    >
                      Back
                    </button>
                  </div>
                  <div className="flex gap-2">
                    <button
                      type="button"
                      onClick={() => {
                        onClose();
                        resetform();
                        fetchUserData();

                      }}
                      className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                    >
                      Close
                    </button>

                    <button
                      type="submit"
                      className="bg-[#313342] text-white rounded-4 hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 py-1 px-8"
                    >
                      Submit
                    </button>
                  </div>
                </div>
              </div>
            </>
          )}
        </form>
      </div>
    </div>
  );
};

export default UpdateUserModel;