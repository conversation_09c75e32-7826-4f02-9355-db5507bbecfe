import { connect } from "@config/db.js";
import Driver from "@models/Driver/Driver.Model.js";
import DriverVehicleAllotment from "@models/DriverVehicleAllotment/DriverVehicleAllotment.Model.js";
import DriverMoreInfo from "@models/DriverMoreInfo/DriverMoreInfo.model.js";
import cloudinary from "@middlewares/cloudinary.js";
import { NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import { uploadImage } from "services/uploadImage.js";//global file for uploading image

// PUT handler for updating driver details
export async function PUT(request, context) {
  try {
    await connect();

    const id = context.params.DrivId;
    const data = await request.formData();

    // accept either key from frontend
    const file = data.get("imageFile") || data.get("image");

    // build plain object from formdata excluding file fields
    const formDataObject = {};
    for (const [key, value] of data.entries()) {
      if (key === "imageFile" || key === "image") continue;
      formDataObject[key] = value;
    }

    // hash password if provided
    if (formDataObject.password) {
      const salt = await bcrypt.genSalt(10);
      formDataObject.confirmPassword = formDataObject.password;
      formDataObject.password = await bcrypt.hash(formDataObject.password, salt);
    }

    const driver = await Driver.findById(id);
    if (!driver) {
      return NextResponse.json({ error: "Driver not found", status: 404 });
    }

    // if new file provided, upload with shared helper and replace old image
    if (file && typeof file === "object" && file.name) {
      const { imageFile: uploadedUrl, imagePublicId } = await uploadImage(file);

      if (uploadedUrl && imagePublicId) {
        if (driver.imagePublicId) {
          try {
            await cloudinary.uploader.destroy(driver.imagePublicId);
          } catch (err) {
            // don't block update on deletion failure
            console.error("Failed to delete old Cloudinary image:", err);
          }
        }

        driver.imageFile = uploadedUrl;
        driver.imagePublicId = imagePublicId;
      }
    }

    // update remaining fields, skip image fields
    for (const key in formDataObject) {
      if (["imageFile", "imagePublicId", "imageName"].includes(key)) continue;
      driver[key] = formDataObject[key];
    }

    await driver.save();

    return NextResponse.json({
      message: "Driver details updated successfully",
      driver,
      status: 200,
      success: true,
    });
  } catch (error) {
    console.error("Error updating driver details:", error);
    return NextResponse.json({ error: "Failed to update driver details", status: 500 });
  }
}

// GET handler for retrieving a specific product by ID
export async function GET(request, context) {
  try {
    // Connect to the database
    await connect();

    // Extract the product ID from the request parameters
    const id = context.params.DrivId;

    // Find the product by ID
    const Find_User = await Driver.findById(id);

    // Check if the product exists
    if (!Find_User) {
      return NextResponse.json({ result: "No User Found", status: 404 });
    } else {
      // Return the found product as a JSON response
      return NextResponse.json({ result: Find_User, status: 200 });
    }
  } catch (error) {
    console.error("Error retrieving product:", error);
    // Return an error response
    return NextResponse.json({ message: "Internal Server Error", status: 500 });
  }
}

// DELETE handler for deleting a driver and associated image
export const DELETE = async (request, { params }) => {
  try {
    // Connect to the database
    await connect();

    const { DrivId } = params; // Access the Driver ID from params

    // Find the driver by ID
    const driver = await Driver.findById({ _id: DrivId });
    if (!driver) {
      return NextResponse.json({ message: "Driver not found", status: 404 });
    }

    // Get the image public ID from the driver object (ensure the field matches your schema)
    const imagePublicId = driver.imagePublicId;

    // Delete the driver from the database
    const deletedDriver = await Driver.findByIdAndDelete({ _id: DrivId });

    if (!deletedDriver) {
      return NextResponse.json({ error: "Driver not found", status: 404 });
    }
    const deletedata = await DriverVehicleAllotment.deleteMany({
      driverId: DrivId,
    });
    const deletallinfo = await DriverMoreInfo.deleteMany({
      driverId: DrivId,
    });
    console.log(deletallinfo);
    console.log(deletedata);
    // If the driver has an associated image, delete it from Cloudinary
    if (imagePublicId) {
      try {
        const cloudinaryResponse = await cloudinary.uploader.destroy(
          imagePublicId
        );
        if (cloudinaryResponse.result !== "ok") {
          console.error("Failed to delete image from Cloudinary");
        }
      } catch (error) {
        console.error("Error deleting image from Cloudinary:", error);
        return NextResponse.json({
          error: "Failed to delete image from Cloudinary",
          status: 500,
        });
      }
    }

    // Return success response
    return NextResponse.json({
      message: "Driver and associated image deleted successfully",
      success: true,
      status: 200,
    });
  } catch (error) {
    console.error("Error deleting driver:", error);
    return NextResponse.json({
      error: "An error occurred while deleting the driver",
      status: 500,
    });
  }
};
