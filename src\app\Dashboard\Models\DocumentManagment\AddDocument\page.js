"use client";

import React, { useState, useEffect, useCallback } from "react";
import Header from "../../../Components/Header";
import Sidebar from "../../../Components/Sidebar";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import axios from "axios";
import { API_URL_Document } from "../../../Components/ApiUrl/ApiUrls";
import {
  getCompanyName, getUserName
} from "@/utils/storageUtils";
import { isAuthenticated } from "@/utils/verifytoken";
import { useRouter } from "next/navigation";
import DeleteModal from "../../../Components/DeleteModal";
import Image from "next/image";
import AddDocumentModel from "./AddDocumentModel";
import UpdateDocumentModel from "./UpdateDocumentModel";

const Page = () => {
  const router = useRouter();
  const [documents, setDocuments] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isMounted, setIsMounted] = useState(false);
  const [isOpenAddDocument, setIsOpenAddDocument] = useState(false);
  const [selectedCompanyName, setSelectedCompanyName] = useState("");
  const [editingDocument, setEditingDocument] = useState(null);
  const [isOpenUpdateDocument, setIsOpenUpdateDocument] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deleteDocumentId, setDeleteDocumentId] = useState(null);

  useEffect(() => {
    if (!isAuthenticated()) {
      router.push("/");
      return;
    }
  }, [router]);

  useEffect(() => {
    setIsMounted(true);
    const companyNameFromStorage = (() => {
      const name1 = getCompanyName();
      if (name1) return name1;

      const name2 = getUserName();
      if (name2) return name2;
    })();

    if (companyNameFromStorage) {
      setSelectedCompanyName(companyNameFromStorage);
    }
  }, []);

  const fetchData = useCallback(async () => {
    try {
      const token = localStorage.getItem("token");

      const response = await axios.get(`${API_URL_Document}`, {
        headers: token ? {
          Authorization: `Bearer ${token}`,
        } : {}
      });

      setDocuments(response?.data?.result || []);
    } catch (error) {
      console.error("Error fetching data:", error);
      if (error.response?.status === 401) {
        toast.error("Authentication failed. Please login again.");
        router.push("/");
      } else {
        toast.error("An error occurred while fetching documents.");
      }
    }
  }, [router]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    if (documents.length > 0 && selectedCompanyName) {
      let companyFiltered = documents?.filter((document) => {
        return document?.adminCompanyName &&
          document?.adminCompanyName?.toLowerCase() === selectedCompanyName?.toLowerCase();
      });

      // Apply search filter
      if (searchTerm) {
        companyFiltered = companyFiltered.filter((document) => {
          const documentName = document?.documentname?.toLowerCase() || "";
          const entityType = Array.isArray(document?.entityType)
            ? document?.entityType?.join(", ").toLowerCase()
            : (document?.entityType?.toLowerCase() || "");
          const searchLower = searchTerm.toLowerCase();

          return documentName.includes(searchLower) || entityType.includes(searchLower);
        });
      }

      setFilteredData(companyFiltered);
    } else {
      setFilteredData([]);
    }
  }, [documents, selectedCompanyName, searchTerm]);

  const isopendeletemodel = (id) => {
    setDeleteDocumentId(id);
    setIsDeleteModalOpen(true);
  };

  const handleDelete = async () => {
    try {
      const token = localStorage.getItem("token");

      if (!token) {
        toast.error("Authentication token not found. Please login again.");
        router.push("/");
        return;
      }

      const response = await axios.delete(`${API_URL_Document}/${deleteDocumentId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        }
      });

      const { data } = response;
      if (data?.success) {
        setDocuments((prevData) => prevData?.filter((item) => item?._id !== deleteDocumentId));
        toast.success("Document deleted successfully");
        fetchData(); // Refresh the list
      } else {
        toast.warn(data?.message || data?.error || "Failed to delete the document.");
      }
    } catch (error) {
      console.error("Error deleting document:", error);
      if (error.response?.status === 401) {
        toast.error("Authentication failed. Please login again.");
        router.push("/");
      } else if (error.response?.status === 403) {
        toast.error("You don't have permission to delete this document.");
      } else {
        toast.error(
          error.response?.data?.error || "An error occurred while deleting the document. Please try again."
        );
      }
    }
    setIsDeleteModalOpen(false);
    setDeleteDocumentId(null);
  };

  const handleEdit = (id) => {
    const docToEdit = filteredData.find(doc => doc._id === id);
    setEditingDocument(docToEdit);
    setIsOpenUpdateDocument(true);
  };

  if (!isMounted) return null;

  const openAddDocumentModel = () => {
    setIsOpenAddDocument(!isOpenAddDocument);
  };

  const closeUpdateDocumentModel = () => {
    setIsOpenUpdateDocument(false);
    setEditingDocument(null);
  };

  return (
    <div className="h-[100vh] overflow-hidden">
      <Header className="min-w-full" />
      <div className="flex gap-4">
        <Sidebar />
        <div
          className="w-[80%] xl:w-[85%] h-screen flex flex-col justify-start overflow-y-auto pr-4"
          style={{
            height: "calc(100vh - 90px)",
          }}
        >
          <h1 className="text-[#313342] font-medium text-2xl py-5 underline decoration-[#AEADEB] underline-offset-8">
            Document Management
          </h1>


          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 mt-4 gap-4">

            <div className="w-64">
              <div className="relative">

                  <input
                type="text"
                placeholder="Search..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8 pr-4 py-2 w-64 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
                <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                  <svg
                    className="h-4 w-4 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>
              </div>
            </div>


            <div className="w-full sm:w-auto">
              <button
                onClick={openAddDocumentModel}
                className="w-full sm:w-auto bg-[#313342] text-white px-6 py-2 rounded hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
              >
                + Add Document Type
              </button>
            </div>
          </div>


          <div className="py-5">
            <div className="drop-shadow-custom4">
              <div className="overflow-x-auto custom-scrollbar">
                <table className="w-full bg-white border table-auto">
                  <thead className="font-sans font-bold text-sm text-center">
                    <tr className="text-white bg-[#38384A]">
                      <th className="py-3 px-4 min-w-[300px] w-[300px] md:w-[50%] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Document Type
                      </th>
                      <th className="py-3 px-4 min-w-[300px] w-[300px] md:w-[50%] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Entity
                      </th>
                      <th className="py-3 px-4 min-w-[200px] w-[200px] md:w-[50%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="font-sans font-medium text-sm">
                    {filteredData?.map((document) => (
                      <tr key={document?._id} className="border-b text-center">
                        <td className="py-3 px-4 min-w-[300px] w-[300px] md:w-[50%] text-center whitespace-normal break-all overflow-hidden">
                          {document?.documentname}
                        </td>
                        <td className="py-3 px-4 min-w-[300px] w-[300px] md:w-[50%] text-center whitespace-normal break-all overflow-hidden">
                          {Array.isArray(document?.entityType)
                            ? document?.entityType?.join(", ")
                            : document?.entityType}
                        </td>
                        <td className="py-3 px-4 min-w-[200px] w-[200px] md:w-[50%] whitespace-normal break-all overflow-hidden text-center">
                          <div className="flex gap-4 justify-center">
                            <button onClick={() => handleEdit(document?._id)}>
                              <Image width={25} height={25} src="/edit.png" alt="edit" />
                            </button>
                            <button onClick={() => isopendeletemodel(document?._id)}>
                              <Image width={25} height={25} src="/trash.png" alt="delete" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                    {filteredData.length === 0 && (
                      <tr>
                        <td colSpan="3" className="py-8 text-center text-gray-500">
                          {searchTerm ? `No documents found matching "${searchTerm}"` : "No documents found"}
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      <AddDocumentModel
        isOpen={isOpenAddDocument}
        onClose={openAddDocumentModel}
        fetchData={fetchData}
      />

      <UpdateDocumentModel
        isOpen={isOpenUpdateDocument}
        onClose={closeUpdateDocumentModel}
        documentData={editingDocument}
        fetchData={fetchData}
      />

      <DeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onDelete={handleDelete}
        Id={deleteDocumentId}
      />
    </div>
  );
};

export default Page;