// apiUrls.js
export const url = "http://localhost:4000";
// export const url = "https://vmsp.netlify.app";
// export const url = "http://**************:4000";
// export const url = "http://vms.nearby.cab";
// export const url = "https://vms.nearby.cab";

// login
export const API_URL_Login = `${url}/api/User/Login`;
export const API_URL_DriverMoreInfonano = `${url}/api/DriverMoreInfo/addnanorecord`;
export const API_URL_DRIVERTOTAL = `${url}/api/DriverMoreInfo/Driverstotal`;
export const API_URL_CRONJOB = `${url}/api/DriverMoreInfo/cronJob`;
export const API_URL_Company = `${url}/api/Company`;
export const API_URL_USER = `${url}/api/User`;
export const API_URL_Driver = `${url}/api/Driver`;
export const API_URL_DriverMoreInfo = `${url}/api/DriverMoreInfo`;
export const API_URL_DriverMoreupdate = `${url}/api/DriverMoreInfo/DriverMoreID`;
// export const API_URL_Drivercalculation = `${url}/api/Driver/Driverid`;
export const API_URL_Enquiry = `${url}/api/Enquiry`;
export const API_URL_Firm = `${url}/api/Firm`;
export const API_URL_Insurence = `${url}/api/Insurence`;
export const API_URL_LocalAuthority = `${url}/api/LocalAuthority`;
export const API_URL_Manufacturer = `${url}/api/Manufacturer`;
export const API_URL_Payment = `${url}/api/Payment`;
export const API_URL_Signature = `${url}/api/Signature`;
export const API_URL_Supplier = `${url}/api/Supplier`;
export const API_URL_Vehicle = `${url}/api/Vehicle`;
export const API_URL_Vehicle_getspecificvehicleid = `${url}/api/Vehicle/getspecificvehicle`;
export const API_URL_VehicleType = `${url}/api/VehicleType`;
export const API_URL_Title = `${url}/api/Title`;
export const API_URL_Badge = `${url}/api/Badge`;
export const API_URL_CarModel = `${url}/api/CarModel`;
export const API_URL_Employee = `${url}/api/Employee`;
export const API_URL_Transmission = `${url}/api/Transmission`;
export const API_URL_FuelType = `${url}/api/FuelType`;
export const API_URL_Type = `${url}/api/BodyType`;
export const API_URL_Vehicle_For_Image = `${url}/api/Vehicle/specificImage`;
export const API_URL_Driver_Vehicle_Allotment = `${url}/api/DriverVehicleAllotment`;
// Report API
export const API_URL_Maintainance = `${url}/api/VehicleReport`;
export const API_URL_VehicleMOT = `${url}/api/VehicleMOT`;
export const API_URL_UpdateMostRecentPendingInMot = `${url}/api/VehicleMOT/UpdateOnlyOneToZero`;
export const API_URL_VehicleService = `${url}/api/VehicleService`;
export const API_URL_UpdateMostRecentPendingInServeice = `${url}/api/VehicleService/UpdateOnlyOneToZero`;
export const API_URL_VehicleRoadTex = `${url}/api/VehicleRoadTax`;
export const API_URL_UpdateMostRecentPendingInRoadTex = `${url}/api/VehicleRoadTax/UpdateOnlyOneToZero`;
// Driver Login API
export const API_URL_DriverLogin = `${url}/api/Driver/login`;
export const API_URL_Balance = `${url}/api/Driver/recordTransaction`;
export const API_URL_ForTransaction = `${url}/api/DriverMoreInfo/addnanorecord/transaction`;
// Find Vehicle Info API
export const API_URL_Vehicleinfo = `${url}/api/Vehicle/FindVehicleinfo`;
// upload document api
export const API_URL_DocumentUpload = `${url}/api/upload`;
// add document
export const API_URL_Document = `${url}/api/DocumentManagement`;
// add driver document
export const API_URL_DriverDocument = `${url}/api/DriverDocuments`;
// add vehicle document
export const API_URL_VehicleDocument = `${url}/api/VehicleDocuments`;
// check doucment expired
export const API_URL_CheckExpired = `${url}/api/VehicleDocuments/expiry`;
// get device info
export const API_URL_DeviceInfo = `${url}/api/Data_`;

