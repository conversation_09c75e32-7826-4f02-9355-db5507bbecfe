import jwt from "jsonwebtoken";
import { connect } from "@config/db.js";
import DriverDocument from "@models/DriverDocuments/DriverDocuments.Model.js";
import { catchAsyncErrors } from "@middlewares/catchAsyncErrors.js";
import { NextResponse } from "next/server";

/* ---------------- CREATE ---------------- */
export const POST = catchAsyncErrors(async (request) => {
  await connect();

  /* ---------------- JWT AUTH ---------------- */
  const authHeader = request.headers.get("Authorization");
    if (!authHeader) {
     return NextResponse.json({ error: "Unauthorized", status: 401 });
   }

  const token  = authHeader || authHeader.split(" ")[1];
  const secret = process.env.JWT_SECRET;
  let decoded;
  try {
    decoded = jwt.verify(token, secret);
  } catch {
    return NextResponse.json({ error: "Invalid token", status: 401 });
  }

  const companyId = decoded?.userId;
  const companyName = decoded?.company;

  if (!companyId || !companyName) {
    return NextResponse.json({ error: "Company info missing in token", status: 400 });
  }

  const {
    document,
    documentname,
    Driverid,
    isActive,
    documentExpire,
    adminCreatedBy,
  } = await request.json();

const duplicate = await DriverDocument.findOne({
  documentname,
  adminCompanyId: companyId,
  adminCompanyName: companyName,
  Driverid: Driverid,
});


  if (duplicate) {
    return NextResponse.json({
      error: "Document with the same name already exists for this company",
      status: 400,
    });
  }

  const newDoc = new DriverDocument({
    document,
    documentname,
    Driverid,
    isActive,
    documentExpire,
    adminCreatedBy,
    adminCompanyId: companyId,
    adminCompanyName: companyName,
  });

  const saved = await newDoc.save();

  return NextResponse.json(
    saved
      ? { message: "Document created successfully", success: true, status: 200, saved }
      : { error: "Document not added", status: 400 }
  );
});

/* ---------------- READ (ALL) ---------------- */
export const GET = async () => {
  try {
    await connect();
    
    const result = await DriverDocument.find().populate('Driverid');    
    const count = await DriverDocument.countDocuments();
    return NextResponse.json({ result, count });
  } catch (error) {
    console.error("Error fetching DriverDocument data:", error);
    return NextResponse.json({ error: "Internal server error", status: 500 });
  }
};

