import { NextResponse } from "next/server";
import { connect } from "@config/db.js";
import DriverMoreInfo from "@models/DriverMoreInfo/DriverMoreInfo.model.js";
import Driver from "@models/Driver/Driver.Model.js";
import mongoose from "mongoose";

export const GET = async (request,context) => {
  await connect(); // Ensure database connection

  const { searchParams } = new URL(request.url);
  const id = context.params.id;
  const Id = searchParams.get("driverId");

  let drivers = await DriverMoreInfo.find({ vehicleId: id, driverId:Id });
  if(Id === null){
    const dId = searchParams.get("vehicleId");
    drivers = await DriverMoreInfo.find({  driverId:id ,vehicleId: dId});
  }

  let totalPayment = 0;
  let totalCost = 0;
  let totalPay = 0;

  // 🔹 Calculate totals for each driver
  for (const driver of drivers) {
    totalPayment += driver.payment || 0;
    totalCost += driver.cost || 0;
    totalPay += driver.pay || 0;
  }

  // 🔹 Calculate the remaining amount
  const remainingAmount = totalPayment - totalPay;

  for (const driver of drivers) {
    if (driver.driverId) {
      const driverObjectId = new mongoose.Types.ObjectId(driver.driverId);
  
     await Driver.findOneAndUpdate(
        { _id: driverObjectId, adminCompanyName: driver.adminCompanyName },
        { $set: { totalamount: remainingAmount } },
        { new: true }
      );
    }
  }

  return NextResponse.json({
    driverId: drivers[0]?.driverId,
    message: "Totals calculated successfully",
    totalPayment,
    totalCost,
    totalPay,
    remainingAmount
  });
};
