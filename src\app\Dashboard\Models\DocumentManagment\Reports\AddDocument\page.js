"use client";

import React, { useState, useEffect } from "react";
import Header from "../../Components/Header";
import Sidebar from "../../Components/Sidebar";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { isAuthenticated } from "@/utils/verifytoken";
import { useRouter } from "next/navigation";
import Image from "next/image";

const AddDocumentPage = () => {
  const router = useRouter();
  const [isMounted, setIsMounted] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    vehicle: "",
    documentType: "",
    documentExpiry: "",
    selectedFile: null
  });

  // Document table state
  const [documents, setDocuments] = useState([
    {
      id: 1,
      documentType: "Type 1",
      uploadedFile: "Doc.png",
      status: "approved",
      expiry: "01-01-2028"
    },
    {
      id: 2,
      documentType: "Type 2",
      uploadedFile: "Doc.png",
      status: "rejected",
      expiry: "01-01-2028"
    },
    {
      id: 3,
      documentType: "Type 3",
      uploadedFile: "Doc.png",
      status: "warning",
      expiry: "01-01-2028"
    },
    {
      id: 4,
      documentType: "Type 4",
      uploadedFile: "Doc.png",
      status: "info",
      expiry: "01-01-2028"
    }
  ]);

  useEffect(() => {
    if (!isAuthenticated()) {
      router.push("/");
      return;
    }
  }, [router]);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    setFormData(prev => ({
      ...prev,
      selectedFile: file
    }));
  };

  const handleUpload = () => {
    // Placeholder upload functionality
    if (!formData.selectedFile) {
      toast.error("Please select a file to upload");
      return;
    }

    toast.success("Document uploaded successfully!");
    // Reset form
    setFormData({
      vehicle: "",
      documentType: "",
      documentExpiry: "",
      selectedFile: null
    });
    // Reset file input
    document.getElementById("fileInput").value = "";
  };

  const handleEdit = (id) => {
    // Placeholder edit functionality
    toast.info(`Edit document with ID: ${id}`);
  };

  const handleDelete = (id) => {
    // Placeholder delete functionality
    toast.info(`Delete document with ID: ${id}`);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case "approved":
        return (
          <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
        );
      case "rejected":
        return (
          <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
        );
      case "warning":
        return (
          <div className="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
        );
      case "info":
        return (
          <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="w-6 h-6 bg-gray-500 rounded-full flex items-center justify-center">
            <span className="text-white text-xs">?</span>
          </div>
        );
    }
  };

  if (!isMounted) return null;

  return (
    <div className="h-[100vh] overflow-hidden">
      <Header className="min-w-full" />
      <div className="flex gap-4">
        <Sidebar />
        <div
          className="w-[80%] xl:w-[85%] h-screen flex flex-col justify-start overflow-y-auto pr-4"
          style={{
            height: "calc(100vh - 90px)",
          }}
        >
          <div className="flex justify-between items-center py-5">
            <h1 className="text-[#313342] font-medium text-2xl underline decoration-[#AEADEB] underline-offset-8">
              Add Document
            </h1>
            <button
              onClick={() => router.back()}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              title="Close"
            >
              <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Form Section */}
          <div className="bg-white p-6 rounded-lg drop-shadow-custom4 mb-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              {/* Vehicle Selector */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Vehicle*
                </label>
                <select
                  name="vehicle"
                  value={formData.vehicle}
                  onChange={handleInputChange}
                  className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-700"
                >
                  <option value="">Select Vehicle</option>
                  <option value="vehicle1">Vehicle 1</option>
                  <option value="vehicle2">Vehicle 2</option>
                  <option value="vehicle3">Vehicle 3</option>
                </select>
              </div>

              {/* Document Type Selector */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Document Type*
                </label>
                <select
                  name="documentType"
                  value={formData.documentType}
                  onChange={handleInputChange}
                  className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-700"
                >
                  <option value="">Select Document Type</option>
                  <option value="type1">Type 1</option>
                  <option value="type2">Type 2</option>
                  <option value="type3">Type 3</option>
                  <option value="type4">Type 4</option>
                </select>
              </div>

              {/* Document Expiry Date */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Document Expiry*
                </label>
                <div className="relative">
                  <input
                    type="date"
                    name="documentExpiry"
                    value={formData.documentExpiry}
                    onChange={handleInputChange}
                    placeholder="Add Document Expiry"
                    className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-700"
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            {/* File Upload Section */}
            <div className="flex flex-col sm:flex-row items-start sm:items-end gap-4">
              <div className="flex-1 w-full sm:w-auto">
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                  <button
                    type="button"
                    onClick={() => document.getElementById("fileInput").click()}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                  >
                    Choose files
                  </button>
                  <span className="text-sm text-gray-500 break-all">
                    {formData.selectedFile ? formData.selectedFile.name : "Document.jpeg"}
                  </span>
                </div>
                <input
                  type="file"
                  id="fileInput"
                  onChange={handleFileChange}
                  accept=".pdf,.png,.jpg,.jpeg"
                  className="hidden"
                />
                <p className="text-xs text-red-500 mt-2">
                  File supported .pdf, .png & .jpg
                </p>
              </div>
              <button
                onClick={handleUpload}
                className="w-full sm:w-auto px-8 py-3 bg-[#38384A] text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 font-medium transition-colors"
              >
                Upload
              </button>
            </div>
          </div>

          {/* Documents Table */}
          <div className="bg-white rounded-lg drop-shadow-custom4">
            <div className="overflow-x-auto custom-scrollbar">
              <table className="w-full border-collapse min-w-[600px]">
                <thead>
                  <tr className="bg-[#38384A] text-white">
                    <th className="py-4 px-3 sm:px-6 text-left font-medium text-sm min-w-[120px]">Document Type</th>
                    <th className="py-4 px-3 sm:px-6 text-left font-medium text-sm min-w-[120px]">Uploaded File</th>
                    <th className="py-4 px-3 sm:px-6 text-center font-medium text-sm min-w-[80px]">Status</th>
                    <th className="py-4 px-3 sm:px-6 text-center font-medium text-sm min-w-[100px]">Expiry</th>
                    <th className="py-4 px-3 sm:px-6 text-center font-medium text-sm min-w-[100px]">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {documents.map((doc) => (
                    <tr key={doc.id} className="border-b border-gray-200 hover:bg-gray-50 transition-colors">
                      <td className="py-4 px-3 sm:px-6 text-sm font-medium text-gray-900">{doc.documentType}</td>
                      <td className="py-4 px-3 sm:px-6 text-sm text-gray-700">{doc.uploadedFile}</td>
                      <td className="py-4 px-3 sm:px-6 text-center">
                        <div className="flex justify-center">
                          {getStatusIcon(doc.status)}
                        </div>
                      </td>
                      <td className="py-4 px-3 sm:px-6 text-center text-sm text-gray-700">{doc.expiry}</td>
                      <td className="py-4 px-3 sm:px-6 text-center">
                        <div className="flex justify-center gap-2 sm:gap-3">
                          <button
                            onClick={() => handleEdit(doc.id)}
                            className="p-1.5 sm:p-2 hover:bg-gray-200 rounded-md transition-colors"
                            title="Edit document"
                          >
                            <Image width={16} height={16} src="/edit.png" alt="edit" className="sm:w-[18px] sm:h-[18px]" />
                          </button>
                          <button
                            onClick={() => handleDelete(doc.id)}
                            className="p-1.5 sm:p-2 hover:bg-red-100 rounded-md transition-colors"
                            title="Delete document"
                          >
                            <Image width={16} height={16} src="/trash.png" alt="delete" className="sm:w-[18px] sm:h-[18px]" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Loading indicator at bottom */}
            <div className="flex justify-center py-4">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddDocumentPage;
