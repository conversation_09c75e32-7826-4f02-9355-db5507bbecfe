import { connect } from "@config/db.js";
import Badge from "@models/Badge/Badge.Model.js";
import { NextResponse } from "next/server";

export const PUT = async (request, context) => {
  try {
    await connect();

    const id = context.params.BadgeID; // Extract ManufacturerID from params
    const data = await request.json(); // Get the form data

    // console.log(id);
    // console.log(data);

    // Destructure the necessary fields
    const { name, description, isActive } = data;

    
    const badge = await Badge.findById({ _id: id });

    if (!badge) {
      return NextResponse.json({
        error: "badge not found",
        status: 404,
      });
    }

   
    badge.name = name ? name.trim() : badge.name; 
    badge.description = description ? description.trim() : badge.description;
    badge.isActive = isActive !== undefined ? isActive : badge.isActive; 

    await badge.save();

    return NextResponse.json({
      message: "Badge details updated successfully",
      badge,
      status: 200,
    });
  } catch (error) {
    console.error("Error updating Badge:", error);
    return NextResponse.json({
      error: "Failed to update Badge",
      status: 500,
    });
  }
};


export const GET = async (request, context) => {
  try {
    
    await connect();

    // Extract the Manufacturer ID from the request parameters
    const id = context.params.BadgeID; // Use context.params for accessing the parameters
    // console.log(id);

    // Find the manufacturer by ID
    const Find_Badge = await Badge.findById({ _id: id });

    if (!Find_Badge) {
      return NextResponse.json({
        result: "No Badge Found",
        status: 404,
      });
    }

    return NextResponse.json({ result: Find_Badge, status: 200 });
  } catch (error) {
    console.error("Error fetching Badge:", error); 
    return NextResponse.json({
      result: "Failed to fetch Badge",
      status: 500,
    });
  }
};

export const DELETE = async (request, { params }) => {
  try {
  
    await connect();

    const { BadgeID } = params; // Access the ManufacturerID from params

    // console.log("Manufacturer ID:", BadgeID);

    const deletedManufacturer = await Badge.findByIdAndDelete(BadgeID);

    if (!deletedManufacturer) {
      return NextResponse.json({
        error: "Badge not found",
        status: 404,
      });
    }

    return NextResponse.json({
      message: "Badge deleted successfully",
      success: true,
      status: 200,
    });
  } catch (error) {
    console.error("Error deleting Badge:", error);
    return NextResponse.json({
      error: "An error occurred while deleting the Badge",
      status: 500,
    });
  }
};
