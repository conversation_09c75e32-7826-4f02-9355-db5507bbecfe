"use client";
import {
  API_URL_Vehicle,
  API_URL_Vehicle_For_Image,
} from "@/app/Dashboard/Components/ApiUrl/ApiUrls";
import axios from "axios";
import Select from "react-select";
import { toast } from "react-toastify";
import React, { useEffect, useRef, useState } from "react";
import {
  fetchManfacturer,
  fetchLocalAuth,
  fetchTransmission,
  fetchType,
  fetchFuelType,
  fetchCarModel,
  fetchSupplier
} from "../../Components/DropdownData/taxiFirm/taxiFirmService";
import {
  getCompanyName,
  getUserId,
  getUserName, getflag, getcompanyId, getUserRole
} from "@/utils/storageUtils";
import Image from "next/image";


const UpdateVehicleModel = ({ isOpen, onClose, fetchData, vehicleId }) => {
  const [vehicleData, setVehicleData] = useState({
    manufacturer: "",
    model: "",
    year: "",
    type: "",
    // 
    dateOfLastV5CIssued: '',
    co2Emissions: '',
    taxStatus: "",
    motStatus: "",
    markedForExport: "",
    typeApproval: "",
    wheelplan: "",
    monthOfFirstRegistration: "",
    // 
    engineType: "",
    fuelType: "",
    transmission: "",
    drivetrain: "",
    exteriorColor: "",
    interiorColor: "",
    weight: "",
    height: "",
    width: "",
    length: "",
    passengerCapacity: "",
    LocalAuthority: "",
    cargoCapacity: "",
    vehicleStatus: "",
    horsepower: "",
    torque: "",
    acceleration: "",
    topSpeed: "",
    fuelEfficiency: "",
    safetyFeatures: [],
    techFeatures: [],
    towingCapacity: "",
    price: "",
    registrationNumber: "",
    warrantyInfo: "",
    adminCreatedBy: "",
    adminCompanyName: "",
    isActive: false,
    enginesize: "",
    chasisnumber: "",
    vehicleSite: "",
    fleetEntryDate: "",
    milesOnFleetEntry: "",
    plannedFleetExit: "",
    milesOnFleetExit: "",
    actualExitDate: "",
    milesAtActualExit: "",
    doors: "",
    color: "",
    editablecolor: "",
    roadTaxDate: "",
    roadTaxCycle: "",
    motDueDate: "",
    motCycle: "",
    seats: "",
    abiCode: "",
    nextServiceDate: "",
    nextServiceMiles: "",
    roadTaxCost: "",
    listPrice: "",
    purchasePrice: "",
    insuranceValue: "",
    departmentCode: "",
    maintenance: false,
    issues_damage: "",
    damage_image: [],
    recovery: "",
    organization: "",
    repairStatus: "",
    jobNumber: "",
    memo: "",
    parts: [
      {
        partNumber: "",
        partName: "",
        partprice: "",
        partsupplier: "",
      },
    ],
    TestDate: "",
    PlateExpiryDate: "",
    Insurance: "",
    insurancePolicyNumber: "",
    PDFofPolicy: "",
    defect: "",
    Defectdate: "",
    defectstatus: "",
    defectdescription: "",
    defectaction: "",
    additionalInfo: "",
    RPCExpiryDate: "",
    TailLiftExpiryDate: "",
    forkLiftNumber: "",
    ForkLiftInspectionDate: "",
    ForkLiftInspectionNumberNotes: "",
    cardocuments: [],
    companyId: null,
  });

  const [superadmin, setSuperadmin] = useState(null);
  const [localAuthority, setLocalAuth] = useState([]);
  const [manufacturer, setManufacturer] = useState([]);
  const [imagePreview, setImagePreview] = useState([]);
  const [damagePreview, setdamagePreview] = useState([]);
  const [carModel, setCarModel] = useState([]); //
  const [pdfPreview, setpdfPreview] = useState(null);
  const [cardocumentimagePreview, setcardocumentimagePreview] = useState([]);
  const [transmission, setTransmission] = useState([]);
  const [type, setType] = useState([]);
  const [fueltype, setFuelType] = useState([]);
  const fileInputRef = useRef(null);
  const [step, setStep] = useState(1);
  const [maintenance, setMaintenance] = useState(true);
  const [selfFitSetting, setSelfFitSetting] = useState(false);
  const [suppliers, setSuppliers] = useState([]);
  const fileInput = useRef(null);
  useEffect(() => {
    const storedCompanyName = getCompanyName();
    const storedSuperadmin = getUserRole();

    if (storedSuperadmin) {
      setSuperadmin(storedSuperadmin);
    }
    if (storedCompanyName) {
      setVehicleData((prevData) => ({
        ...prevData,
        adminCompanyName: storedCompanyName,
      }));
    }
  }, []);

  useEffect(() => {
    const storedcompanyName = (() => {
      const name1 = getCompanyName();
      if (name1) return name1;

      const name2 = getUserName();
      if (name2) return name2;
    })();
    const userId = getUserId();
    const flag = getflag();
    const compID = getcompanyId();
    if (storedcompanyName && userId) {
      if (storedcompanyName.toLowerCase() === "superadmin" && flag === "true") {
        setVehicleData((prevData) => ({
          ...prevData,
          adminCompanyName: storedcompanyName,
          companyId: compID
        }));
      }
    } else {
      setVehicleData((prevData) => ({
        ...prevData,
        adminCompanyName: storedcompanyName,
        companyId: userId,
      }));
    }
  }, []);

  const [selectedCarmodel, setseletedCarmodel] = useState([]);

  const [selectedOptions, setSelectedOptions] = useState([]);
  const [option, setoptions] = useState([]);
  const [optiontech, setoptionstech] = useState([]);

  const [selectedOptionstech, setSelectedtech] = useState([]);
  console.log(option, optiontech)


  const options = [
    { value: "airbags", label: "airbags" },
    { value: "abs", label: "abs" },
    { value: "stability control", label: "stability control" },
    { value: "traction control", label: "traction control" },
    { value: "blind spot monitoring", label: "blind spot monitoring" },
    { value: "lane departure warning", label: "lane departure warning" },
    { value: "adaptive cruise control", label: "adaptive cruise control" },
    { value: "rearview camera", label: "rearview camera" },
    { value: "parking sensors", label: "parking sensors" },
    {
      value: "automatic emergency braking",
      label: "automatic emergency braking",
    },
  ];

  const featurestech = [
    { value: "navigation", label: "navigation" },
    { value: "bluetooth", label: "bluetooth" },
    { value: "backup_camera", label: "backup_Camera" },
    { value: "adaptive_headlights", label: "adaptive_headlights" },
    { value: "lane_keep_assist", label: "lane_keep_assist" },
    { value: "parking_assist", label: "parking_assist" },
    { value: "smartphone_integration", label: "smartphone_integration" },
    { value: "voice_recognition", label: "voice_recognition" },
    { value: "keyless_entry", label: "keyless_entry" },
    { value: "rear_seat_entertainment", label: "rear_seat_entertainment" },
  ];
  console.log(selectedOptions, selectedOptionstech)

  const handleChangesafty = (selected) => {
    setSelectedOptions(selected);
    const selectedValues = selected.map((option) => option.value);
    setVehicleData((prevData) => ({
      ...prevData,
      safetyFeatures: selectedValues,
    }));

  };
  const handleChangestech = (selected) => {
    setSelectedtech(selected);
    const selectedValues = selected.map((option) => option.value);
    setVehicleData((prevData) => ({
      ...prevData,
      techFeatures: selectedValues,
    }));
  };

  const handleChange = (e) => {
    const { name, value, type, checked, files } = e.target;

    setVehicleData((prevData) => {
        if (type === "file") {
        // generate previews for UI
        const previews = Array.from(files).map((file) => ({
          url: URL.createObjectURL(file), // temporary preview URL
          file,
        }));

        // update preview state (without removing your existing logic)
        setcardocumentimagePreview(previews);

        return {
          ...prevData,
          [name]: Array.from(files), // your original code
        };
      }


      if (type === "checkbox") {
        return {
          ...prevData,
          [name]: checked,
        };
      }

      if (name === "manufacturer") {

        const filteredCarModel = carModel.filter(
          (model) => model.makemodel === value
        );
        setseletedCarmodel(filteredCarModel);
        return {
          ...prevData,
          [name]: value,
          model: "",
        };
      }

      if (name === "model") {
        return {
          ...prevData,
          [name]: value,
        };
      }

      return {
        ...prevData,
        [name]: value,
      };
    });
  };

  useEffect(() => {
    const fetchDataForDropdowns = async () => {
      try {
        const storedCompanyName = vehicleData.adminCompanyName;
        const manufacturerResponse = await fetchManfacturer();
        const ModelData = await fetchCarModel();

        const localauthResponse = await fetchLocalAuth();
        const transmission = await fetchTransmission();
        const type = await fetchType();
        const fueltype = await fetchFuelType();
        const supplier = await fetchSupplier();

        const filteredManufacturer =
          superadmin === "superadmin"
            ? manufacturerResponse.Result
            : manufacturerResponse.Result.filter(
              (manufacturer) =>
                manufacturer.adminCompanyName === storedCompanyName ||
                manufacturer.adminCompanyName === "superadmin"
            );

        const filteredCarModel =
          superadmin === "superadmin"
            ? ModelData.result
            : ModelData.result.filter(
              (model) =>
                model.adminCompanyName === storedCompanyName ||
                model.adminCompanyName === "superadmin"
            );

        const filteredLocalAuth =
          superadmin === "superadmin"
            ? localauthResponse.Result
            : localauthResponse.Result.filter(
              (localauth) =>
                localauth.adminCompanyName === storedCompanyName ||
                localauth.adminCompanyName === "superadmin"
            );

        const filteredsupplier =
          superadmin === "superadmin"
            ? supplier.Result
            : supplier.Result.filter(
              (supplier) =>
                supplier.adminCompanyName === storedCompanyName ||
                supplier.adminCompanyName === "superadmin"
            );
        const filteredtransmission =
          superadmin === "superadmin"
            ? transmission.Result
            : transmission.Result.filter(
              (transmission) =>
                transmission.adminCompanyName === storedCompanyName ||
                transmission.adminCompanyName === "superadmin"
            );
        const filteredtype =
          superadmin === "superadmin"
            ? type.Result
            : type.Result.filter(
              (type) =>
                type.adminCompanyName === storedCompanyName ||
                type.adminCompanyName === "superadmin"
            );
        const filteredfueltype =
          superadmin === "superadmin"
            ? fueltype.Result
            : fueltype.Result.filter(
              (fueltype) =>
                fueltype.adminCompanyName === storedCompanyName ||
                fueltype.adminCompanyName === "superadmin"
            );

        setManufacturer(filteredManufacturer);
        setCarModel(filteredCarModel);

        setLocalAuth(filteredLocalAuth);
        setTransmission(filteredtransmission);
        setType(filteredtype);
        setFuelType(filteredfueltype);

        setSuppliers(filteredsupplier);

      } catch (error) {
        console.error("Error fetching dropdown data:", error);
      }
    };

    fetchDataForDropdowns();
  }, [superadmin, vehicleData.adminCompanyName]);

  const fetchVehicleData = async () => {
    try {
      const response = await axios.get(`${API_URL_Vehicle}/${vehicleId}`);
      setVehicleData(response.data.result);
      setImagePreview(response.data.result.images || null);
      setdamagePreview(response.data.result.damageImage || null);
      setpdfPreview(response.data.result.PDFofPolicyUrl || null);
      setcardocumentimagePreview(response.data.result.cardocuments || []);
      setoptions(response.data.result.safetyFeatures || []);
      setoptionstech(response.data.result.techFeatures || []);
      setVehicleData({
        ...response.data.result,
        parts: response.data.result.parts || [
          { partNumber: "", partName: "", partprice: "", partsupplier: "" },
        ],
      });
    } catch (error) {
      console.error("Error fetching vehicle data:", error);
    }
  };

  useEffect(() => {
    if (vehicleId) {
      fetchVehicleData();
    }
  }, [vehicleId]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    const formData = new FormData();



    for (const key in vehicleData) {
      if (
        key === "imageFiles" ||
        key === "damage_image" ||
        key === "cardocuments"
      ) {
        vehicleData[key].forEach((file) => {
          formData.append(key, file);
        });
      } else if (typeof vehicleData[key] === "object") {
        for (const subKey in vehicleData[key]) {
          formData.append(`${key}[${subKey}]`, vehicleData[key][subKey]);
        }
      } else {
        formData.append(key, vehicleData[key]);
      }
    }

    // console.log(vehicleData);
    try {
      const response = await axios.put(
        `${API_URL_Vehicle}/${vehicleId}`,
        vehicleData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      // console.log("Response:", response.data);
      toast.success(response.data.message);
      fetchData();
      resetForm();
      onClose();
      setStep(1);
    } catch (error) {
      console.error("Error updating vehicle data:", error);
      toast.error(response.data.error);
    }
  };

  const [sele, setselect] = useState("");
  const handleImageClick = (img) => {

    setselect(img);
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileSelect = async (event) => {
    event.preventDefault();
    const file = event.target.files[0];

    const { _id, url, publicId } = sele;
    console.log("Image _id:", _id);
    console.log("Image URL:", url);
    console.log("Image Public ID:", publicId);
    if (!file) {
      console.error("No image file found for upload.");
      return;
    }
    const formData = new FormData();
    formData.append("imagepublicId", publicId);
    formData.append("imageFile", file);

    try {
      const response = await axios.put(
        `${API_URL_Vehicle_For_Image}/${_id}`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      console.log(response)
      fetchVehicleData();
      setStep(1);
    } catch (error) {
      console.error("Error updating vehicle image:", error);
    }
  };

  if (!isOpen) return null;

  const resetForm = () => {
    setStep(1);
  };

  const nextStep = () => setStep(step + 1);
  const prevStep = () => setStep(step - 1);

  const handleMaintenanceToggle = (e) => {
    const { checked } = e.target;
    setMaintenance(!maintenance);
    setVehicleData((prevData) => ({
      ...prevData,
      maintenance: checked,
    }));
  };

  const handleAddMoreParts = () => {
    setVehicleData((prevData) => ({
      ...prevData,
      parts: [
        ...prevData.parts,
        { partNumber: "", partName: "", partprice: "", partsupplier: "" },
      ],
    }));
  };

  const handleRemovePart = (index) => {
    setVehicleData((prevData) => {
      const updatedParts = [...prevData.parts];
      updatedParts.splice(index, 1);
      return {
        ...prevData,
        parts: updatedParts,
      };
    });
  };

  const handlePartChange = (index, e) => {
    const { name, value } = e.target;
    const updatedParts = [...vehicleData.parts];
    updatedParts[index][name] = value;

    setVehicleData((prev) => ({
      ...prev,
      parts: updatedParts,
    }));
  };

  const handleSelfFitsettingToggle = () => {
    setSelfFitSetting(!selfFitSetting);
  };

  const handleImage = () => {
    fileInput.current.click()
  };

  const years = Array.from({ length: 2012 - 1999 }, (_, index) => 2013 + index);

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50 z-50 ">
      <div className="bg-white p-12 rounded-xl shadow-lg w-full max-w-4xl overflow-y-auto max-h-screen">

        <div className="flex items-center justify-between mb-2">
          <h2 className="text-2xl font-bold">
            Update Vehicle Form
          </h2>

          <Image width={15} height={15} alt="cross" src="/crossIcon.svg" className="cursor-pointer" onClick={() => {
            onClose();
            setStep(1);
          }} />
        </div>

        <form onSubmit={handleSubmit} className="max-w-4xl mx-auto p-6">
          {step === 1 && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-x-2 gap-y-4">

                <div>
                  <div className="flex gap-1">
                    <label
                      className="text-[10px]"
                    >
                      Registration Number <span className="text-red-600">*</span>
                    </label>
                  </div>
                  <input
                    type="text"
                    name="registrationNumber"
                    value={vehicleData.registrationNumber}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    required placeholder="Registration Number"
                  />
                </div> 

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Road Tax Due Date</label>
                  </div>
                  <input
                    type="date"
                    name="roadTaxDate"
                    value={vehicleData.roadTaxDate}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    required
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label htmlFor="year" className="text-[10px]">
                      Year<span className="text-red-600">*</span>
                    </label>
                  </div>
                  <select
                    name="year"
                    value={vehicleData.year}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    required
                  >
                    <option value="" disabled>Select Year</option>
                    {years.map((year) => (
                      <option className=" text-xs" key={year} value={year}>{year}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Engine Size (cc)</label>
                  </div>
                  <input
                    type="number"
                    name="enginesize"
                    value={vehicleData.enginesize}
                    onChange={handleChange}
                    placeholder="Enter Engine Size"
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label
                      htmlFor="Fuel_Type"
                      className="text-[10px]"
                    >
                      Fuel Type <span className="text-red-600">*</span>
                    </label>
                  </div>
                  <select
                    name="fuelType"
                    value={vehicleData.fuelType}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    required
                  >
                    <option value="" disabled>
                      Select fuel type
                    </option>

                    {fueltype
                      .filter((fuel) => fuel.isActive)  
                      .map((fuelType) => (
                        <option key={fuelType._id} value={fuelType.name}>
                          {fuelType.name}
                        </option>
                      ))}
                  </select>
                </div>

                <div>
                  <div className="flex gap-1">
                    <label
                      className="text-[10px]"
                    >
                      Exterior Color <span className="text-red-600">*</span>
                    </label>
                  </div>
                  <input
                    type="text"
                    name="exteriorColor"
                    value={vehicleData.exteriorColor}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    placeholder="e.g., Red, Blue"
                    required
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">MOT Due Date</label>
                  </div>
                  <input
                    type="date"
                    name="motDueDate"
                    value={vehicleData.motDueDate}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                  />
                </div>

                
                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Tax Status</label>
                  </div>
                  <input
                    type="text"
                    name="taxStatus"
                    value={vehicleData.taxStatus}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    required
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">MOT Status</label>
                  </div>
                  <input
                    type="text"
                    name="motStatus"
                    value={vehicleData.motStatus}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    required
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Co2 Emissions</label>
                  </div>
                  <input
                    name="co2Emissions" type="text" value={vehicleData.co2Emissions} onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    required
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Marked For Export</label>
                  </div>
                  <input
                    type="text"
                    name="markedForExport"
                    value={vehicleData.markedForExport}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    required
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Type Approval</label>
                  </div>
                  <input
                    type="text"
                    name="typeApproval"
                    value={vehicleData.typeApproval}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    required
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Date of last V5 C Issued</label>
                  </div>
                  <input
                    type="date" name="dateOfLastV5CIssued" value={vehicleData.dateOfLastV5CIssued} onChange={handleChange}

                    className="w-full p-2 border border-[#42506666] rounded shadow"
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Wheel Plan</label>
                  </div>
                  <input
                    type="text"
                    name="wheelplan"
                    value={vehicleData.wheelplan}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    required
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Month of First Registration</label>
                  </div>
                  <input
                    type="text"
                    name="monthOfFirstRegistration"
                    value={vehicleData.monthOfFirstRegistration}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    required
                  />
                </div>
                
              </div>

              <div className="mt-6 flex gap-2 justify-end">
                <button
                  type="button"
                  onClick={() => {
                    onClose();
                    resetForm();
                  }}
                  className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                >
                  Cancel
                </button>
                <button
                  onClick={nextStep}
                  className="bg-[#313342] text-white rounded-4 hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 py-1 px-8"
                >
                  Next
                </button>
              </div>
            </>
          )}

          {step === 2 && (
            <>
              <div>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-4">
                  <div>
                    <div className="flex gap-1">
                      <label htmlFor="manufacturer" className="text-[10px]">
                        Manufacturer <span className="text-red-600">*</span>
                      </label>
                    </div>
                    <select
                      id="manufacturer"
                      name="manufacturer"
                      value={vehicleData.manufacturer}
                      onChange={handleChange}
                      className="w-full p-2 border border-[#42506666] rounded-[4px]"
                      required
                    >
                      <option value="">Select Manufacturer</option>
                      {manufacturer
                        .filter((Manufacturer) => Manufacturer.isActive)
                        .map((Manufact) => (
                          <option key={Manufact._id} value={Manufact.name}>
                            {Manufact.name}
                          </option>
                        ))}
                    </select>
                  </div>

                  <div>
                    <div className="flex gap-1">
                      <label htmlFor="model" className="text-[10px]">
                        Model <span className="text-red-600">*</span>
                      </label>
                    </div>
                    <select
                      name="model"
                      value={vehicleData.model}
                      onChange={handleChange}
                      className="w-full p-2 border border-[#42506666] rounded shadow bg-white"
                      required
                    >
                      <option value="">Select a model</option>
                      {!selectedCarmodel.some((model) => model.name === vehicleData.model) && vehicleData.model && (
                        <option value={vehicleData.model}>{vehicleData.model}</option>
                      )}

                      {selectedCarmodel
                        .map((model) => (
                          <option key={model._id} value={model.name}>
                            {model.name}
                          </option>
                        ))}
                    </select>
                  </div>



                  <div>
                    <div className="flex gap-1">
                      <label className="text-[10px]">Transmission <span className="text-red-600">*</span>

                      </label>
                    </div>
                    <select
                      name="transmission"
                      value={vehicleData.transmission}
                      onChange={handleChange}
                      className="w-full p-2 border border-[#42506666] rounded-[4px]"
                      placeholder="e.g., Automatic"
                      required
                    >
                      <option value="" disabled>
                        Select Transmission
                      </option>

                      {transmission
                        .filter((tran) => tran.isActive) 
                        .map((trans) => (
                          <option key={trans._id} value={trans.name}>
                            {trans.name}
                          </option>
                        ))}
                    </select>
                  </div>

                  <div>
                    <div className="flex gap-1">
                      <label className="text-[10px]">
                        Passenger Capacity <span className="text-red-600">*</span>
                      </label>
                    </div>
                    <select
                      name="passengerCapacity"
                      value={vehicleData.passengerCapacity}
                      onChange={handleChange}
                      className="w-full p-2 border border-[#42506666] rounded-[4px]"
                      required
                    >
                      <option value="" disabled>
                        Select Capacity
                      </option>
                      {["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"].map((val, i) => (
                        <option key={i} value={val}>
                          {val}
                        </option>
                      ))}
                    </select>
                  </div>



                  <div>
                    <div className="flex gap-1">
                      <label className="text-[10px]">
                        Cargo Capacity
                      </label>
                    </div>
                    <input
                      type="text"
                      name="cargoCapacity"
                      value={vehicleData.cargoCapacity}
                      onChange={handleChange}
                      className="w-full p-2 border border-[#42506666] rounded-[4px]"
                    />
                  </div>

                  <div>
                    <div className="flex gap-1">
                      <label className="text-[10px]">Horsepower</label>
                    </div>
                    <input
                      type="number"
                      name="horsepower"
                      value={vehicleData.horsepower}
                      onChange={handleChange}
                      className="w-full p-2 border border-[#42506666] rounded-[4px]"
                    />
                  </div>

                  <div>
                    <div className="flex">
                      <label className="text-[10px]">
                        Fuel Efficiency
                      </label>
                    </div>

                    <input
                      type="text"
                      name="fuelEfficiency"
                      value={vehicleData.fuelEfficiency}
                      onChange={handleChange}
                      className="w-full p-2 border border-[#42506666] rounded-[4px]"
                      placeholder="e.g., 25 MPG"
                    />
                  </div>

                  <div>
                    <div className="flex gap-1">
                      <label className="text-[10px]">Price (£) <span className="text-red-600">*</span>
                      </label>

                    </div>
                    <input
                      type="number"
                      name="price"
                      value={vehicleData.price}
                      onChange={handleChange}
                      className="w-full p-2 border border-[#42506666] rounded-[4px]"
                      required
                    />
                  </div>

                  

                  <div>
                    <div className="flex gap-1">
                      <label className="text-[10px]">Body Type <span className="text-red-600">*</span>
                      </label>
                    </div>

                    <select
                      name="type"
                      value={vehicleData.type}
                      onChange={handleChange}
                      className="w-full p-2 border border-[#42506666] rounded-[4px]"
                      required
                    >
                      <option value="" disabled>
                        Select type
                      </option>

                      {type
                        .filter((t) => t.isActive)
                        .map((ty) => (
                          <option key={ty._id} value={ty.name}>
                            {ty.name}
                          </option>
                        ))}
                    </select>
                  </div>

                  <div>
                    <div className="flex gap-1">
                      <label className="text-[10px]">Drivetrain <span className="text-red-600">*</span>

                      </label>
                    </div>
                    <select
                      name="drivetrain"
                      value={vehicleData.drivetrain}
                      onChange={handleChange}
                      className="w-full p-2 border border-[#42506666] rounded-[4px]"
                      required
                    >
                      <option value="" disabled>
                        Select drivetrain
                      </option>
                      <option value="FWD">Front-wheel drive (FWD)</option>
                      <option value="RWD">Rear-wheel drive (RWD)</option>
                      <option value="AWD">All-wheel drive (AWD)</option>
                      <option value="4WD">Four-wheel drive (4WD)</option>
                    </select>
                  </div>

                  <div>
                    <div className="flex gap-1">
                      <label className="text-[10px]">Engine Type <span className="text-red-600">*</span>

                      </label>
                    </div>
                    <input
                      type="text"
                      name="engineType"
                      value={vehicleData.engineType}
                      onChange={handleChange}
                      className="w-full p-2 border border-[#42506666] rounded-[4px]"
                      placeholder="e.g., 2.5L 4-Cylinder"
                      required
                    />
                  </div>
                </div>

                <div>
                  <div>
                    <label
                      className="text-[10px]"
                    >
                      Safety Features
                    </label>
                  </div>
                  <div className="">
                    <Select
                      isMulti
                      options={options}
                      value={selectedOptions}
                      onChange={handleChangesafty}
                      placeholder="Select features..."
                      className="react-select w-full border border-[#42506666] rounded shadow"
                      classNamePrefix="select"
                    />
                  </div>
                </div>

                <div className="flex flex-wrap gap-2 mt-1">
                  {option.map((feature, index) => (
                    <span
                      key={index}
                      className="bg-blue-100 text-blue-800 px-2 py-1 text-xs rounded"
                    >
                      {feature}
                      <button
                        onClick={() => {
                          const updated = option.filter((item) => item !== feature);
                          setoptions(updated);
                        }}
                        className="ml-2 text-red-500 hover:text-red-700 font-bold"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>

                <div className="">
                  <div>
                    <label
                      className="text-[10px]"
                    >
                      Technology Features
                    </label>
                  </div>
                  <div className="">

                    <Select
                      isMulti
                      options={featurestech}
                      value={selectedOptionstech}
                      onChange={handleChangestech}
                      placeholder="Select Tech..."
                      className="react-select w-full border border-[#42506666] rounded  shadow"
                      classNamePrefix="select"
                    />
                  </div>
                </div>

                <div className="flex flex-wrap gap-2 mt-1">
                  {optiontech.map((tech, index) => (
                    <span
                      key={index}
                      className="bg-blue-100 text-blue-800 px-2 py-1 text-xs rounded"
                    >
                      {tech}
                      <button
                        onClick={() => {
                          const updated = optiontech.filter((item) => item !== tech);
                          setoptionstech(updated);
                        }}
                        className="ml-2 text-red-500 hover:text-red-700 font-bold"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>


              </div>

              <div className="mt-6 flex gap-2 justify-between">
                <button
                  onClick={prevStep}
                  className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                >
                  Back
                </button>
                <div className="flex justify-end gap-2">
                  <button
                    type="button"
                    onClick={() => {
                      onClose();
                      resetForm();
                    }}
                    className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                  >
                    Close
                  </button>
                  <button
                    onClick={nextStep}
                    className="bg-[#313342] text-white rounded-4 hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 py-1 px-8"
                  >
                    Next
                  </button>
                </div>
              </div>
            </>
          )}

          {step === 3 && (
            <>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-4">
                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">
                      Vin / Chasis Number
                    </label>
                  </div>
                  <input
                    type="text"
                    name="chasisnumber"
                    value={vehicleData.chasisnumber}
                    onChange={handleChange}
                    placeholder="Enter Engine Size"
                    className="w-full p-2 border border-[#42506666] rounded-[4px]"
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">
                      Interior Color  <span className="text-red-600">*</span>
                    </label>
                  </div>
                  <input
                    type="text"
                    name="interiorColor"
                    value={vehicleData.interiorColor}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded-[4px]"
                    placeholder="e.g., Black, Beige"
                    required
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Height(mm)</label>
                  </div>
                  <input
                    type="number"
                    name="height"
                    value={vehicleData.height}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded-[4px]" placeholder="Height in millimeter"
                  />
                </div>
                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Width (mm)</label>
                  </div>
                  <input
                    type="number"
                    name="width"
                    value={vehicleData.width}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded-[4px]" placeholder="Width in millimeter"
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Length (in)</label>
                  </div>
                  <input
                    type="number"
                    name="length"
                    value={vehicleData.length}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded-[4px]" placeholder="Length in inch"
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">Doors</label>
                  </div>
                  <input
                    type="number"
                    name="doors"
                    value={vehicleData.doors}
                    onChange={handleChange}
                    placeholder="Enter Engine Size"
                    className="w-full p-2 border border-[#42506666] rounded-[4px]"
                  />
                </div>

                <div>
                  <label className="text-[10px]">Road Tax Cycle</label>
                  <select
                    name="roadTaxCycle"
                    value={vehicleData.roadTaxCycle}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded-[4px]"
                  >
                    <option value="">Select Cycle</option>
                    <option value="3months">3 Months</option>
                    <option value="6months">6 Months</option>
                    <option value="1year">1 Year</option>
                  </select>
                </div>

                <div className="relative">
                  <label className="text-[10px]">Road Tax Cost</label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-600">£</span>
                    <input
                      type="number"
                      name="roadTaxCost"
                      value={vehicleData.roadTaxCost}
                      onChange={handleChange}
                      className="w-full p-2 pl-6 border border-[#42506666] rounded shadow"
                      placeholder="Enter road tax cost"
                    />
                  </div>
                </div>



                <div>
                  <label className="text-[10px]">MOT Cycle</label>
                  <select
                    name="motCycle"
                    value={vehicleData.motCycle}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                  >
                    <option value="">Select Cycle</option>
                    <option value="3months">3 Months</option>
                    <option value="6months">6 Months</option>
                    <option value="1year">1 Year</option>
                  </select>
                </div>

                <div>
                  <label className="text-[10px]">ABI Code</label>
                  <input
                    type="text"
                    name="abiCode"
                    value={vehicleData.abiCode}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    placeholder="Enter ABI Code"
                  />
                </div>

                <div>
                  <label className="text-[10px]">
                    Next Service Date
                  </label>
                  <input
                    type="date"
                    name="nextServiceDate"
                    value={vehicleData.nextServiceDate}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                  />
                </div>
                <div>
                  <label className="text-[10px]">
                    Next Service Miles
                  </label>
                  <input
                    type="number"
                    name="nextServiceMiles"
                    value={vehicleData.nextServiceMiles}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded shadow"
                    placeholder="Enter miles for next service"
                  />
                </div>
              </div>

              <div className="mt-6 flex gap-2 justify-between">
                <button
                  onClick={prevStep}
                  className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                >
                  Back
                </button>
                <div className="flex justify-end gap-[10px]">
                  <button
                    type="button"
                    onClick={() => {
                      onClose();
                      resetForm();
                    }}
                    className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={nextStep}
                    className="bg-[#313342] text-white rounded-4 hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 py-1 px-8"
                  >
                    Next
                  </button>
                </div>
              </div>
            </>
          )}

          {step === 4 && (
            <>
              <h2 className="font-bold mb-4">
                Financials Information
              </h2>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-4 mb-2">
                <div className="mb-1">
                  <label className="text-[10px]">
                    List Price (P11D)
                  </label>
                  <input
                    type="number"
                    name="listPrice"
                    value={vehicleData.listPrice}
                    onChange={handleChange}
                    placeholder="List Price"
                    className="w-full p-2 border border-[#42506666] rounded-[4px]"
                  />
                </div>

                <div className="mb-1">
                  <label className="text-[10px]">
                    Purchase Price
                  </label>
                  <input
                    type="number"
                    name="purchasePrice"
                    value={vehicleData.purchasePrice}
                    onChange={handleChange}
                    placeholder="Purchase Price"
                    className="w-full p-2 border border-[#42506666] rounded-[4px]"
                  />
                </div>

                <div className="mb-1">
                  <label className="text-[10px]">
                    Insurance Value
                  </label>
                  <input
                    type="number"
                    name="insuranceValue"
                    value={vehicleData.insuranceValue}
                    onChange={handleChange}
                    placeholder="Insurance Value"
                    className="w-full p-2 border border-[#42506666] rounded-[4px]"
                  />
                </div>

                <div className="mb-1">
                  <label className="text-[10px]">
                    Department Code
                  </label>
                  <input
                    type="text"
                    name="departmentCode"
                    value={vehicleData.departmentCode}
                    onChange={handleChange}
                    placeholder="Department Code"
                    className="w-full p-2 border border-[#42506666] rounded-[4px]"
                  />
                </div>
              </div>

              <div className="mb-4">
                <label className="block text-gray-700 font-semibold mb-1">
                  <input
                    type="checkbox"
                    name="maintenance"
                    checked={maintenance}
                    onChange={handleMaintenanceToggle}
                    className="mr-2"
                  />
                  Maintenance Record (if any)
                </label>
              </div>
              {maintenance && (
                <>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-4">
                    <div className="mb-4 col-span-2">
                      <label className="text-[10px]">
                        Issues / Damage
                      </label>
                      <input
                        type="text"
                        name="issues_damage"
                        value={vehicleData.issues_damage}
                        onChange={handleChange}
                        placeholder="Describe"
                        className="w-full p-2 border border-[#42506666] rounded-[4px]"
                      />
                    </div>

                    <div className="mb-4 col-span-1">
                      <label className="text-[10px]">
                        Damage Image
                      </label>
                    {/* changes */}
                      <input
                        type="file"
                        id="damage_image"
                        name="damage_image"
                        accept="image/*"
                        onChange={handleChange}
                        className=" block w-48 text-[8px] text-gray-400 file:mr-4 file:py-1 p-2 file:px-4 file:rounded-lg file:border file:text-[10px] file:font-semibold file:bg-white hover:file:bg-blue-100 border border-[#0885864D] rounded-[10px] border-dashed "
                      />

                    

                      {maintenance ? (
                        <div className="mt-3">
                          <div>
                            <input
                              type="file"
                              onChange={handleFileSelect}
                              accept="image/*"
                              ref={fileInputRef}
                              style={{ display: "none" }}
                            />


                            {damagePreview.map((dimg, index) => (
                              <div
                                key={index}
                                className="cursor-pointer"
                                onClick={() => handleImageClick(dimg)} 
                              >
                                <Image width={124} height={80}
                                  src={dimg.url}
                                  alt="Avatar Preview"
                                  className="avatar-preview w-32 h-20"
                                />
                              </div>
                            ))}

                          </div>
                        </div>
                      ) : null}



                    </div>

                    <div className="mb-4">
                      <label className="text-[10px]">
                        Recovery
                      </label>
                      <input
                        type="text"
                        name="recovery"
                        value={vehicleData.recovery}
                        onChange={handleChange}
                        placeholder="Recovery"
                        className="w-full p-2 border border-[#42506666] rounded-[4px]"
                      />
                    </div>

                    <div className="mb-4">
                      <label className="text-[10px]">
                        Repair Status
                      </label>
                      <input
                        type="text"
                        name="repairStatus"
                        value={vehicleData.repairStatus}
                        onChange={handleChange}
                        placeholder="Repair status"
                        className="w-full p-2 border border-[#42506666] rounded-[4px]"
                      />
                    </div>

                    <div className="mb-4">
                      <label className="text-[10px]">
                        Job Number
                      </label>
                      <input
                        type="text"
                        name="jobNumber"
                        value={vehicleData.jobNumber}
                        onChange={handleChange}
                        placeholder="Job number"
                        className="w-full p-2 border border-[#42506666] rounded-[4px]"
                      />
                    </div>

                    <div className="mb-4">
                      <label className="text-[10px]">
                        Organization
                      </label>
                      <select
                        name="organization"
                        value={vehicleData.organization}
                        onChange={handleChange}
                        className="w-full p-2 border border-[#42506666] rounded-[4px]"
                      >
                        <option value="">Select Organization</option>
                        <option value="Organization1">Organization 1</option>
                        <option value="Organization2">Organization 2</option>
                      </select>
                    </div>

                    <div className="mb-4">
                      <label className="text-[10px]">
                        Memo
                      </label>
                      <input
                        type="text"
                        name="memo"
                        value={vehicleData.memo}
                        onChange={handleChange}
                        placeholder="Memo for repair"
                        className="w-full p-2 border border-[#42506666] rounded-[4px]"
                      />
                    </div>
                  </div>
                  <h3 className="font-semibold text-gray-700 mb-2">Parts</h3>
                  
                  {vehicleData.parts.map((part, index) => (
                    <div
                      key={index}
                      className="grid grid-cols-2 md:grid-cols-2 gap-2 mt-4 mb-2 relative"
                    >
                      <div>
                        <input
                          type="text"
                          name="partNumber"
                          value={part.partNumber}
                          onChange={(e) => handlePartChange(index, e)}
                          placeholder="Part Number"
                          className="w-full p-2 border border-[#42506666] rounded shadow mb-2"
                        />
                      </div>

                      <div>
                        <input
                          type="text"
                          name="partName"
                          value={part.partName}
                          onChange={(e) => handlePartChange(index, e)}
                          placeholder="Part Name"
                          className="w-full p-2 border border-[#42506666] rounded shadow mb-2"
                        />
                      </div>

                      <div>
                        <input
                          type="number"
                          name="partprice"
                          value={part.partprice}
                          onChange={(e) => handlePartChange(index, e)}
                          placeholder="Price"
                          className="w-full p-2 border border-[#42506666] rounded shadow mb-2"
                        />
                      </div>

                      <div>
                        <select
                          name="partsupplier"
                          value={part.partsupplier}
                          onChange={(e) => handlePartChange(index, e)}
                          className="w-full p-2 border border-[#42506666] rounded shadow mb-2"
                        >
                          <option value="">Select Supplier</option>
                          {suppliers
                            ?.filter((sup) => sup.isActive === true)
                            .map((supplier) => (
                              <option key={supplier._id} value={supplier.name}>
                                {supplier.name}
                              </option>
                            ))}
                        </select>
                      </div>

                      {index !== 0 && (
                        <button
                          type="button"
                          onClick={() => handleRemovePart(index)}
                          className="absolute -top-5 -right-5 text-red-500 hover:text-red-700"
                          title="Remove this part"

                        >
                          ❌
                        </button>
                      )}

                    </div>
                  ))}

                  <button
                    type="button"
                    onClick={handleAddMoreParts}
                    className="text-xs px-3 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
                  >
                    + Add More Parts
                  </button>
                </>
              )}
              <h2 className="font-bold mb-4">Commercial Vehicles</h2>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-4 mb-2">
                <div>
                  <label className="text-[10px]">RPC Expiry Date</label>
                  <input
                    type="date"
                    name="RPCExpiryDate"
                    value={vehicleData.RPCExpiryDate}
                    onChange={handleChange}
                    className="w-full border border-[#42506666] rounded-[4px] p-2"
                  />
                </div>

                <div>
                  <label className="text-[10px]">
                    Tail-Lift Expiry Date
                  </label>
                  <input
                    type="date"
                    name="TailLiftExpiryDate"
                    value={vehicleData.TailLiftExpiryDate}
                    onChange={handleChange}
                    className="w-full border border-[#42506666] rounded-[4px] p-2"
                  />
                </div>

                <div>
                  <label className="text-[10px]">
                    Fork Lift Inspection Date
                  </label>
                  <input
                    type="date"
                    name="ForkLiftInspectionDate"
                    value={vehicleData.ForkLiftInspectionDate}
                    onChange={handleChange}
                    className="w-full border border-[#42506666] rounded-[4px] p-2"
                  />
                </div>

                <div>
                  <label className="text-[10px]">
                    Fork Lift Inspection Number/Notes
                  </label>
                  <input
                    type="text"
                    name="ForkLiftInspectionNumberNotes"
                    value={vehicleData.ForkLiftInspectionNumberNotes}
                    onChange={handleChange}
                    placeholder="Enter inspection number or notes"
                    className="w-full border border-[#42506666] rounded-[4px] p-2"
                  />
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <label className="text-[10px]">
                  <input
                    type="checkbox"
                    name="selfFitSetting"
                    onChange={handleSelfFitsettingToggle}
                    checked={selfFitSetting}
                    className="mr-2"
                  />
                  Self-Fit Setting
                </label>
              </div>

              {selfFitSetting && (
                <div>
                  <label className="block font-semibold">
                    Additional Info
                  </label>
                  <textarea
                    value={vehicleData.additionalInfo}
                    onChange={handleChange}
                    placeholder="Enter any additional info"
                    className="w-full border border-[#42506666] rounded-[4px] p-2"
                    rows="2"
                  />
                </div>
              )}

              <div className="mt-6 flex gap-2 justify-between">
                <button
                  onClick={prevStep}
                  className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                >
                  Back
                </button>
                <div className="flex justify-end gap-2">
                  <button
                    type="button"
                    onClick={() => {
                      onClose();
                      resetForm();
                    }}
                    className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                  >
                    Close
                  </button>
                  <button
                    onClick={nextStep}
                    className="bg-[#313342] text-white rounded-4 hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 py-1 px-8"
                  >
                    Next
                  </button>
                </div>
              </div>
            </>
          )}
          {step === 5 && (
            <>
              <h2 className="font-bold mb-4">Local Authority</h2>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                <div>
                  <div>
                    <label htmlFor="taxiFirm" className="text-[10px]">
                      Taxi Local Authoritcy <span className="text-red-600">*</span>
                    </label>

                  </div>
                  <select
                    id="LocalAuthority"
                    name="LocalAuthority"
                    value={vehicleData.LocalAuthority}
                    onChange={handleChange}
                    className="w-full p-2 border border-[#42506666] rounded-[4px]"
                    required
                  >
                    <option value="">Select Local Authority</option>

                    {localAuthority
                      .filter((a) => a.isActive)
                      .map((auth) => (
                        <option key={auth._id} value={auth.name}>
                          {auth.name}
                        </option>
                      ))}
                  </select>
                </div>
                <div>
                  <label className="text-[10px]">Test Date</label>
                  <input
                    type="date"
                    name="TestDate"
                    value={vehicleData.TestDate}
                    onChange={handleChange}
                    className="w-full border border-[#42506666] rounded-[4px] p-2"
                  />
                </div>

                <div>
                  <label className="text-[10px]">
                    Plate Expiry Date
                  </label>
                  <input
                    type="date"
                    name="PlateExpiryDate"
                    value={vehicleData.PlateExpiryDate}
                    onChange={handleChange}
                    className="w-full border border-[#42506666] rounded-[4px] p-2"
                  />
                </div>

                <div>
                  <label className="text-[10px]">Insurance</label>
                  <input
                    type="text"
                    name="Insurance"
                    value={vehicleData.Insurance}
                    onChange={handleChange}
                    placeholder="Enter insurance details"
                    className="w-full border border-[#42506666] rounded-[4px] p-2"
                  />
                </div>

                <div>
                  <label className="text-[10px]">
                    Insurance Policy Number
                  </label>
                  <input
                    type="text"
                    name="insurancePolicyNumber"
                    value={vehicleData.insurancePolicyNumber}
                    onChange={handleChange}
                    placeholder="Enter policy number"
                    className="w-full border border-[#42506666] rounded-[4px] p-2"
                  />
                </div>

                <div>
                  <label className="text-[10px]">
                    Picture/PDF of Policy:
                  </label>
                  <input
                    type="file"
                    name="PDFofPolicy"
                    onChange={handleChange}

                    accept="application/pdf, image/*"
                    className="block w-48 text-[8px] text-gray-400 file:mr-4 file:py-1 p-2 file:px-4 file:rounded-lg file:border file:text-[10px] file:font-semibold file:bg-white hover:file:bg-blue-100 border border-[#0885864D] rounded-[10px] border-dashed "
                  />
                </div>

                <div className="mt-3">
                  <div>

                    <div className="flex flex-col gap-2 mb-2">
                      <div>
                          {pdfPreview?.endsWith(".pdf") ? (
                            <iframe
                              src={pdfPreview}
                              width="300px"
                              height="300px"
                              title="PDF Preview"
                            ></iframe>
                          ) : (
                            <img src={pdfPreview} alt="Preview" width="300px" height="300px" />
                          )}
                        </div>


                    </div>
                  </div>
                </div>
              </div>

              <h2 className="font-bold my-2">Defect Details</h2>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mb-2">
                <div>
                  <label className="text-[10px]">Defect</label>
                  <input
                    type="text"
                    name="defect"
                    value={vehicleData.defect}
                    onChange={handleChange}
                    placeholder="Enter defect name"
                    className="w-full border border-[#42506666] rounded-[4px] p-2"
                  />
                </div>

                <div>
                  <label className="text-[10px]">Date</label>
                  <input
                    type="date"
                    name="Defectdate"
                    value={vehicleData.Defectdate}
                    onChange={handleChange}
                    className="w-full border border-[#42506666] rounded-[4px] p-2"
                  />
                </div>

                <div>
                  <label className="text-[10px]">Status</label>
                  <select
                    name="defectstatus"
                    value={vehicleData.defectstatus}
                    onChange={handleChange}
                    className="w-full border border-[#42506666] rounded-[4px] p-2"
                  >
                    <option value="" disabled>
                      Select status
                    </option>
                    <option value="Pending">Pending</option>
                    <option value="InProgress">In Progress</option>
                    <option value="Resolved">Resolved</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-2 gap-2 mb-2">
                <div>
                  <label className="text-[10px]">Description</label>
                  <textarea
                    name="defectdescription"
                    value={vehicleData.defectdescription}
                    onChange={handleChange}
                    placeholder="Enter a brief description of the defect"
                    className="w-full border border-[#42506666] rounded-[4px] p-2 resize-none"
                    rows="2"
                  />
                </div>

                <div>
                  <label className="text-[10px]">Action</label>
                  <textarea
                    name="defectaction"
                    value={vehicleData.defectaction}
                    onChange={handleChange}
                    placeholder="Describe the action taken or needed"
                    className="w-full border border-[#42506666] rounded-[4px] p-2 resize-none"
                    rows="2"
                  />
                </div>
              </div>

              <div>
                <h2 className="block font-semibold">Update Car Documents</h2>
                <div>
                  {" "}
                  <div className="image-file-inputs">
                    <Image width={100} height={100}
                      src="https://www.freeiconspng.com/uploads/file-add-icon-20.png"
                      alt="Add new file input"
                      onClick={handleImage}
                      className="cursor-pointer mt-3 w-20 h-20 border-2 border-dashed border-gray-400 hover:border-gray-600"
                    />
                    <div className="flex gap-2">
                      <input
                        ref={fileInput}
                        name="cardocuments"
                        type="file"
                        onChange={handleChange}
                        style={{ display: "none" }}
                        multiple
                      />
                    </div>
                    <span className="block text-red-500 mt-2 text-xs">
                      You can select multiple documents at once (up to 5 images)
                    </span>

                    <div className="mt-3">
                      <div>Car Documents</div>
                      <div>
                        <input
                          type="file"
                          onChange={handleFileSelect}
                          accept="image/*"
                          ref={fileInputRef}
                          style={{ display: "none" }} 
                        />

                        <div className="grid grid-cols-3 md:grid-cols-6 gap-2  mb-2">
                          {cardocumentimagePreview.map((img, index) => (
                            <div
                              key={index}
                              className="cursor-pointer"
                              onClick={() => handleImageClick(img)} 
                            >
                              <Image width={124} height={20}
                                src={img.url}
                                alt="Car Document Preview"
                                className="avatar-preview w-32 h-20"
                              />
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-4 mb-2">
                <div className="flex flex-col">
                  <div className="flex gap-1 items-center">
                    <label className="block font-semibold">
                      Warranty Information
                    </label>
                    <span className="text-red-600">*</span>
                  </div>
                  <textarea
                    name="warrantyInfo"
                    value={vehicleData.warrantyInfo}
                    onChange={handleChange}
                    className="border border-[#42506666] rounded-[4px]-lg p-3 resize-none" 
                    placeholder="e.g., 3 years or 36,000 miles"
                    required
                  />
                </div>
                <div className="">
                  <label className="block font-semibold">Vehicle Images</label>

                  <input
                    type="file"
                    id="imageFiles"
                    name="imageFiles"
                    onChange={handleChange}
                    className="mt-1 block w-48 text-[8px] text-gray-400 file:mr-4 file:py-1 p-2 file:px-4 file:rounded-lg file:border file:text-[10px] file:font-semibold file:bg-white hover:file:bg-blue-100 border border-[#0885864D] rounded-[10px] border-dashed "
                    multiple

                  />
                  <span className="block text-red-500 mt-2 text-xs">
                    Maximum 10 images
                  </span>
                </div>
              </div>

              <div className="mt-3">
                <div>Vehicle Images</div>
                <div>
                  <input
                    type="file"
                    onChange={handleFileSelect}
                    accept="image/*"
                    ref={fileInputRef}
                    style={{ display: "none" }}
                  />

                  <div className="grid grid-cols-3 md:grid-cols-6 gap-2  mb-2">
                    {imagePreview.map((img, index) => (
                      <div
                        key={index}
                        className="cursor-pointer"
                        onClick={() => handleImageClick(img)} 
                      >
                        <Image width={132} height={80}
                          src={img.url}
                          alt="Avatar Preview"
                          className="avatar-preview w-32 h-20"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div>
                <label className="block font-medium mb-2">Status:</label>
                <div className="flex gap-4">
                  <label className="flex items-center gap-2">
                    <input
                      type="radio"
                      name="isActive"
                      value="true"
                      checked={vehicleData.isActive === true}
                      onChange={() =>
                        handleChange({
                          target: { name: "isActive", value: true },
                        })
                      }
                      className="accent-green-500"
                    />
                    <span>Active</span>
                  </label>

                  <label className="flex items-center gap-2">
                    <input
                      type="radio"
                      name="isActive"
                      value="false"
                      checked={vehicleData.isActive === false}
                      onChange={() =>
                        handleChange({
                          target: { name: "isActive", value: false },
                        })
                      }
                      className="accent-red-500"
                    />
                    <span>Inactive</span>
                  </label>
                </div>
              </div>
              

              <div className="mt-6 flex gap-2 justify-between">
                <button
                  onClick={prevStep}
                  className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                >
                  Back
                </button>
                <div className="flex justify-end gap-2">
                  <button
                    type="button"
                    onClick={() => {
                      onClose();
                      resetForm();
                    }}
                    className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                  >
                    Close
                  </button>
                  <button className="bg-[#313342] text-white rounded-4 hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 py-1 px-8" >
                    Submit
                  </button>
                </div>
              </div>
            </>
          )}
        </form>
      </div>
    </div>
  );
};

export default UpdateVehicleModel;