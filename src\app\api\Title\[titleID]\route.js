import { connect } from "@config/db.js";
import Title from "@models/Title/Title.Model.js";
import { NextResponse } from "next/server";

export const PUT = async (request, context) => {
  try {
    await connect(); // Connect to the database

    const id = context.params.TitleID; // Extract ManufacturerID from params
    const data = await request.json(); // Get the form data

    // Destructure the necessary fields
    const { name, description, isActive } = data;

    // Find the manufacturer by ID
    const title = await Title.findById({ _id: id });

    if (!title) {
      return NextResponse.json({
        error: "Title not found",
        status: 404,
      });
    }

    // Update manufacturer properties with values from formDataObject or retain existing values
    title.name = name ? name.trim() : title.name; // Update name or retain existing
    title.description = description ? description.trim() : title.description; // Update description or retain existing
    title.isActive = isActive !== undefined ? isActive : title.isActive; // Ensure isActive is treated correctly

    // Save the updated manufacturer
    await authority.save();

    return NextResponse.json({
      message: "Title details updated successfully",
      manufacturer, // Return the updated manufacturer object
      status: 200,
    });
  } catch (error) {
    console.error("Error updating Title:", error);
    return NextResponse.json({
      error: "Failed to update Title",
      status: 500,
    });
  }
};

// GET handler for retrieving a specific manufacturer by ID
export const GET = async (request, context) => {
  try {
    // Connect to the database
    await connect();

    // Extract the Manufacturer ID from the request parameters
    const id = context.params.TitleID; // Use context.params for accessing the parameters

    // Find the manufacturer by ID
    const Find_Title = await Title.findById({ _id: id });

    // Check if the manufacturer exists
    if (!Find_Title) {
      return NextResponse.json({
        result: "No Title Found",
        status: 404,
      });
    }

    // Return the found manufacturer as a JSON response
    return NextResponse.json({ result: Find_Title, status: 200 });
  } catch (error) {
    console.error("Error fetching Title:", error); // Log the error for debugging
    return NextResponse.json({
      result: "Failed to fetch Title",
      status: 500,
    });
  }
};
// DELETE handler for deleting a manufacturer
export const DELETE = async (request, { params }) => {
  try {
    // Connect to the database
    await connect();

    const { TitleID } = params; // Access the ManufacturerID from params

    // Find and delete the manufacturer
    const deletedManufacturer = await Title.findByIdAndDelete(TitleID);

    if (!deletedManufacturer) {
      return NextResponse.json({
        error: "Title not found",
        status: 404,
      });
    }

    return NextResponse.json({
      message: "Title deleted successfully",
      success: true,
      status: 200,
    });
  } catch (error) {
    console.error("Error deleting Title:", error);
    return NextResponse.json({
      error: "An error occurred while deleting the Title",
      status: 500,
    });
  }
};
