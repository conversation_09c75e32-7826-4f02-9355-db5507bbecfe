# Dependencies
node_modules/

# Build output
/.next
.next/
out/
build/

# Environment variables
.env
.env.*
*.env

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# OS / Editor files
.DS_Store
*.pem
*.key
*.crt

# Vercel deployment
.vercel/

# TypeScript build info
*.tsbuildinfo
next-env.d.ts

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env*
*.env
.env.local
.env.simple
.env.test
.env.production

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache



# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
# Comment in the public line in if your project uses Gatsby and not Next.js
# https://nextjs.org/blog/next-9-1#public-directory-support
# public

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# End of https://mrkandreev.name/snippets/gitignore-generator/#Node

# Next.js build output
/.next

.early.coverage