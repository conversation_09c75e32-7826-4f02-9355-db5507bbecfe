{"name": "carmanagementsystem", "version": "0.1.0", "private": true, "scripts": {"dev": "concurrently \"node scripts/tcp-server.js\" \"next dev -p 4000\"", "vms": "concurrently \"node scripts/tcp-server.js\" \"next start -p 4000\"", "build": "next build", "start": "next start -p 4000", "lint": "next lint", "check-expiry": "node -e \"import('./app/api/_init.js').then(m => m.initializeApp())\""}, "dependencies": {"@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@tailwindcss/typography": "^0.5.15", "axios": "^1.7.7", "bcryptjs": "^2.4.3", "chart.js": "^4.4.4", "cloudinary": "^2.5.0", "complete-teltonika-parser": "^0.3.6", "dotenv": "^16.4.5", "express": "^5.1.0", "formidable": "^3.5.4", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.2", "leaflet": "^1.9.4", "mongoose": "^8.6.3", "multer": "^2.0.1", "next": "14.2.11", "node-cron": "^3.0.3", "nodemailer": "^7.0.5", "pdfjs-dist": "^3.11.174", "primeflex": "^3.3.1", "primeicons": "^7.0.0", "primereact": "^10.8.3", "react": "^18", "react-chartjs-2": "^5.2.0", "react-data-table-component": "^7.6.2", "react-dom": "^18", "react-icons": "^5.5.0", "react-modal": "^3.16.3", "react-pdf": "^9.2.1", "react-select": "^5.8.3", "react-toastify": "^10.0.5", "react-transition-group": "^4.4.5", "recharts": "^2.12.7", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "teltonika-decoder": "^0.1.1", "uuid": "^11.0.3"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.20", "concurrently": "^9.2.0", "eslint": "^8", "eslint-config-next": "14.2.11", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5"}}