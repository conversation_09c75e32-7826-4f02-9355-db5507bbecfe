"use client";
import axios from "axios";
import Link from "next/link";
import React, { useState, useCallback, useEffect } from "react";


import LogoutModal from './LogoutModal';

import {
  getCompanyName,
  getUserId,
  getUserName,
  getUserRole,
  getflag,
  getCompanyId,
} from "../../../../utils/storageUtils";
import { useRouter } from "next/navigation";
import "react-toastify/dist/ReactToastify.css";
import { API_URL_Company, API_URL_USER, API_URL_CRONJOB } from "../Components/ApiUrl/ApiUrls";
import { isAuthenticated, clearAuthData } from "@/utils/verifytoken";
import {
  API_URL_VehicleMOT,
  API_URL_VehicleService,
  API_URL_VehicleRoadTex,
} from "../Components/ApiUrl/ApiUrls";
import Image from "next/image";

const Header = () => {
  const router = useRouter();
  const [companyName, setCompanyName] = useState("");
  const [role, setRole] = useState("");
  const [flag, setflag] = useState(false);
  const [username, setusername] = useState("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isPendingDropdown, setIsPendingDropdown] = useState(false);
  const [imagePreview, setImagePreview] = useState("");
  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);

  const [isLogoutModalOpen, setIsLogoutModalOpen] = useState(false);

  const fetchAllData = async () => {
    try {
      const [motResponse, serviceResponse, roadtaxResponse] = await Promise.all(
        [
          axios.get(API_URL_VehicleMOT),
          axios.get(API_URL_VehicleService),
          axios.get(API_URL_VehicleRoadTex),
        ]
      );

      const combinedData = [
        ...motResponse?.data?.Result,
        ...serviceResponse?.data?.Result,
        ...roadtaxResponse?.data?.Result,
      ];
      setData(combinedData);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };
  useEffect(() => {
    const filterData = data.filter(
      (item) =>
        username === item?.asignto &&
        companyName === item?.adminCompanyName &&
        (item?.motPending_Done === "1" ||
          item?.servicePending_Done === "1" ||
          item?.roadtexPending_Done === "1")
    );
    setFilteredData(filterData);
  }, [data, username]);
  useEffect(() => {
    const fetchData = async () => {
      try {
         await axios.get(`${API_URL_CRONJOB}`);
      } catch (error) {
        console.error("❌ Error updating data:", error);
      }
    };

    const interval = setInterval(fetchData, 3600000);
    fetchData();

    return () => clearInterval(interval);


  }, []);
  
  useEffect(() => {
    fetchAllData();
    if (!isAuthenticated()) {
      router.push("/");
      return;
    }

    const userId = getUserId();
    const companyId = getCompanyId();
    const flag = getflag();
    const username = getUserName();
    const role = getUserRole();
    const companyNameFromStorage = getCompanyName();

    setflag(flag);
    setusername(username);
    setRole(role);
    setCompanyName(companyNameFromStorage);

    const idToFetch =
      flag === "true" && companyNameFromStorage ? companyId : userId;
    showAllAdmins(idToFetch);
  }, []);

  const showAllAdmins = async (id) => {
    try {
      const res = await axios.get(`${API_URL_Company}/${id}`);
      const adminData = res?.data?.result;
      if (adminData?._id === id) {
        setImagePreview(adminData?.image);
      } else {
        const userRes = await axios.get(`${API_URL_USER}/${id}`);
        const userData = userRes?.data?.result;
        if (userData?.useravatar) {
          setImagePreview(userData?.useravatar);
        }
      }
    } catch (error) {
      console.error(`Error fetching data for user ${id}:`, error);
    }
  };

  const handleLogout = useCallback(async () => {
    if (isAuthenticated()) {
      clearAuthData();
      document.cookie =
        "token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
      router.push("/");
    } else {
      console.log("User is not authenticated");
    }
  }, [router]);
  const toggleDropdown = useCallback(() => {
    setIsDropdownOpen((prev) => !prev);
  }, []);

  const pendingDropdown = useCallback(() => {
    setIsPendingDropdown((prev) => !prev);
  });

  return (
    <header className=" relative z-50 text-black px-3  flex items-center font-sans justify-between  w-full drop-shadow-custom2 bg-[#222434ED]">
      <div className="flex flex-shrink-0 py-5 px-3  bg-transparent">
        <span className=" hidden sm:block lg:text-3xl md:text-2xl sm:text-lg font-bold text-white bg-transparent">
          Vehicle Management System
        </span>
        <span className="sm:hidden text-xl font-bold bg-transparent text-white">VMS</span>
      </div>

      <div className="flex items-center bg-transparent px-2 sm:px-5 relative">
        <div className="flex gap-4 bg-transparent">
          <div className="flex gap-2 relative bg-transparent">
            {role === "user" && filteredData?.length > 0 ? (
              <>
                <div
                  className="h-8 w-8 rounded-lg cursor-pointer bg-transparent"
                  onClick={pendingDropdown}
                >
                  <Image
                    src="/bell.png"
                    alt="notification"
                    height={25}
                    width={25}
                  />
                </div>

                {isPendingDropdown && (
                  <div className="absolute right-0 mt-12 flex flex-col bg-white rounded shadow-lg text-black w-80 p-4  border-2 border-gray-500">
                    <p>
                      You have been assigned a task. Please review the details.
                    </p>
                  </div>
                )}
              </>
            ) : (

              <Image
                src="/bell.svg"
                className="bg-transparent  "
                alt="no notification"
                height={25}
                width={25}
              />
            )}
          </div>

          <div className="bg-transparent">
            <h6 className="mr-4 font-sans text-lg font-medium hidden md:block bg-transparent text-white">
              {role === "superadmin" && flag === "false" ? (
                <div className="bg-transparent">
                  <p className="bg-transparent text-white">{username}</p>
                </div>
              ) : role === "superadmin" && flag === "true" ? (
                <p className="bg-transparent text-white">{companyName}</p>
              ) : role === "user" ? (
                <p className="bg-transparent text-white"> {username}</p>
              ) : (
                <p className="bg-transparent text-white"> {companyName}</p>
              )}
            </h6>
          </div>
        </div>

        <div className="relative bg-transparent">
          <div
            className=" h-[36px] w-[36px] md:h-[46px] md:w-[46px] cursor-pointer bg-transparent"
            onClick={toggleDropdown}
          >
            <Image
              src={imagePreview}
              alt="Profile"
              width={46}
              height={46}
              className="rounded-full h-full w-full object-cover object-center"
            />
          </div>


          {typeof window !== "undefined" && isDropdownOpen && (
            <div className=" border-[1px] border-white  absolute right-[3px] sm:right-[2px]  h-[60px] top-5 sm:top-6 mt-2 flex rounded-bl-[9px] rounded-tl-[9px] rounded-br-[9px] flex-col ] drop-shadow-custom  z-100 w-44">
              <ul className="rounded-tl-[9px] h-[60px] rounded-bl-[9px] rounded-br-[9px]  ">
                {(role === "superadmin" && flag === "false") ||
                  role === "admin" ||
                  role === "user" ? (
                  <Link href="/Dashboard/Profile">
                    <li className="px-4 font-sans text-sm font-semibold py-2 rounded-tl-[9px] h-[50%]  cursor-pointer  flex gap-2 items-center  hover:bg-drop-custom-bg  hover:text-white group">
                      <Image width={17} height={17} src="/profile.svg" className="  group-hover:invert "alt="profile"/>

                      Profile
                    </li>
                  </Link>
                ) : role === "superadmin" && flag === "true" ? (
                  <Link href="/Dashboard/CompanyProfile">
                    <li className="px-4 font-sans text-sm font-semibold py-2 rounded-tl-[9px] h-[50%]  cursor-pointer  flex gap-2 items-center  hover:bg-drop-custom-bg  hover:text-white group">
                      <Image width={17} height={17}  src="/profile.svg" className="group-hover:invert " alt="profile"/>
                      Profile
                    </li>
                  </Link>
                ) : (
                  <Link href="/Dashboard/CompanyProfile">
                    <li className="px-4 font-sans text-sm font-semibold py-2 rounded-tl-[9px] h-[50%]  cursor-pointer  flex gap-2 items-center  hover:bg-drop-custom-bg  hover:text-white group">
                      <Image width={17} height={17}  src="/profile.svg" className="h-[17px] w-[17px]  group-hover:invert " alt=""/>
                      Profile
                    </li>
                  </Link>
                )}

                <li
                  className="px-4 py-2 rounded-bl-[9px] w-full h-[50%] font-semibold font-sans text-sm rounded-br-[9px] hover:bg-drop-custom-bg cursor-pointer items-center gap-2  flex hover:text-white group"
                  onClick={
                    () => {
                      setIsLogoutModalOpen(true)
                    }
                  }
                >
                  <Image width={17} height={17}  className="group-hover:invert" src="/signOut.svg" alt="signout"/>
                  Logout
                </li>
              </ul>
            </div>
          )}
        </div>
      </div>
      <LogoutModal
        isOpen={isLogoutModalOpen}
        onClose={() => setIsLogoutModalOpen(false)}
        onLogout={handleLogout}
      />
    </header>
  );
};

export default Header;
