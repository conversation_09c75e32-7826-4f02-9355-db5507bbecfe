// models/Form.js
import mongoose from "mongoose";

// Define the schema
const VehicleDocumentsSchema = new mongoose.Schema({
  document: {
    type: String,
    trim: true,
    required: true,
  },
  documentExpire:{
    type:String,
    required:true
  },
  documentname: {
    type: String,
    trim: true,
  },
  Vehicleid:{
    type: mongoose.Schema.Types.ObjectId,
    ref:'Vehicle',
  },
  isActive: {
    type: Boolean,
    default: false, // Defaults to false if not specified
  },
  verificationStatus: {
    type: String,
    enum: ['pending', 'verified', 'rejected'],
    default: 'pending'
  },
  verifiedBy: {
    type: String,
    default: null
  },
  verifiedAt: {
    type: Date,
    default: null
  },
  adminCreatedBy: { type: String },
  adminCompanyName: { type: String },
  adminCompanyId: { type: mongoose.Schema.Types.ObjectId, ref: 'Company' },

   expiredNotificationSent: {
    type: Boolean,
    default: false
  },
  // For upcoming expiry
  upcomingNotificationSent: {
    type: Boolean,
    default: false
  },
  daysBeforeExpiry: {
    type: Number,
    default: 7 // Notify 7 days before expiry
  }
}, { timestamps: true });

// Create the model
export default mongoose.models.VehicleDocuments || mongoose.model("VehicleDocuments", VehicleDocumentsSchema);
