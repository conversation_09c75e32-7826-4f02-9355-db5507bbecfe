import nodemailer from 'nodemailer';

const transporter = nodemailer.createTransport({
  service: process.env.EMAIL_SERVICE,
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASSWORD
  }
});

export const sendExpiryEmail = async (vehicle, document) => {
  // console.log(vehicle.email);
  // console.log(vehicle?.Driverid?.email);
  const mailOptions = {
    from: `"Vehicle Docs System" <${process.env.EMAIL_FROM}>`,
    to: vehicle.email ,
    subject: `URGENT: ${document?.documentname} Expired for ${vehicle?.Vehicleid?.registrationNumber}`,
    html: `
      <div style="font-family: Arial, sans-serif; line-height: 1.6;">
        <h2 style="color: #d9534f;">⚠️ Document Expired Notification</h2>
        
        <p>Dear ${vehicle?.CompanyName},</p>
        
        <p>Your vehicle document has <strong>expired</strong>:</p>
        
        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
          <p><strong>Document Type:</strong> ${document?.documentname}</p>
          <p><strong>'Vehicle':</strong> ${vehicle?.Vehicleid?.registrationNumber}</p>
        </div>
        
        <p>Please renew this document immediately to avoid penalties.</p>
        
      
        
        <p style="margin-top: 20px;">
          <small>This is an automated message. Please do not reply.</small>
        </p>
      </div>
    `
  };

  await transporter.sendMail(mailOptions);
};
export const sendExpiryEmailtodriver = async (driver, document) => {
  // console.log(vehicle.email);
  // console.log(vehicle?.Driverid?.email);
  const mailOptions = {
    from: `"Vehicle Docs System" <${process.env.EMAIL_FROM}>`,
    to:  driver?.Driverid?.email,
    subject: `URGENT: ${document?.documentname} Expired`,
    html: `
      <div style="font-family: Arial, sans-serif; line-height: 1.6;">
        <h2 style="color: #d9534f;">⚠️ Document Expired Notification</h2>
        
        <p>Dear ${driver?.CompanyName},</p>
        
        <p>Your vehicle document has <strong>expired</strong>:</p>
        
        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
          <p><strong>Document Type:</strong> ${driver?.documentname}</p>
          <p><strong>$ 'Driver':</strong> ${driver?.Driverid?.title + "" + driver?.Driverid?.First_Name }</p>
        </div>
        
        <p>Please renew this document immediately to avoid penalties.</p>
        
      
        
        <p style="margin-top: 20px;">
          <small>This is an automated message. Please do not reply.</small>
        </p>
      </div>
    `
  };

  await transporter.sendMail(mailOptions);
};
