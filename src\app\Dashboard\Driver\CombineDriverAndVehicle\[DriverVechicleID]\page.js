"use client";
import React, { useState, useEffect } from "react";
import axios from "axios";
import Header from "../../../Components/Header";
import Sidebar from "../../../Components/Sidebar";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import AddDriverAndVehicleModel from "../AddDriverAndVehicleModel/AddDriverAndVehicleModel";
import UpdateCombineDriverAndVehicle from "../UpdateCombineDriverAndVehicle/UpdateCombineDriverAndVehicle";
import { API_URL_Driver_Vehicle_Allotment, API_URL_Vehicle } from "@/app/Dashboard/Components/ApiUrl/ApiUrls";
import { getCompanyName } from "@/utils/storageUtils";
import Link from "next/link";
import { isAuthenticated } from "@/utils/verifytoken";
import { useRouter } from "next/navigation";
import BackButton from "../../../Components/BackButton";
import DeleteModal from "@/app/Dashboard/Components/DeleteModal";
import Image from "next/image";

const Page = ({ params }) => {
  const router = useRouter();
  const id = params?.DriverVechicleID;
  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [isMounted, setIsMounted] = useState(false);
  const [selectedCompanyName, setSelectedCompanyName] = useState("");
  const [selectedUserId, setSelectedUserId] = useState(null);
  const [isOpenAddDriverModal, setIsOpenAddDriverModal] = useState(false);
  const [isOpenUpdateModal, setIsOpenUpdateModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const [itemperpage, setitemperpage] = useState(5);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleteModalOpenId, setIsDeleteModalOpenId] = useState(null);
  const [addButtonShowHide, setaddButtonShowHide] = useState(true);

  useEffect(() => {
    if (!isAuthenticated()) {
      router.push("/");
      return;
    }
  }, [router]);
  useEffect(() => {
    setIsMounted(true);
    const companyNameFromStorage = getCompanyName();
    if (companyNameFromStorage) {
      setSelectedCompanyName(companyNameFromStorage);
    }
  }, []);

  const fetchData = async () => {
    try {
      const response = await axios.get(
        `${API_URL_Driver_Vehicle_Allotment}/${id}`
      );
      response.data.result.length === 1 ? setaddButtonShowHide(false) : setaddButtonShowHide(true);
      setData(response?.data?.result || []);
      setFilteredData(response?.data?.result);
    } catch (error) {
      console.error("Error fetching data:", error);
      setData([]);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const isopendeletemodel = (id) => {
    setIsDeleteModalOpenId(id);
    setIsDeleteModalOpen(true);
  };

  const handleDelete = async (id) => {
    try {
      const response = await axios.delete(
        `${API_URL_Driver_Vehicle_Allotment}/${id}`
      );
      console.log(response);
      const { data } = response;
      if (data.status === 200) {
        setData((prevData) => prevData.filter((item) => item?._id !== id));
        setFilteredData((prevFilteredData) =>
          prevFilteredData.filter((item) => item?._id !== id)
        );
        toast.success(data?.message || "Allotment deleted successfully.");
        const carId = filteredData.find((records) => records?._id == id);
        const formDataupdate = new FormData();
        formDataupdate.append("vehicleStatus", "Standby");
        const updateResponse = await axios.put(
          `${API_URL_Vehicle}/${carId.vehicleId}`,
          formDataupdate,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );
        console.log("update vehicle status:", updateResponse);
      } else {
        toast.warn(data.message || "Failed to delete the allotment.");
      }
    } catch (error) {
      console.error("Error deleting allotment:", error);
      toast.error(
        error.response?.data?.message ||
        "An error occurred while deleting the allotment. Please try again."
      );
    }
  };

  useEffect(() => {
    const companyName = getCompanyName();
    const filtered = data.filter((item) => {
      const companyMatch =
        item.adminCompanyName &&
        companyName &&
        item.adminCompanyName.toLowerCase() === companyName.toLowerCase();
      return companyMatch;
    });
    setFilteredData(filtered);
  }, [data, selectedCompanyName]);

  const OpenAddDriverModal = () => {
    setSelectedUserId(id);
    setIsOpenAddDriverModal(!isOpenAddDriverModal);
  };

  const handleEdit = (idd) => {
    setSelectedUserId(idd);
    setIsOpenUpdateModal(true);
  };

  const OpenUpdateModal = () => {
    setIsOpenUpdateModal(!isOpenUpdateModal);
  };

  const indexOfLastDriver = currentPage * itemperpage;
  const indexOfFirstDriver = indexOfLastDriver - itemperpage;
  const currentDrivers = filteredData.slice(
    indexOfFirstDriver,
    indexOfLastDriver
  );

  console.log(currentDrivers);

  const totalPages = Math.ceil(filteredData.length / itemsPerPage);

  if (!isMounted) {
    return null;
  }

  return (
    <div className="h-[100vh] overflow-hidden">
      <Header className="min-w-full" />
      <div className="flex gap-4">
        <Sidebar />
        <div
          className="w-[80%] xl:w-[85%] h-screen flex flex-col justify-start overflow-y-auto pr-4"
          style={{
            height: "calc(100vh - 90px)",
          }}
        >
          <h1 className="text-[#313342] font-medium text-2xl py-5 underline decoration-[#AEADEB] underline-offset-8">
            Car Allotment
          </h1>
          <div className="py-5">
            <div className="drop-shadow-custom4">
              <div className="justify-between mx-auto items-center mt-3 w-full">
                <div className="flex justify-between w-full py-2 px-2">
                  <div className="flex flex-wrap justify-between flex-col sm:flex-row sm:items-center gap-3 w-full">
                    <div className="flex justify-between gap-7 items-center">
                      <div className="md:flex gap-3 hidden items-center">
                        <div className="font-sans font-medium text-sm">
                          Show
                        </div>
                       
                        <div>
                          <select
                            value={itemperpage}
                            onChange={(e) => {
                              setitemperpage(Number(e.target?.value)); // Make sure to convert to number
                              setCurrentPage(1);
                            }}
                            className="rounded-lg w-16 px-1 h-8 bg-[#E0E0E0] focus:outline-none"
                          >
                            <option disabled value={0}>Select</option>
                            {Array.from({ length: 6 }, (_, i) => (i + 1) * 5).map((number) => (
                              <option key={number} value={number}>
                                {number}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>

                    </div>

                      <div className="flex gap-2 ">
                          <BackButton />
                    {
                          addButtonShowHide === true ? (
                          <button
                            onClick={OpenAddDriverModal}
                            className="w-[132px] font-sans font-bold text-xs bg-[#313342] text-white rounded-lg hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 px-3 flex py-[10px] gap-2 items-center justify-center"
                          >
                            <Image width={15} height={15} src="/plus.svg" alt="Add Driver" />
                            Car Allotment
                          </button>
                      ) : null
                    }
                        </div>
                    

                  </div>
                </div>

                <div className="overflow-x-auto custom-scrollbar">
                  <table className="w-full bg-white border table-auto">
                    <thead className="font-sans font-bold text-sm text-center">
                      <tr className="text-white bg-[#38384A]">
                        <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                          Driver Name
                        </th>
                        <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                          Vehicle Name
                        </th>
                        <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                          Local Authority
                        </th>
                        <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                          Start Date
                        </th>
                        <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                          Payment Cycle
                        </th>
                        <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                          Payment
                        </th>
                        <th className="py-3 px-4 min-w-[180px] w-[180px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="font-sans font-medium text-sm text-center">
                      {currentDrivers.map((driver) => (
                        <tr key={driver?._id} className="border-b">
                          <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-center whitespace-normal break-all overflow-hidden">
                            {driver?.driverName}
                          
                          </td>
                          <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                            {driver?.vehicle}
                          </td>
                          <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-center whitespace-normal break-all overflow-hidden">
                            {driver?.taxilocalauthority}
                          </td>
                          <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                           
                            {new Intl.DateTimeFormat("en-US", {
                              month: "2-digit",
                              day: "2-digit",
                              year: "numeric",
                            }).format(new Date(driver?.startDate))}
                          </td>
                          
                          <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                            {driver?.paymentcycle === "perday" ? "Per Day" : driver?.paymentcycle === "perweek" ? "Per Week" : driver?.paymentcycle}
                          </td>

                          <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-center whitespace-normal break-all overflow-hidden">
                            {driver?.payment || 0}
                          </td>

                          <td className="py-3 px-4 min-w-[180px] w-[180px] md:w-[16.66%] whitespace-normal break-all overflow-hidden text-center">
                            <div className="flex gap-4 justify-center">
                              <button onClick={() => handleEdit(driver?._id)}>
                                <Image width={24} height={24}
                                  src="/edit.png"
                                  alt="edit"
                                  className="w-6"
                                />
                              </button>
                              <button
                                onClick={() => isopendeletemodel(driver?._id)}
                              >
                                <Image width={24} height={24}
                                  src="/trash.png"
                                  alt="delete"
                                  className="w-6"
                                />
                              </button>
                              <Link
                                passHref
                                href={`/Dashboard/Driver/MoreInfo/${driver?.vehicleId}`}
                                className="flex items-center"
                              >
                                <button>
                                  <Image width={24} height={24}
                                    src="/info.png"
                                    alt="info"
                                    className="w-6"
                                  />
                                </button>
                              </Link>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                <div className="flex justify-center py-5 font-montserrat font-medium text-[12px]">
                  <nav>
                    <ul className="flex items-center gap-3">
                     
                      <li>
                        <button
                          onClick={() =>
                            setCurrentPage((prev) => Math.max(prev - 1, 1))
                          }
                          disabled={currentPage === 1}
                          className={`h-8 px-2 border rounded-lg ${currentPage === 1
                            ? "opacity-50 cursor-not-allowed"
                            : "bg-white"
                            }`}
                        >
                          Previous
                        </button>
                      </li>
                      {totalPages > 1 && (
                        <>
                          {totalPages <= 3 ? (
                            Array.from(
                              { length: totalPages },
                              (_, index) => index + 1
                            ).map((page) => (
                              <li key={page}>
                                <button
                                  onClick={() => setCurrentPage(page)}
                                  className={`h-8 w-8 border rounded-lg ${currentPage === page
                                    ? "bg-custom-bg text-white"
                                    : "bg-white"
                                    }`}
                                >
                                  {page}
                                </button>
                              </li>
                            ))
                          ) : (
                            <>
                              {currentPage === 1 && (
                                <>
                                  <li>
                                    <button className="h-8 w-8 border rounded-lg bg-custom-bg text-white">
                                      1
                                    </button>
                                  </li>
                                  <li>
                                    <span className="px-2">...</span>
                                  </li>
                                  <li>
                                    <button
                                      onClick={() => setCurrentPage(totalPages)}
                                      className="h-8 w-8 border rounded-lg bg-white"
                                    >
                                      {totalPages}
                                    </button>
                                  </li>
                                </>
                              )}
                              {currentPage > 1 && currentPage < totalPages && (
                                <>
                                  <li>
                                    <button className="h-8 w-8 border rounded-lg bg-custom-bg text-white">
                                      {currentPage}
                                    </button>
                                  </li>
                                  <li>
                                    <span className="px-2">...</span>
                                  </li>
                                  <li>
                                    <button
                                      onClick={() => setCurrentPage(totalPages)}
                                      className="h-8 w-8 border rounded-lg bg-white"
                                    >
                                      {totalPages}
                                    </button>
                                  </li>
                                </>
                              )}
                              {currentPage === totalPages && (
                                <li>
                                  <button className="h-8 w-8 border rounded-lg bg-custom-bg text-white">
                                    {totalPages}
                                  </button>
                                </li>
                              )}
                            </>
                          )}
                        </>
                      )}

                      <li>
                        <button
                          onClick={() =>
                            setCurrentPage((prev) =>
                              Math.min(prev + 1, totalPages)
                            )
                          }
                          disabled={currentPage === totalPages}
                          className={`h-8 px-2 border rounded-lg ${currentPage === totalPages
                            ? "opacity-50 cursor-not-allowed"
                            : "bg-white"
                            }`}
                        >
                          Next
                        </button>
                      </li>
                    </ul>
                  </nav>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <AddDriverAndVehicleModel
        isOpen={isOpenAddDriverModal}
        onClose={OpenAddDriverModal}
        fetchData={fetchData}
        selectedUserId={selectedUserId}
      />
      <UpdateCombineDriverAndVehicle
        isOpen={isOpenUpdateModal}
        onClose={OpenUpdateModal}
        fetchData={fetchData}
        selectedUserId={selectedUserId}
      />
      <DeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onDelete={handleDelete}
        Id={isDeleteModalOpenId}
      />
    </div>
  );
};

export default Page;
