import { catchAsyncErrors } from "@middlewares/catchAsyncErrors.js";
import { connect } from "@config/db.js";
import { checkExpiredDocuments } from "@/utils/expirationChecker";
import { NextResponse } from "next/server";

export const GET = catchAsyncErrors(async () => {
  await connect();
  const count = await checkExpiredDocuments();
  return NextResponse.json({
    success: true,
    message: `Sent ${count} expiry notifications`
  });
});