"use client";
import React, { useEffect, useState } from "react";
import { API_URL_Signature } from "@/app/Dashboard/Components/ApiUrl/ApiUrls";
import { toast } from "react-toastify";
import axios from "axios";
import {
  getCompanyName,
  getUserId,
  getUserName, getflag, getcompanyId
} from "@/utils/storageUtils";
import Image from "next/image";
const UpdateSignatureModel = ({
  isOpen,
  onClose,
  fetchData,
  signatureData,
}) => {
  const [perviewimage, setpreviewimage] = useState(null);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    isActive: false,
    imageName: "",
    imageFile: null,
    adminCreatedBy: "",
    adminCompanyName: "",
    companyId: null,

  });


  useEffect(() => {
    const storedcompanyName = getCompanyName() || getUserName();
    const userId = getUserId();
    const flag = getflag();
    const compID = getcompanyId();

    if (storedcompanyName && userId) {
      if (storedcompanyName.toLowerCase() === "superadmin" && flag === "true" && compID) {
        setFormData((prevData) => ({
          ...prevData,
          adminCompanyName: storedcompanyName,
          companyId: compID, 
        }));
      } else {
        setFormData((prevData) => ({
          ...prevData,
          adminCompanyName: storedcompanyName,
          companyId: userId,
        }));
      }
    } else {
      console.error("Missing required fields:", { storedcompanyName, userId, flag, compID });
    }
  }, []);

  useEffect(() => {
    if (isOpen && signatureData) {
      const fetchEnquiry = async () => {
        try {
          const { data } = await axios.get(
            `${API_URL_Signature}/${signatureData}`
          );
          setFormData(data.result);
          setpreviewimage(data.result.imageFile);
        } catch (error) {
          console.error("Error fetching enquiry data:", error);
        }
      };
      fetchEnquiry();
    }
  }, [isOpen, signatureData]);


  const handleChange = (e) => {
    const { name, value, type, checked, files } = e.target;
    setFormData({
      ...formData,
      [name]:
        type === "checkbox" ? checked : type === "file" ? files[0] : value,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const formDataToSend = new FormData();
      Object.keys(formData).forEach((key) => {
        formDataToSend.append(key, formData[key]);
      });

      const response = await axios.put(
        `${API_URL_Signature}/${signatureData}`, 
        formDataToSend,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      toast.success(response?.data?.message);
      onClose();
      fetchData();

      setFormData({
        name: "",
        description: "",
        isActive: false,
        imageName: "",
        imageFile: null,
        adminCreatedBy: formData?.adminCreatedBy,
        adminCompanyName: formData?.adminCompanyName,
        companyId: formData?.companyId
      });
    } catch (error) {
      console.error("Error submitting the form:", error);
      toast.error("An error occurred while updating the signature."); 
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50 z-50">
      <div className="bg-white px-12 py-7 rounded-xl shadow-lg w-[531px] overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">
            Update Signature
          </h2>

          <Image width={15} height={15} alt="cross" src="/crossIcon.svg" className="cursor-pointer" onClick={() => {
            onClose();
          }} />
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-2 sm:grid-cols-2 gap-2">
            <div className="col-span-2">
              <label
                htmlFor="name"
                className="text-[10px]"
              >
                Name <span className="text-red-600">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData?.name}
                onChange={handleChange}
                className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>

            <div className="col-span-2">
              <label
                htmlFor="description"
                className="text-[10px]"
              >
                Description
              </label>
              <textarea
                id="description"
                name="description"
                value={formData?.description}
                onChange={handleChange}
                className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow focus:ring-blue-500 focus:border-blue-500"
                rows="2"
                required
              ></textarea>
            </div>

            <h2 className="font-bold mb-0 mt-4">Image Details</h2>
            <div className="col-span-2">
              <label
                htmlFor="imageName"
                className="text-[10px]"
              >
                Image Name
              </label>
              <input
                type="text"
                id="imageName"
                name="imageName"
                value={formData?.imageName}
                onChange={handleChange}
                className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow focus:ring-blue-500 focus:border-blue-500"
              />
            </div>


            <div className="flex items-center gap-2 col-span-2 mt-5">
              <label
                htmlFor="imageFile"
                className="text-[10px]"
              >
                Update Image
              </label>
              <input
                type="file"
                id="imageFile"
                name="imageFile"
                onChange={handleChange}
                accept="image/*"
                className="mt-1 block w-48 text-[8px] text-gray-400 file:mr-4 file:py-1 p-2 file:px-4 file:rounded-lg file:border file:text-[10px] file:font-semibold file:bg-white hover:file:bg-blue-100 border border-[#0885864D] rounded-[10px] border-dashed "
              />
            </div>

            <div>
              <label className="mb-2 text-[10px]">Status</label>
              <div className="flex gap-4 mt-4">
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="isActive"
                    value="true"
                    checked={formData.isActive === true}
                    onChange={() =>
                      handleChange({
                        target: { name: "isActive", value: true },
                      })
                    }
                    className="accent-green-500"
                  />
                  <span className="text-xs">Active</span>
                </label>

                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="isActive"
                    value="false"
                    checked={formData.isActive === false}
                    onChange={() =>
                      handleChange({
                        target: { name: "isActive", value: false },
                      })
                    }
                    className="accent-red-500"
                  />
                  <span className="text-xs">Inactive</span>
                </label>
              </div>
            </div>
            <div>
              {perviewimage && (
                <div>
                  <Image width={100} height={100}
                    src={perviewimage}
                    alt="Avatar Preview"
                    className="avatar-preview w-32 h-20"
                  />
                </div>
              )}
            </div>
          </div>

          <div className="flex gap-[10px] justify-end mt-8">
            <button
              type="button"
              onClick={onClose}
              className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-[#313342] text-white rounded-4 hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 py-1 px-8"
            >
              {signatureData ? "Update" : "Submit"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UpdateSignatureModel;
