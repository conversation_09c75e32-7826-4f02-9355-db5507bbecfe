import { connect } from "@config/db.js";
import Vehicle from "@models/Vehicle/Vehicle.Model.js";
import { catchAsyncErrors } from "@middlewares/catchAsyncErrors.js";
import { NextResponse } from "next/server";
import { uploadImage } from "services/uploadImage.js";  // Import the global uploadImage function

export async function POST(request) {
  try {
    await connect(); // Connect to the database

    // Parse the form data from the request
    const formDataObject = await request.formData();
    const safetyFeature = formDataObject.getAll("safetyFeatures[]");
    const techFeature = formDataObject.getAll("techFeatures[]");
    const files = formDataObject.getAll("imageFiles[]");// upload 10 images
    const damage_image = formDataObject.getAll("damage_image[0][file]");
    const cardocument = formDataObject.getAll("cardocuments[]");
    const pdfofpolicy = formDataObject.get("PDFofPolicy");

    const parts = [];
    const partMap = {}; // group parts by index: e.g., partMap[0] = { partNumber: ..., ... }

    // Group parts by their index in the form data
    for (let [key, value] of formDataObject.entries()) {
      const match = key.match(/^parts\[(\d+)\]\[(\w+)\]$/);
      if (match) {
        const index = match[1];
        const field = match[2];

        if (!partMap[index]) {
          partMap[index] = {};
        }

        partMap[index][field] = value;
      }
    }

    // Convert grouped map to array
    for (let index in partMap) {
      parts.push({
        partNumber: partMap[index].partNumber || "",
        partName: partMap[index].partName || "",
        partprice: partMap[index].partprice || "",
        partsupplier: partMap[index].partsupplier || ""
      });
    }

    const images = [];
    const damageImage = [];
    const cardocuments = [];

    // ok
    //  1: Handle PDF file upload
    let PDFofPolicyUrl = "";
    let PDFofPolicyPublicId = "";

    const { imageFile, imagePublicId } = await uploadImage(pdfofpolicy);

    PDFofPolicyUrl = imageFile;
    PDFofPolicyPublicId = imagePublicId;
    // till here PDF/image upload

    // ok
    //  2: Handle image uploads
      if (files.length === 0) {
      // Use global fallback from uploadImage.js
      const { imageFile, imagePublicId } = await uploadImage(null);
      images.push({ url: imageFile, publicId: imagePublicId });
    } else if (files.length > 10) {
      return NextResponse.json({
        error: "You can upload a maximum of 10 images.",
        status: 400,
      });
    } else {
      // Upload all files in parallel
      const uploaded = await Promise.all(
        files.filter(Boolean).map((file) => uploadImage(file))
      );

      uploaded.forEach(({ imageFile, imagePublicId }) =>
        images.push({ url: imageFile, publicId: imagePublicId })
      );
    }

    // ok
    //  3: Upload damage images testing
    for (const file of damage_image) {
      if (file) {
        try {
          const { imageFile, imagePublicId } = await uploadImage(file);

          // Store Cloudinary response (URL and public ID)
          damageImage.push({
            url: imageFile,
            publicId: imagePublicId,
          });

          console.log("Damage image uploaded:", {
            url: imageFile,
            publicId: imagePublicId,
          });
        } catch (err) {
          console.error("Error uploading damage image:", err.message);
        }
      } else {
        console.log("Invalid file detected:", file);
      }
    }

    // ok
    //  4: Upload car documents 
    for (const file of cardocument) {
      if (file) {
        try {
          const { imageFile, imagePublicId } = await uploadImage(file);

          // Store Cloudinary response (URL and public ID)
          cardocuments.push({
            url: imageFile,
            publicId: imagePublicId,
          });

          console.log("Car document uploaded:", {
            url: imageFile,
            publicId: imagePublicId,
          });
        } catch (err) {
          console.error("Error uploading car document:", err.message);
        }
      } else {
        console.log("Invalid file detected:", file);
      }
    }// till here car documents upload


    // Collect non-image fields from the form data
    const formDataObjectt = {};
    for (const [key, value] of formDataObject.entries()) {
      if (
        !key.startsWith("imageFiles[]") &&
        !key.startsWith("damage_image[]") &&
        !key.startsWith("pdfofpolicy[]") &&
        !key.startsWith("cardocuments[]")
      ) {
        formDataObjectt[key] = value;
      }
    }

    // Destructure the properties safely
    const {
      manufacturer,
      model,
      year,
      type,
      engineType,
      fuelType,
      transmission,
      drivetrain,
      exteriorColor,
      interiorColor,
      height,
      width,
      length,
      passengerCapacity,
      cargoCapacity,
      horsepower,
      torque,
      topSpeed,
      towingCapacity,
      fuelEfficiency,
      vehicleStatus,
      price,
      registrationNumber,
      warrantyInfo,
      isActive,
      adminCreatedBy,
      adminCompanyName,
      LocalAuthority,
      enginesize,
      chasisnumber,
      vehicleSite,
      fleetEntryDate,
      milesOnFleetEntry,
      plannedFleetExit,
      milesOnFleetExit,
      actualExitDate,
      milesAtActualExit,
      doors,
      color,
      editablecolor,
      roadTaxDate,
      roadTaxCycle,
      motDueDate,
      motCycle,
      seats,
      abiCode,
      nextServiceDate,
      nextServiceMiles,
      roadTaxCost,
      listPrice,
      purchasePrice,
      insuranceValue,
      departmentCode,
      maintenance,
      issues_damage,
      recovery,
      organization,
      repairStatus,
      jobNumber,
      memo,
      TestDate,
      PlateExpiryDate,
      Insurance,
      insurancePolicyNumber,
      defect,
      Defectdate,
      defectstatus,
      defectdescription,
      defectaction,
      additionalInfo,
      RPCExpiryDate,
      TailLiftExpiryDate,
      forkLiftNumber,
      ForkLiftInspectionDate,
      ForkLiftInspectionNumberNotes,
      companyId,
      dateOfLastV5CIssued,
      co2Emissions,
      taxStatus,
      motStatus,
      markedForExport,
      typeApproval,
      wheelplan,
      monthOfFirstRegistration
    } = formDataObjectt;

    // Validate required fields
    if (!registrationNumber || !manufacturer || !model) {
      return NextResponse.json({
        error: "Registration number, manufacturer, and model are required",
        status: 400,
      });
    }

    // Check for existing vehicle with the same registration number
    const existingVehicle = await Vehicle.findOne({
      $and: [{ registrationNumber: registrationNumber }, { adminCompanyName: adminCompanyName }],
    });
    if (existingVehicle) {
      return NextResponse.json({
        error: "Vehicle with this registration number already exists",
        status: 400,
      });
    }

    // Create a new vehicle entry in the database
    const newVehicle = new Vehicle({
      manufacturer,
      model,
      year,
      type,
      engineType,
      fuelType,
      transmission,
      drivetrain,
      exteriorColor,
      interiorColor,
      height,
      width,
      length,
      passengerCapacity,
      cargoCapacity,
      horsepower,
      torque,
      topSpeed,
      towingCapacity,
      safetyFeatures: safetyFeature,
      fuelEfficiency,
      techFeatures: techFeature,
      vehicleStatus: vehicleStatus || "Standby",
      price,
      registrationNumber,
      warrantyInfo,
      isActive,
      adminCreatedBy,
      adminCompanyName,
      LocalAuthority,
      images,
      enginesize,
      chasisnumber,
      vehicleSite,
      fleetEntryDate,
      milesOnFleetEntry,
      plannedFleetExit,
      milesOnFleetExit,
      actualExitDate,
      milesAtActualExit,
      doors,
      color,
      editablecolor,
      roadTaxDate,
      roadTaxCycle,
      motDueDate,
      motCycle,
      seats,
      abiCode,
      nextServiceDate,
      nextServiceMiles,
      roadTaxCost,
      listPrice,
      purchasePrice,
      insuranceValue,
      departmentCode,
      maintenance,
      issues_damage,
      recovery,
      organization,
      repairStatus,
      jobNumber,
      memo,
      parts,
      damageImage,
      TestDate,
      PlateExpiryDate,
      Insurance,
      insurancePolicyNumber,
      defect,
      Defectdate,
      defectstatus,
      defectdescription,
      defectaction,
      additionalInfo,
      RPCExpiryDate,
      TailLiftExpiryDate,
      forkLiftNumber,
      ForkLiftInspectionDate,
      PDFofPolicyUrl: PDFofPolicyUrl,
      PDFofPolicyPublicId: PDFofPolicyPublicId,
      cardocuments,
      companyId,
      ForkLiftInspectionNumberNotes,

      dateOfLastV5CIssued,
      co2Emissions,
      taxStatus,
      motStatus,
      markedForExport,
      typeApproval,
      wheelplan,
      monthOfFirstRegistration
    });

    await newVehicle.save();

    return NextResponse.json({
      success: true,
      message: "Vehicle uploaded successfully",
      vehicle: newVehicle,
      status: 200,
    });
  } catch (error) {
    console.log(error);
    return NextResponse.json({
      error: error.message,
      status: 500,
    });
  }
}

export const GET = catchAsyncErrors(async () => {
  await connect();
  const allVehicle = await Vehicle.find().sort({ createdAt: -1 }).populate("companyId");
  const VehicleCount = await Vehicle.countDocuments();
  if (!allVehicle || allVehicle.length === 0) {
    return NextResponse.json({ result: allVehicle });
  } else {
    return NextResponse.json({ result: allVehicle, count: VehicleCount });
  }
});
