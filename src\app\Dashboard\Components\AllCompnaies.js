"use client";
import React, { useEffect, useState, useCallback } from "react";
import { GetCompany } from "../Components/ApiUrl/ShowApiDatas/ShowApiDatas";
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from "chart.js";
import AddCompanymodel from "../Company/AddCompany/AddCompanyModel";
import { API_URL_Company } from "../Components/ApiUrl/ApiUrls";
import axios from "axios";
import UpdateCompanyModel from "../Company/UpdateCompany/UpdateCompanyModel";
import { isAuthenticated } from "@/utils/verifytoken";
import { useRouter } from "next/navigation";
import DeleteModal from "./DeleteModal";
import AdminDashBDoughnut from "../Components/AdminDashBDoughnut.jsx"
import { toast } from "react-toastify";
import Image from "next/image";
import SearchAndAddBar from "@/utils/searchAndAddBar";
import Pagination from "@/utils/pagination"
import {Count} from "@/utils/count";

ChartJS.register(ArcElement, Tooltip, Legend);

const AllCompanies = () => {
  const router = useRouter();
  const [data, setData] = useState([]);
  const [chartData, setChartData] = useState(null);
  const [filteredData, setFilteredData] = useState([]);
  const [isOpenCompany, setIsOpenCompany] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState(null);
  const [isOpenDriverUpdate, setIsOpenDriverUpdate] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleteModalOpenId, setIsDeleteModalOpenId] = useState(null);
  const [itemperpage, setitemperpage] = useState(Count);
  const [paginatedData, setPaginatedData] = useState([]);

  useEffect(() => {
    if (!isAuthenticated()) {
      router.push("/");
      return;
    }
  }, [router]);

  const isopendeletemodel = (id) => {
    setIsDeleteModalOpenId(id);
    setIsDeleteModalOpen(true);
  };

  const handleDelete = async (id) => {
    try {
      const { data: responseData } = await axios.delete(`${API_URL_Company}/${id}`);
      if (responseData?.success) {
        setData((prevData) => prevData.filter((item) => item?._id !== id));
        toast.success("Company deleted successfully.");
      } else {
        toast.warn(responseData?.message || "Failed to delete the Company.");
      }
    } catch (error) {
      console.error("Error deleting company:", error);
    }
  };

  const handleEdit = (id) => {
    setSelectedUserId(id);
    setIsOpenDriverUpdate(true);
  };

  const OpenCompanyModle = () => setIsOpenCompany(!isOpenCompany);
  const OpenDriverUpdateModle = () =>
    setIsOpenDriverUpdate(!isOpenDriverUpdate);

  const handleFilterChange = useCallback((filtered) => {
    setFilteredData(filtered);
  }, []);

  const handleItemsPerPageChange = useCallback((newItemsPerPage) => {
  setitemperpage(newItemsPerPage);
}, []);

  const fetchData = async () => {
    try {
      const { result } = await GetCompany();
      const activeCompanies = result?.filter(
        (company) => company?.isActive
      ).length;
      const inactiveCompanies = result?.length - activeCompanies;

      setData(result || []);

      setChartData({
        datasets: [
          {
            data: [activeCompanies, inactiveCompanies],
            backgroundColor: ["#27273AEB", "#404CA0"],
            borderWidth: inactiveCompanies === 0 ? 0 : 1,
          },
        ],
      });
    } catch (error) {
      console.error("Error fetching data:", error);
      setData([]);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    setFilteredData(data);
  }, [data]);


  const options = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
    }
  }

  if (!chartData) return <p>Loading...</p>;

  return (
    <div className=" overflow-y-auto overflow-x-hidden flex flex-col gap-10 pr-4" style={{
      height: "calc(100vh - 90px)"
    }}>

      <h1 className="text-[#313342] font-medium text-2xl py-5 underline decoration-[#AEADEB] underline-offset-8"> Registered Companies</h1>

      <AdminDashBDoughnut link={"/Dashboard/Company/AllCompanies"} title="Companies" data={chartData} option={options} />

      <div className="w-full py-5">
        <div className="drop-shadow-custom4 ">
          <SearchAndAddBar
            data={data}
            itemperpage={itemperpage}
            onAddClick={OpenCompanyModle}
            addLabel="Add Company"
            onFilterChange={handleFilterChange}
            onItemsPerPageChange={handleItemsPerPageChange}
          />

          <div className=" overflow-x-auto  custom-scrollbar">
            <table className="w-full bg-white border table-auto ">
              <thead className="font-sans font-bold text-sm text-left" >
                <tr className="   text-white bg-[#38384A]   ">
                  <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                    Company Name
                  </th>
                  <th className=" py-3 px-4 min-w-[150px] md:min-w-[16.66%] text-center text-white bg-custom-bg whitespace-normal break-all  overflow-hidden">Image</th>
                  <th className=" py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%]  text-white bg-custom-bg whitespace-normal break-all overflow-hidden">Email</th>
                  <th className=" py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%]  text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                    Password
                  </th>
                  <th className=" py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden ">Status</th>
                  <th className=" py-3 px-4 min-w-[180px] w-[180px] md:w-[16.66%] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden ">Actions</th>
                </tr>
              </thead>
              <tbody className=" font-sans font-medium text-sm" >
                {paginatedData?.map((item) => (
                  <tr key={item?._id} className="border-b">
                    <td className=" py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-center whitespace-normal break-all overflow-hidden">{item?.CompanyName}</td>
                    <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-centerr">
                      <Image
                        width={30} height={30}
                        src={item?.image}
                        alt="Company"
                        className="w-8 h-8 block mx-auto rounded-lg object-cover object-center"
                      />
                    </td>
                    <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">{item?.email}</td>
                    <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">{item?.confirmPassword}</td>
                    <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden text-center">
                      <span className="bg-[#38384A33]  px-4 py-2 rounded-[22px] text-xs">
                        {item?.isActive ? "Active" : "Inactive"}
                      </span>
                    </td>
                    <td className=" py-3 px-4 min-w-[180px] w-[180px] md:w-[16.66%] whitespace-normal break-all overflow-hidden text-center">
                      <div className="flex gap-4 justify-center">
                        <button
                          onClick={() => handleEdit(item?._id)}
                        >
                          <Image width={20} height={20} src="/edit.png" alt="edit" />
                        </button>
                        <button
                          onClick={() => isopendeletemodel(item?._id)}
                        >
                          <Image width={20} height={20} src="/trash.png" alt="delete" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <Pagination
            data={filteredData}
            itemperpage={itemperpage}
            onPageChange={(currentItems) => {
              setPaginatedData(currentItems);
            }}
          />
        </div>
      </div>

      <AddCompanymodel
        isOpen={isOpenCompany}
        onClose={OpenCompanyModle}
        fetchData={fetchData}
      />
      <UpdateCompanyModel
        isOpen={isOpenDriverUpdate}
        onClose={OpenDriverUpdateModle}
        fetchData={fetchData}
        existingCompanyId={selectedUserId}
      />
      <DeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onDelete={handleDelete}
        Id={isDeleteModalOpenId}
      />
    </div>
  );
};

export default AllCompanies;
