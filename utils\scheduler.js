import cron from 'node-cron';
import { checkDocumentExpirations } from './expirationChecker';

export const startExpiryScheduler = () => {
  // Run every day at 9 AM
  cron.schedule('0 9 * * *', async () => {
    console.log('Running document expiry check...');
    try {
      const { expired, upcoming } = await checkDocumentExpirations();
      console.log(`Sent ${expired} expiry and ${upcoming} upcoming alerts`);
    } catch (error) {
      console.error('Scheduler error:', error);
    }
  });
};