import axios from "axios";
import {
  API_URL_USER,
  API_URL_Driver,
  API_URL_Enquiry,
  API_URL_Firm,
  API_URL_Insurence,
  API_URL_LocalAuthority,
  API_URL_Manufacturer,
  API_URL_Payment,
  API_URL_Signature,
  API_URL_Supplier,
  API_URL_Vehicle,
  API_URL_VehicleType,
  API_URL_Title,
  API_URL_Badge,
  API_URL_Employee,
  API_URL_Company,
  API_URL_Transmission,
  API_URL_FuelType,
  API_URL_Type,
  API_URL_CarModel,
  API_URL_Driver_Vehicle_Allotment,
  API_URL_Document,
  API_URL_VehicleDocument
} from "../ApiUrls.js";

export const GetUsers = () => {
  let companyName = "";
let flag = "";
let superadmin = "";

if (typeof window !== "undefined") {
  companyName = localStorage.getItem("companyName") || "";
  flag = localStorage.getItem("flag") || "";
  superadmin = localStorage.getItem("role") || "";
}

console.log("Company Name:", companyName);
console.log("Flag:", flag);
console.log("Superadmin Role:", superadmin);

  return axios
    .get(`${API_URL_USER}`)
    .then((res) => {
      let users = res.data.result;

      if (superadmin === "superadmin" && flag === "false") {
        return { result: users, count: users.length };
      } else {
        users = users.filter((user) => user.companyname === companyName);
        return { result: users, count: users.length };
      }
    })
    .catch((error) => {
      console.log(`Error: ${error}`);
      throw error;
    });
};
export const GetDriver = () => {
  let companyName = localStorage.getItem("companyName"); 
  let flag = localStorage.getItem("flag");
  let superadmin = localStorage.getItem("role");
  return axios
    .get(`${API_URL_Driver}`)
    .then((res) => {
      let driver = res.data;
      if (superadmin === "superadmin" && flag === "false") {
        return { result: driver.result, count: driver.count };
      } else if (superadmin === "superadmin" && flag === "true") {
        return { result: driver.result, count: driver.count };
      } else {
        const filteredDrivers = driver.result.filter(
          (d) => d.companyname === companyName
        );
        return { result: filteredDrivers, count: filteredDrivers.length };
      }
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};
export const GetEnquiry = () => {
  return axios
    .get(`${API_URL_Enquiry}`)
    .then((res) => {
      return { result: res.data.Result, count: res.data.count };
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};
export const GetFirm = () => {
  return axios
    .get(`${API_URL_Firm}`)
    .then((res) => {
      return { result: res.data.result, count: res.data.count };
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};
export const GetCompany = () => {
  return axios
    .get(`${API_URL_Company}`)
    .then((res) => {
      return { result: res.data.result, count: res.data.count };
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};
export const GetInsurence = () => {
  return axios
    .get(`${API_URL_Insurence}`)
    .then((res) => {
      return { result: res.data.Result, count: res.data.count };
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};
export const GetEmployee = () => {
  return axios
    .get(`${API_URL_Employee}`)
    .then((res) => {
      return { result: res.data.Result, count: res.data.count };
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};
export const GetLocalAuthority = () => {
  return axios
    .get(`${API_URL_LocalAuthority}`)
    .then((res) => {
      return { result: res.data.Result, count: res.data.count };
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};
export const GetManufacturer = () => {
  return axios
    .get(`${API_URL_Manufacturer}`)
    .then((res) => {
      return { result: res.data.Result, count: res.data.count };
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};
export const GetPayment = () => {
  return axios
    .get(`${API_URL_Payment}`)
    .then((res) => {
      return { result: res.data.Result, count: res.data.count };
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};
export const GetTransmissions = () => {
  return axios
    .get(`${API_URL_Transmission}`)
    .then((res) => {
      return { result: res.data.Result, count: res.data.count };
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};
export const GetFueltype = () => {
  return axios
    .get(`${API_URL_FuelType}`)
    .then((res) => {
      return { result: res.data.Result, count: res.data.count };
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};
export const Gettype = () => {
  return axios
    .get(`${API_URL_Type}`)
    .then((res) => {
      return { result: res.data.Result, count: res.data.count };
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};
export const GetSignature = () => {
  return axios
    .get(`${API_URL_Signature}`)
    .then((res) => {
      return { result: res.data.Result, count: res.data.count };
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};
export const GetSupplier = () => {
  return axios
    .get(`${API_URL_Supplier}`)
    .then((res) => {
      return { result: res.data.Result, count: res.data.count };
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};
export const GetVehicleType = () => {
  return axios
    .get(`${API_URL_VehicleType}`)
    .then((res) => {
      return { result: res.data.Result, count: res.data.count };
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};
export const GetVehicle = () => {
  let companyName = localStorage.getItem("companyName");
  let flag = localStorage.getItem("flag");
  let superadmin = localStorage.getItem("role");
  return axios
    .get(`${API_URL_Vehicle}`)
    .then((res) => {
      let vehicle = res.data;
      if (superadmin === "superadmin" && flag === "false") {
        return { result: vehicle.result, count: vehicle.count };
      } else if (superadmin === "superadmin" && flag === "true") {
        return { result: vehicle.result, count: vehicle.count };
      } else {
        const filteredDrivers = vehicle.result.filter(
          (d) => d.adminCompanyName === companyName
        );
        return { result: filteredDrivers, count: filteredDrivers.length };
      }
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};
export const GetTitle = () => {
  return axios
    .get(`${API_URL_Title}`)
    .then((res) => {
      return { result: res.data.result, count: res.data.count };
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};
export const GetCarModel = () => {
  return axios
    .get(`${API_URL_CarModel}`)
    .then((res) => {
      return { result: res.data.result, count: res.data.count };
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};
export const GetBadge = () => {
  return axios
    .get(`${API_URL_Badge}`)
    .then((res) => {
      return { result: res.data.result, count: res.data.count };
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};
export const GetDriverVehicleAllotment = () => {
  let companyName = localStorage.getItem("companyName");
  let flag = localStorage.getItem("flag");
  let superadmin = localStorage.getItem("role");
  return axios
    .get(`${API_URL_Driver_Vehicle_Allotment}`)
    .then((res) => {
      let drivervehicle = res.data;

      if (superadmin === "superadmin" && flag === "false") {
        return { result: drivervehicle.Result, count: drivervehicle.count };
      } else if (superadmin === "superadmin" && flag === "true") {
        return { result: drivervehicle.Result, count: drivervehicle.count };
      } else {
        const filteredDrivers = drivervehicle.Result.filter(
          (d) => d.companyname === companyName
        );
        return { result: filteredDrivers, count: filteredDrivers.length };
      }
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};

export const GetDocumentType = () => {
  return axios
    .get(`${API_URL_Document}`)
    .then((res) => {
      return { result: res.data.result, count: res.data.count };
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};

export const VehicleDocument = () => {
  return axios
    .get(`${API_URL_VehicleDocument}`)
    .then((res) => {
      return { result: res.data.result, count: res.data.count };
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};