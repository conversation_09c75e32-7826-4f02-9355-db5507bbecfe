"use client";
import React, { useState, useEffect } from 'react';
import Header from "@/app/Dashboard/Components/Header";
import Sidebar from "@/app/Dashboard/Components/Sidebar";
import { toast } from "react-toastify";

const DocumentArchivePage = () => {
  const [archivedDocuments, setArchivedDocuments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  // Filter states
  const [selectedDocumentType, setSelectedDocumentType] = useState("all");
  const [selectedUploadedOn, setSelectedUploadedOn] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");

  // Mock archived documents data - in real implementation, this would come from localStorage or API
  const mockArchivedDocuments = [
    {
      _id: "1",
      documentType: "Insurance",
      documentName: "insurance_2024.pdf",
      uploadedOn: "2024-04-15",
      expiryDate: "2025-04-15",
      status: "Verified",
      verified: "Yes",
      notes: "—",
      vehicleInfo: "Honda Civic 2020"
    },
    {
      _id: "2",
      documentType: "Pollution Cert.",
      documentName: "pollution_cert.jpeg",
      uploadedOn: "2023-03-01",
      expiryDate: "2024-03-01",
      status: "Expired",
      verified: "No",
      notes: "—",
      vehicleInfo: "Honda Civic 2020"
    },
    {
      _id: "3",
      documentType: "Archived",
      documentName: "old_doc_scan.pdf",
      uploadedOn: "2022-06-10",
      expiryDate: "2023-06-10",
      status: "Verified",
      verified: "Yes",
      notes: "This document type is archived",
      vehicleInfo: "Honda Civic 2020"
    },
    {
      _id: "4",
      documentType: "Registration",
      documentName: "reg_2025.pdf",
      uploadedOn: "2025-01-01",
      expiryDate: "2026-01-01",
      status: "Expiring Soon",
      verified: "No",
      notes: "—",
      vehicleInfo: "Honda Civic 2020"
    }
  ];

  useEffect(() => {
    // Load archived documents from localStorage or set mock data
    const savedArchived = localStorage.getItem('archivedDocuments');
    if (savedArchived) {
      setArchivedDocuments(JSON.parse(savedArchived));
    } else {
      setArchivedDocuments(mockArchivedDocuments);
      localStorage.setItem('archivedDocuments', JSON.stringify(mockArchivedDocuments));
    }
  }, []);

  // Helper function to format date safely
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch (error) {
      return 'Invalid Date';
    }
  };

  // Helper function to get unique document types
  const getUniqueDocumentTypes = () => {
    const types = new Set();
    archivedDocuments.forEach(doc => {
      if (doc.documentType) {
        types.add(doc.documentType.toLowerCase());
      }
    });
    return Array.from(types);
  };

  // Helper function to count active filters
  const getActiveFiltersCount = () => {
    let count = 0;
    if (selectedDocumentType !== "all") count++;
    if (selectedUploadedOn !== "all") count++;
    if (selectedStatus !== "all") count++;
    if (searchTerm) count++;
    return count;
  };

  // Filter documents based on search term and filter criteria
  const filteredDocuments = archivedDocuments.filter((doc) => {
    const searchLower = searchTerm.toLowerCase();

    // Search filter
    const matchesSearch = searchTerm === '' || (
      doc.documentType.toLowerCase().includes(searchLower) ||
      doc.documentName.toLowerCase().includes(searchLower) ||
      doc.status.toLowerCase().includes(searchLower) ||
      doc.notes.toLowerCase().includes(searchLower) ||
      doc.vehicleInfo.toLowerCase().includes(searchLower)
    );

    // Document Type filter
    const matchesDocumentType = selectedDocumentType === 'all' ||
      doc.documentType.toLowerCase().includes(selectedDocumentType.toLowerCase());

    // Uploaded On filter (by year)
    const matchesUploadedOn = selectedUploadedOn === 'all' ||
      (doc.uploadedOn && doc.uploadedOn.includes(selectedUploadedOn));

    // Status filter
    const matchesStatus = selectedStatus === 'all' ||
      doc.status.toLowerCase() === selectedStatus.toLowerCase();

    return matchesSearch && matchesDocumentType && matchesUploadedOn && matchesStatus;
  });

  // Action handlers
  const handleView = (documentId) => {
    console.log("View document:", documentId);
    toast.info("View functionality to be implemented");
  };

  const clearAllFilters = () => {
    setSelectedDocumentType("all");
    setSelectedUploadedOn("all");
    setSelectedStatus("all");
    setSearchTerm("");
  };

  return (
    <div className="h-[100vh] overflow-hidden">
      <Header className="min-w-full" />
      <div className="flex gap-4">
        <Sidebar />
        <div
          className="w-[80%] xl:w-[85%] h-screen flex flex-col justify-start overflow-y-auto pr-4"
          style={{
            height: "calc(100vh - 90px)",
          }}
        >
          {/* Page Header */}
          <div className="flex items-center gap-2 py-5 pb-8">
            <div className="text-orange-500">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
              </svg>
            </div>
            <h1 className="text-[#313342] font-medium text-2xl">
              Document History
            </h1>
          </div>

          {/* Filter Buttons */}
          <div className="flex gap-4 mb-6">
            <select
              value={selectedDocumentType}
              onChange={(e) => setSelectedDocumentType(e.target.value)}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Document Type</option>
              {getUniqueDocumentTypes().map(type => (
                <option key={type} value={type}>
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </option>
              ))}
            </select>
            <select
              value={selectedUploadedOn}
              onChange={(e) => setSelectedUploadedOn(e.target.value)}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Uploaded On</option>
              <option value="2024">2024</option>
              <option value="2023">2023</option>
              <option value="2022">2022</option>
              <option value="2025">2025</option>
            </select>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Status</option>
              <option value="verified">Verified</option>
              <option value="expired">Expired</option>
              <option value="expiring soon">Expiring Soon</option>
            </select>
            <button
              onClick={clearAllFilters}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md font-medium flex items-center gap-2 hover:bg-gray-400 transition-colors"
            >
              <span>✕</span>
              Clear all Filters
            </button>

            {/* Search Bar */}
            <div className="relative ml-auto">
              <input
                type="text"
                placeholder="search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-4 pr-4 py-2 w-64 bg-gray-300 text-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder-gray-600"
              />
            </div>
          </div>

          {/* Results Counter */}
          <div className="flex justify-between items-center mb-4">
            <div className="text-sm text-gray-600">
              Showing {filteredDocuments.length} of {archivedDocuments.length} archived documents
            </div>
          </div>

          {/* Table */}
          <div className="py-5">
            <div className="drop-shadow-custom4">
              <div className="overflow-x-auto custom-scrollbar">
                <table className="w-full bg-white border table-auto">
                  <thead className="font-sans font-bold text-sm">
                    <tr className="text-gray-700 bg-gray-100">
                      <th className="py-3 px-4 text-left border-r">Document Type</th>
                      <th className="py-3 px-4 text-left border-r">Document Name</th>
                      <th className="py-3 px-4 text-left border-r">Uploaded On</th>
                      <th className="py-3 px-4 text-left border-r">Expiry Date</th>
                      <th className="py-3 px-4 text-left border-r">Status</th>
                      <th className="py-3 px-4 text-left border-r">Verified</th>
                      <th className="py-3 px-4 text-left border-r">Notes / Reason</th>
                      <th className="py-3 px-4 text-left">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="font-sans font-medium text-sm">
                    {loading ? (
                      <tr>
                        <td colSpan="8" className="py-8 text-center text-gray-500">
                          <div className="flex justify-center items-center">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                            <span className="ml-2">Loading...</span>
                          </div>
                        </td>
                      </tr>
                    ) : filteredDocuments.length === 0 ? (
                      <tr>
                        <td colSpan="8" className="py-8 text-center text-gray-500">
                          {searchTerm ? `No archived documents found matching "${searchTerm}"` : "No archived documents found"}
                        </td>
                      </tr>
                    ) : (
                      filteredDocuments.map((doc, index) => (
                        <tr key={doc._id || index} className="border-b hover:bg-gray-50">
                          <td className="py-3 px-4 border-r">{doc.documentType}</td>
                          <td className="py-3 px-4 border-r text-blue-600">{doc.documentName}</td>
                          <td className="py-3 px-4 border-r">{formatDate(doc.uploadedOn)}</td>
                          <td className="py-3 px-4 border-r">{formatDate(doc.expiryDate)}</td>
                          <td className="py-3 px-4 border-r">
                            <div className="flex items-center gap-2">
                              {doc.status === 'Verified' && (
                                <span className="text-green-600">✓</span>
                              )}
                              {doc.status === 'Expired' && (
                                <span className="text-red-600">✕</span>
                              )}
                              {doc.status === 'Expiring Soon' && (
                                <span className="text-orange-600">⚠</span>
                              )}
                              <span className={`${
                                doc.status === 'Verified' ? 'text-green-600' :
                                doc.status === 'Expired' ? 'text-red-600' :
                                doc.status === 'Expiring Soon' ? 'text-orange-600' :
                                'text-gray-600'
                              }`}>
                                {doc.status}
                              </span>
                            </div>
                          </td>
                          <td className="py-3 px-4 border-r">
                            <div className="flex items-center gap-2">
                              {doc.verified === 'Yes' ? (
                                <span className="text-green-600">✓ Yes</span>
                              ) : (
                                <span className="text-red-600">✕ No</span>
                              )}
                            </div>
                          </td>
                          <td className="py-3 px-4 border-r">{doc.notes}</td>
                          <td className="py-3 px-4">
                            <button
                              onClick={() => handleView(doc._id)}
                              className="transition-colors p-1 hover:bg-blue-50 rounded"
                              title="View Document"
                            >
                              <svg
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="text-blue-600"
                              >
                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
                                <circle cx="12" cy="12" r="3" />
                              </svg>
                            </button>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentArchivePage;