import { connect } from "@config/db.js";
import { checkExpiredDocuments } from "@/utils/expirationChecker";
import { NextResponse } from "next/server";

export const GET = async () => {
  try {
    await connect();
    const { expired, upcoming } = await checkExpiredDocuments();
    
    return NextResponse.json({
      success: true,
      message: `Processed ${expired} expired and ${upcoming} upcoming documents`
    });
  } catch (error) {
    return NextResponse.json(
      { success: false, message: error.message },
      { status: 500 }
    );
  }
};