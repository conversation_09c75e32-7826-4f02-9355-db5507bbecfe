// pages/api/data/route.js
import { connect } from '@config/db.js';
import { catchAsyncErrors } from '@middlewares/catchAsyncErrors';
import DeviceData from '../../../../models/Device_tmp/DeviceData.cjs'; 

export const GET = catchAsyncErrors(async () => {
  try {
    await connect();
    const data = await DeviceData.find().sort({ timestamp: -1 }).limit(100);
    return Response.json({ success: true, count: data.length, data }, { status: 200 });
  } catch (err) {
    return Response.json({ success: false, message: err.message }, { status: 500 });
  }
});

export async function POST(req) {
  try {
    await connect();
    const body = await req.json(); // parse JSON from the request
    const newRecord = await DeviceData.create(body);
    return Response.json({ success: true, data: newRecord }, { status: 201 });
  } catch (err) {
    return Response.json({ success: false, message: err.message }, { status: 400 });
  }
}