import VehicleDocument from "@models/VehicleDocuments/VehicleDocuments.Model.js";
import DriverDocument from "@models/DriverDocuments/DriverDocuments.Model.js";
import { sendExpiryEmail,sendExpiryEmailtodriver } from "./emailService";
import Vehicle from "@models/Vehicle/Vehicle.Model.js";
import Company from "@models/Company/Company.Model.js";
import Driver from "@models/Driver/Driver.Model.js";


export const checkExpiredDocuments = async () => {
  const today = new Date();
  today.setHours(0, 0, 0, 0); 

  // console.log(typeof Vehicle);
  // console.log(typeof Company);
  // console.log(typeof Driver);
  const expiredDocs = await VehicleDocument.find().populate('Vehicleid').populate('adminCompanyId');
  const expiredDriverDocs = await DriverDocument.find().populate('Driverid');
  // console.log(expiredDocs);

//  console.log(`Found ${expiredDocs.length} expired documents`);
//  console.log(`Found ${expiredDriverDocs.length} expired driver documents`);

  let notifiedCount = 0;

  for (const doc of expiredDocs) {
    try {
      await sendExpiryEmail(doc.adminCompanyId, doc);
      doc.notificationSent = true;
      doc.lastNotifiedAt = new Date();
      await doc.save();
      
      notifiedCount++;
      // console.log(`Notified ${doc?.adminCompanyId} about expired ${doc?.documentname}`);
    } catch (error) {
      console.error(`Failed to notify for document ${doc._id}:`, error);
    }
  }

  for (const doc of expiredDriverDocs) {
    try {
      // console.log(doc);
      await sendExpiryEmailtodriver(doc, doc);
      doc.notificationSent = true;
      doc.lastNotifiedAt = new Date();
      await doc.save();
      
      notifiedCount++;
      // console.log(`Notified ${doc?.adminCompanyId} about expired ${doc?.documentname}`);
    } catch (error) {
      console.error(`Failed to notify for document ${doc._id}:`, error);
    }
  }
  return notifiedCount;
};
