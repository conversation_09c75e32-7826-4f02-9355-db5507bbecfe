import { connect } from "@config/db.js";
import Driver from "@models/Driver/Driver.Model.js";
import { catchAsyncErrors } from "@middlewares/catchAsyncErrors.js";
import { NextResponse } from "next/server";
import bycrypt from "bcryptjs";
import {uploadImage} from "services/uploadImage.js";//Global image upload file

export async function POST(request) {
  try {
    await connect();
    const data = await request.formData();

    // Handling the uploaded files
    let file1 = data.get("imageFile");

    let Driveravatar = "";
    let DriveravatarId = "";

   // Use uploadImage method for less code
    if (!file1) {
          Driveravatar = "https://cdn-icons-png.flaticon.com/128/17561/17561717.png";
          DriveravatarId = "*********"; // Dummy image ID
      } else {
          try {
            const { imageFile, imagePublicId } = await uploadImage(file1);
            Driveravatar = imageFile;
            DriveravatarId = imagePublicId;
          } catch (error) {
            console.error("Upload failed, using default avatar:", error);
            // Use default image in case of any error during upload
            Driveravatar =
              "https://cdn-icons-png.flaticon.com/128/17561/17561717.png";
            DriveravatarId = "*********"; // Dummy image ID
          }
    }

    // Constructing formDataObject excluding the files
    const formDataObject = {};

    for (const [key, value] of data.entries()) {
      if (key !== "Driveravatar") {
        formDataObject[key] = value;
      }
    }

    const {
      title,
      First_Name,
      firstName,
      lastName,
      dateOfBirth,
      tel1,
      tel2,
      email,
      password,
      licenseNumber,
      niNumber,
      driverNumber,
      taxiFirm,
      badgeType,
      insurance,
      startDate,
      driverRent,
      licenseExpiryDate,
      taxiBadgeDate,
      rentPaymentCycle,
      city,
      pay,
      county,
      postcode,
      postalAddress,
      // permanentAddress,
      isActive,
      adminCreatedBy,
      adminCompanyName,
      LocalAuth,
      imageName,
      vehicle,
      calculation,
      companyId,
      BuildingAndStreetOne,
      BuildingAndStreetTwo,
    } = formDataObject;

    const FirstName = First_Name.trim();
    const last_Name = lastName.trim();
    console.log(FirstName,last_Name);

    const existingDriver = await Driver.findOne({
      $and: [{ email: email }, { adminCompanyName: adminCompanyName }],
    });

    if (existingDriver) {
      return NextResponse.json({
        error: "Driver with this email already exists",
        status: 400,
      });
    }

    const existingDriverUserName = await Driver.findOne({
      $and: [{ firstName: firstName }, { adminCompanyName: adminCompanyName }],
    });

    if (existingDriverUserName) {
      return NextResponse.json({
        error: "Driver with this UserName already exists",
        status: 400,
      });
    }

    const hashPassword = await bycrypt.hash(password, 10);

    // Create and save the new blog entry
    const newDriver = new Driver({
      title,
      First_Name:FirstName,
      firstName,
      lastName:last_Name,
      dateOfBirth,
      tel1,
      tel2,
      email,
      password: hashPassword,
      confirmPassword: password,
      licenseNumber,
      niNumber,
      driverNumber,
      taxiFirm,
      badgeType,
      insurance,
      startDate,
      driverRent,
      licenseExpiryDate,
      taxiBadgeDate,
      rentPaymentCycle,
      city,
      county,
      postcode,
      pay,
      LocalAuth,
      postalAddress,
      // permanentAddress,
      imageName,
      adminCreatedBy,
      adminCompanyName,
      vehicle,
      calculation,
      imageFile: Driveravatar,
      imagePublicId: DriveravatarId,
      companyId,
      BuildingAndStreetOne,
      BuildingAndStreetTwo,
      isActive: isActive || false, // Default to "Driver" if no role is specified
    });

    const savedDriver = await newDriver.save();
    if (!savedDriver) {
      return NextResponse.json({ message: "Driver not added", status: 400 });
    } else {
      return NextResponse.json({
        message: "Driver created successfully",
        success: true,
        savedDriver,
        status: 200,
      });
    }
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: error.message, status: 500 });
  }
}

export const GET = catchAsyncErrors(async () => {
  await connect();
  const allDriver = await Driver.find().sort({ createdAt: -1 }).populate("companyId");
  const DriverCount = await Driver.countDocuments();
  if (!allDriver || allDriver.length === 0) {
    return NextResponse.json({ result: allDriver });
  } else {
    return NextResponse.json({ result: allDriver, count: DriverCount });
  }
});
