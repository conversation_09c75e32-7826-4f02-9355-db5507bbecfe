"use client";
import { API_URL_Driver_Vehicle_Allotment } from "@/app/Dashboard/Components/ApiUrl/ApiUrls";
import axios from "axios";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import {
  fetchTaxiFirms,
  fetchLocalAuth,
  fetchVehicle,
} from "../../../Components/DropdownData/taxiFirm/taxiFirmService";
import {
  getCompanyName,
  getsuperadmincompanyname,
  getUserRole,
} from "@/utils/storageUtils";
import Image from "next/image";

const UpdateCombineDriverAndVehicle = ({
  isOpen,
  onClose,
  fetchData,
  selectedUserId,
}) => {
  const [formData, setFormData] = useState({
    driverId: "",
    driverName: "",
    startDate: "",
    taxifirm: "",
    taxilocalauthority: "",
    vehicle: "",
    vehicleId: "",
    paymentcycle: "",
    payment: "",
    adminCompanyName: "",
    adminCompanyId: "",
  });



  const [loading, setLoading] = useState(false);
  const [taxiFirms, setTaxiFirms] = useState([]);
  const [localAuth, setLocalAuth] = useState([]);
  const [vehicle, setVehicle] = useState([]);
  const [filteredVehicles, setFilteredVehicles] = useState([]);
  const [superadmin, setSuperadmin] = useState(null);



  useEffect(() => {
    const storedCompanyName = getCompanyName() || getsuperadmincompanyname();
    const storedSuperadmin = getUserRole();

    if (storedSuperadmin) {
      setSuperadmin(storedSuperadmin);
    }

    if (storedCompanyName) {
      setFormData((prevData) => ({
        ...prevData,
        adminCompanyName: storedCompanyName,
      }));
    }
  }, []);

  useEffect(() => {
    const fetchDriverData = async () => {
      if (!selectedUserId) return;
      setLoading(true);
      try {
        const { data } = await axios.get(
          `${API_URL_Driver_Vehicle_Allotment}/${selectedUserId}`
        );

        const formattedStartDate = data?.result?.startDate
          ? new Date(data.result.startDate).toISOString().split("T")[0]
          : "";


        const selectedVehicle = vehicle.find((v) => v._id === data?.result?.vehicleId);
        setFormData((prevData) => ({
          ...prevData,
          driverId: data?.result?.driverId,
          driverName: data?.result?.driverName,
          startDate: formattedStartDate,
          taxifirm: data?.result?.taxifirm || "",
          taxilocalauthority: data?.result?.taxilocalauthority || "",
          vehicle: selectedVehicle ? selectedVehicle.model : "", 
          vehicleId: selectedVehicle ? selectedVehicle._id : "",
          paymentcycle: data?.result?.paymentcycle || "",
          payment: data?.result?.payment || 0,
        }));
      } catch (err) {
        console.error(
          err.response?.data?.message || "Failed to fetch driver data"
        );
      } finally {
        setLoading(false);
      }
    };

    fetchDriverData();
  }, [selectedUserId, vehicle]);

  useEffect(() => {
    const loadDropdownData = async () => {
      try {
        const [taxiFirmsData, localAuthData, vehicleData] = await Promise.all([
          fetchTaxiFirms(),
          fetchLocalAuth(),
          fetchVehicle(),
        ]);

        const storedCompanyName = getCompanyName();
        const filterByCompany = (data) =>
          superadmin === "superadmin"
            ? data
            : data.filter(
              (item) =>
                item.adminCompanyName === storedCompanyName ||
                item.adminCompanyName === "superadmin"
            );

        setTaxiFirms(filterByCompany(taxiFirmsData?.result));
        setLocalAuth(filterByCompany(localAuthData?.Result));
        setVehicle(filterByCompany(vehicleData?.result));
      } catch (err) {
        console.error("Error loading dropdown data:", err);
      }
    };

    loadDropdownData();
  }, [superadmin]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));

    if (name === "vehicle") {
      const selectedVehicle = filteredVehicles.find(
        (vehicle) => vehicle.model === value
      );
      if (selectedVehicle) {
        setVehicleStatus(selectedVehicle._id);
        setFormData((prevFormData) => ({
          ...prevFormData,
          vehicleId: selectedVehicle._id,
        }));
      }
    }
    if (name === "taxilocalauthority") {
      const matchedVehicles = vehicle.filter(
        (veh) => veh.LocalAuthority === value
      );
      setFilteredVehicles(matchedVehicles);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const { data } = await axios.put(
        `${API_URL_Driver_Vehicle_Allotment}/${selectedUserId}`,
        formData
      );
      if (data.success) {
        toast.success(data.message);
        fetchData();
        onClose();
      } else {
        toast.warn(data.error);
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Failed to update record.");
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50 z-50">
      <div className="bg-white p-12 rounded-xl shadow-lg w-full max-w-4xl">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">
            Update Car Allotment
          </h2>

          <Image width={15} height={15} alt="cross" src="/crossIcon.svg" className="cursor-pointer" onClick={() => {
            onClose();
          }} />
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="flex flex-wrap -mx-2">
            <div className="w-full md:w-1/3 px-2 mb-4">
              <label
                htmlFor="driverName"
                className="text-[10px]"
              >
                Driver Name
              </label>
              <input
                type="text"
                id="driverName"
                name="driverName"
                value={formData?.driverName}
                onChange={handleChange}
                className="mt-1 block w-full p-2 border border-[#42506666] rounded-[4px] shadow-sm focus:ring-blue-500 focus:border-blue-500"
                readOnly
              />
            </div>

            <div className="w-full md:w-1/3 px-2 mb-4">
              <label
                htmlFor="paymentcycle"
                className="text-[10px]"
              >
                Rent Payment Cycle
              </label>
              <select
                id="paymentcycle"
                name="paymentcycle"
                value={formData?.paymentcycle}
                onChange={handleChange}
                className="mt-1 block w-full p-2 border border-[#42506666] rounded-[4px]"
              >
                <option value="">Select Payment</option>
                <option value="perday">Per Day</option>
                <option value="perweek">Per Week</option>
                <option value="permonth">Per Month</option>
                <option value="perquarter">Per Quarter</option>
                <option value="peryear">Per Year</option>
              </select>
            </div>

            <div className="w-full md:w-1/3 px-2 mb-4">
              <label
                htmlFor="startDate"
                className="text-[10px]"
              >
                Start Date
              </label>
              <input
                type="date"
                id="startDate"
                name="startDate"
                value={formData?.startDate}
                onChange={handleChange}
                className="mt-1 block w-full p-2 border border-[#42506666] rounded-[4px] shadow-sm focus:ring-blue-500 focus:border-blue-500"
                readOnly
              />
            </div>

            <div className="w-full md:w-1/3 px-2 mb-4">
              <label
                htmlFor="taxifirm"
                className="text-[10px]"
              >
                Taxi Firm
              </label>
              <select
                id="taxifirm"
                name="taxifirm"
                value={formData?.taxifirm}
                onChange={handleChange}
                className="mt-1 block w-full p-2 border border-[#42506666] rounded-[4px]"
              >
                <option value="">Select Taxi Firm</option>
                {taxiFirms.map((firm) => (
                  <option key={firm?._id} value={firm?.name}>
                    {firm?.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="w-full md:w-1/3 px-2 mb-4">
              <label
                htmlFor="taxilocalauthority"
                className="text-[10px]"
              >
                Taxi Local Authority
              </label>
              <select
                id="taxilocalauthority"
                name="taxilocalauthority"
                value={formData.taxilocalauthority}
                onChange={handleChange}
                className="mt-1 block w-full p-2 border border-gray-300 rounded-lg"
              >
                <option value="">Select Local Authority</option>
                {localAuth.map((auth) => (
                  <option key={auth?._id} value={auth?.name}>
                    {auth?.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="w-full md:w-1/3 px-2 mb-4">
              <label
                htmlFor="taxiFirm"
                className="text-[10px]"
              >
                Vehicle
              </label>
              <input
                type="text"
                id="vehicle"
                name="vehicle"
                value={formData?.vehicle}
                onChange={handleChange}
                className="mt-1 block w-full p-2 pl-7 border border-[#42506666] rounded-[4px] shadow-sm focus:ring-blue-500 focus:border-blue-500"
                
                readOnly
              />

            </div>

            <div className="w-full md:w-1/3 px-2 mb-4">
              <label
                htmlFor="payment"
                className="text-[10px]"
              >
                Rent Payment Amount
              </label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">£</span>
                <input
                  type="number"
                  id="payment"
                  name="payment"
                  min={1}
                  value={formData?.payment}
                  onChange={handleChange}
                  className="mt-1 block w-full p-2 pl-7 border border-[#42506666] rounded-[4px] shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>

            </div>
          </div>

          <div className="flex justify-end gap-2">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2 ml-2 text-custom-bg rounded-[4px] text-xs font-bold border-2 border-custom-bg hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className={`px-6 py-2 bg-custom-bg text-white rounded-[4px] text-xs font-bold hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50${loading ? "bg-gray-400" : "bg-blue-600"
                }`}
              disabled={loading}
            >
              {loading ? "Updating..." : "Update"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UpdateCombineDriverAndVehicle;