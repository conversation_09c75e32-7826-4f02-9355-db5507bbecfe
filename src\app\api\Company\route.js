import { connect } from "@config/db.js";
import Company from "@models/Company/Company.Model.js";
import { catchAsyncErrors } from "@middlewares/catchAsyncErrors.js";
import { NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import { uploadImage } from "services/uploadImage.js";// Global cloudinary upload image file

export async function POST(request) {
  try {
    
    await connect();
    const data = await request.formData();

    // Handling the uploaded files
    let file1 = data.get("image");

    const { image, imagePublicId } = await uploadImage(file1); // Call the function from uploadImage.js

    // Constructing formDataObject excluding the files
    const formDataObject = {};
    for (const [key, value] of data.entries()) {
      if (key !== "image") {
        formDataObject[key] = value;
      }
    }

    // Only hash the password if it's being updated
    const {
      CompanyName,
      userId,
      email,
      password,
      confirmPassword,
      isActive,
      CreatedBy,
      CompanyRegistrationNumber,
      vatnumber,
      mailingAddress,
      physical_Address,
      phoneNumber,
      fax_Number,
      generalEmail,
      accountsPayableEmail,
      specificContactEmail,
      accountsPayableContactName,accountsPayableContactLastName,title,
      accountsPayableContactPhoneNumberandEmail,
      billingAddress,
      paymentTermsAgreedPaymentSchedule,
      paymentTermsPreferredPaymentMethod,
      bankingInformationBankName,
      bankingInformationBankAccountNumber,
      bankingInformationBankIBANSWIFTCode,
      bankingInformationBankAddress,
      specificDepartmentContactInformationBillingFinanceDepartment,
      specificDepartmentContactInformationProcurementPurchasingContact,
      specificDepartmentContactInformationPrimaryContactfortheProject,
      Postcode,
      BuildingAndStreetOne,
      BuildingAndStreetTwo,
      Town_City,
      Country,
      isSameAddress
    
    } = formDataObject;

    const hashedPassword = await bcrypt.hash(password, 10);
    const existingUser = await Company.findOne({ email });
    if (existingUser) {
      return NextResponse.json({
        error: "Company Already Exist",
        status: 400,
      });
    }

    // Create and save the new company entry
    const newCompany = new Company({
      CompanyName,
      email,
      userId,
      password: hashedPassword,
      confirmPassword,
      isActive: isActive || false,
      CreatedBy,
      image,
      imagePublicId,
      CompanyRegistrationNumber,
      vatnumber,
      mailingAddress,
      physical_Address,
      phoneNumber,
      fax_Number,
      generalEmail,
      accountsPayableEmail,
      specificContactEmail,
      accountsPayableContactName
      ,accountsPayableContactLastName
      ,title,
      accountsPayableContactPhoneNumberandEmail,
      billingAddress,
      paymentTermsAgreedPaymentSchedule,
      paymentTermsPreferredPaymentMethod,
      bankingInformationBankName,
      bankingInformationBankAccountNumber,
      bankingInformationBankIBANSWIFTCode,
      bankingInformationBankAddress,
      specificDepartmentContactInformationBillingFinanceDepartment,
      specificDepartmentContactInformationProcurementPurchasingContact,
      specificDepartmentContactInformationPrimaryContactfortheProject,
      Postcode,
      BuildingAndStreetOne,
      BuildingAndStreetTwo,
      Town_City,
      Country,
      isSameAddress
    });

    const savedFirm = await newCompany.save();
    if (!savedFirm) {
      return NextResponse.json({ message: "Company not added", status: 400 });
    } else {
      return NextResponse.json({
        message: "Company created successfully",
        success: true,
        status: 200,
      });
    }
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: error.message, status: 500 });
  }
}

export const GET = catchAsyncErrors(async () => {
  await connect();
  const allCompany = await Company.find().populate('userId');
  const CompanyCount = await Company.countDocuments();
  if (!allCompany || allCompany.length === 0) {
    return NextResponse.json({ result: allCompany });
  } else {
    return NextResponse.json({ result: allCompany, count: CompanyCount });
  }
});
