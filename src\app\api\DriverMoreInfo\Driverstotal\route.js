// import { NextResponse } from "next/server";
// import { connect } from "@config/db.js";
// import DriverMoreInfo from "@models/DriverMoreInfo/DriverMoreInfo.model.js";
// import Driver from "@models/Driver/Driver.Model.js";
// import mongoose from "mongoose";


// export const GET = async () => {
//   await connect(); // Ensure database connection

//   // 🔹 Fetch all driver records
//   const drivers = await DriverMoreInfo.find({});

//   let totalPayment = 0;
//   let totalCost = 0;
//   let totalPay = 0;

//   for (const driver of drivers) {
//     totalPayment += driver.payment || 0;
//     totalCost += driver.cost || 0;
//     totalPay += driver.pay || 0;
//   }

//   // 🔹 Calculate the remaining amount
//   const remainingAmount = totalPayment - totalPay;

//   console.log("✅ Total Calculated:", {
//     totalPayment,
//     totalCost,
//     totalPay,
//     remainingAmount
//   });


//     for (const driver of drivers) {
//       if (driver.driverId) {
//         const driverObjectId = new mongoose.Types.ObjectId(driver.driverId);
    
//         let data = await Driver.findOneAndUpdate(
//           // { _id: driverObjectId }, // Ensure _id is an ObjectId
//           { _id: driverObjectId, adminCompanyName: driver.adminCompanyName },
//           { $set: { totalamount: remainingAmount } },
//           { new: true }
//         );
//         console.log(data)
//       }
//     }

//   return NextResponse.json({
//     dirverId: drivers[0].driverId,
//     message: "Totals calculated successfully",
//     totalPayment,
//     totalCost,
//     totalPay,
//     remainingAmount
//   });
// };


import { NextResponse } from "next/server";
import { connect } from "@config/db.js";
import DriverMoreInfo from "@models/DriverMoreInfo/DriverMoreInfo.model.js";
import Driver from "@models/Driver/Driver.Model.js";
import mongoose from "mongoose";

export const GET = async () => {
  await connect(); // Ensure database connection

  // 🔹 Fetch all driver records
  const drivers = await DriverMoreInfo.find({});

  let totalPayment = 0;
  let totalCost = 0;
  let totalPay = 0;

  for (const driver of drivers) {
    totalPayment += driver?.payment || 0;  // ✅ Safe access
    totalCost += driver?.cost || 0;        // ✅ Safe access
    totalPay += driver?.pay || 0;          // ✅ Safe access
  }

  // 🔹 Calculate the remaining amount
  const remainingAmount = totalPayment - totalPay;

    for (const driver of drivers) {
      if (driver.driverId) {
        const driverObjectId = new mongoose.Types.ObjectId(driver.driverId);
    
        await Driver.findOneAndUpdate(
          { _id: driverObjectId, adminCompanyName: driver.adminCompanyName },
          { $set: { totalamount: remainingAmount } },
          { new: true }
        );
      }
    }
  

  // ✅ CHANGED: Avoid crash if drivers array is empty
  return NextResponse.json({
    driverId: drivers.length > 0 ? drivers[0].driverId : null, 
    message: "Totals calculated successfully",
    totalPayment,
    totalCost,
    totalPay,
    remainingAmount
  });
};
