import type { Metada<PERSON> } from "next";
import localFont from "next/font/local";
import "./globals.css";
import { ToastContainer } from "react-toastify";

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export const metadata: Metadata = {
  title: "Vehicle Management System",
  description: "Generated by <PERSON><PERSON> khan",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-[#F7F7FD] `}
      >
        <ToastContainer position="top-center"  hideProgressBar={true}  autoClose={2000}
          closeButton={false}/>
        {/* <ToastContainer
          position="top-center"
          autoClose={2000}
          hideProgressBar={true}
          closeButton={false}
          newestOnTop={false}
          closeOnClick
          rtl
          pauseOnFocusLoss={false}
          draggable={false}
          pauseOnHover={false}
        /> */}
        {children}
      </body>
    </html>
  );
}
