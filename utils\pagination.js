import { useState, useEffect, useRef } from "react";

const Pagination = ({
  data = [],
  itemperpage,
  onPageChange,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const totalPages = Math.ceil(data.length / itemperpage);
  const dataLengthRef = useRef(data.length);
  const onPageChangeRef = useRef(onPageChange);
  

  useEffect(() => {
    onPageChangeRef.current = onPageChange;
  }, [onPageChange]);

  useEffect(() => {
    if (dataLengthRef.current !== data.length) {
      setCurrentPage(1);
      dataLengthRef.current = data.length;
    }
  }, [data.length]);

  useEffect(() => {
    setCurrentPage(1);
  }, [itemperpage]);

  useEffect(() => {
    const indexOfLast = currentPage * itemperpage;
    const indexOfFirst = indexOfLast - itemperpage;
    const currentItems = data.slice(indexOfFirst, indexOfLast);
    
    if (onPageChangeRef.current) {
      onPageChangeRef.current(currentItems, currentPage);
    }
  }, [currentPage, data, itemperpage]);


  return (
    <div className="flex justify-center py-5 font-montserrat font-medium text-[12px]">
      <nav>
        <ul className="flex items-center gap-3">
          <li>
            <button
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className={`h-8 px-2 border rounded-lg ${currentPage === 1 ? "opacity-50 cursor-not-allowed" : "bg-white"}`}
            >
              Previous
            </button>
          </li>

          {totalPages > 1 && (
            <>
              {totalPages <= 3 ? (
                Array.from({ length: totalPages }, (_, index) => index + 1).map((page) => (
                  <li key={page}>
                    <button
                      onClick={() => setCurrentPage(page)}
                      className={`h-8 w-8 border rounded-lg ${currentPage === page ? "bg-custom-bg text-white" : "bg-white"}`}
                    >
                      {page}
                    </button>
                  </li>
                ))
              ) : (
                <>
                  {currentPage === 1 && (
                    <>
                      <li>
                        <button className="h-8 w-8 border rounded-lg bg-custom-bg text-white">1</button>
                      </li>
                      <li>
                        <button onClick={() => setCurrentPage(2)} className="h-8 w-8 border rounded-lg bg-white">
                          2
                        </button>
                      </li>
                      <li><span className="px-2">...</span></li>
                      <li>
                        <button onClick={() => setCurrentPage(totalPages)} className="h-8 w-8 border rounded-lg bg-white">
                          {totalPages}
                        </button>
                      </li>
                    </>
                  )}
                  {currentPage > 1 && currentPage < totalPages && (
                    <>
                      <li>
                        <button 
                          onClick={() => setCurrentPage(1)} 
                          className="h-8 w-8 border rounded-lg bg-white"
                        >
                          1
                        </button>
                      </li>
                      <li><span className="px-2">...</span></li>
                      <li>
                        <button className="h-8 w-8 border rounded-lg bg-custom-bg text-white">
                          {currentPage}
                        </button>
                      </li>
                      <li><span className="px-2">...</span></li>
                      <li>
                        <button onClick={() => setCurrentPage(totalPages)} className="h-8 w-8 border rounded-lg bg-white">
                          {totalPages}
                        </button>
                      </li>
                    </>
                  )}
                  {currentPage === totalPages && (
                    <>
                      <li>
                        <button onClick={() => setCurrentPage(1)} className="h-8 w-8 border rounded-lg bg-white">
                          1
                        </button>
                      </li>
                      <li><span className="px-2">...</span></li>
                      <li>
                        <button onClick={() => setCurrentPage(totalPages - 1)} className="h-8 w-8 border rounded-lg bg-white">
                          {totalPages - 1}
                        </button>
                      </li>
                      <li>
                        <button className="h-8 w-8 border rounded-lg bg-custom-bg text-white">
                          {totalPages}
                        </button>
                      </li>
                    </>
                  )}
                </>
              )}
            </>
          )}

          <li>
            <button
              onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className={`h-8 px-2 border rounded-lg ${currentPage === totalPages ? "opacity-50 cursor-not-allowed" : "bg-white"}`}
            >
              Next
            </button>
          </li>
        </ul>
      </nav>
    </div>
  );
};

export default Pagination;