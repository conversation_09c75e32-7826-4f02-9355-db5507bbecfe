import { connect } from "@config/db.js";
import { Firm } from "@models/Firm/Firm.Model.js";
import { catchAsyncErrors } from "@middlewares/catchAsyncErrors.js";
import { NextResponse } from "next/server";
import { uploadImage } from "services/uploadImage.js";// Global image upload file

export async function POST(request) {
  try {
    await connect();
    const data = await request.formData();

    // accept multiple frontend keys for file
    const file1 = data.get("imageFile") || data.get("useravatar") || data.get("image");

    let imageFile = "";
    let imagepublicId = "";

    if (file1 && typeof file1 === "object" && file1.name) {
      try {
        const { imageFile: uploadedUrl, imagePublicId } = await uploadImage(file1);
        if (uploadedUrl) imageFile = uploadedUrl;
        if (imagePublicId) imagepublicId = imagePublicId;
      } catch (err) {
        console.error("uploadImage helper failed:", err);
        return NextResponse.json({ error: "Image upload failed", status: 500 });
      }
  }

     // Constructing formDataObject excluding the files
    const formDataObject = {};
    for (const [key, value] of data.entries()) {
      if (key === "imageFile" || key === "useravatar" || key === "image") continue;
      formDataObject[key] = value;
    }

    const {
      name,
      description,
      imageName,
      companyNo,
      vatNo,
      insurancePolicyNo,
      website,
      email,
      tel1,
      tel2,
      address,
      city,
      country,
      postcode,
      employmentLetter,
      coverLetter,
      signature,
      isActive,
      adminCreatedBy,
      adminCompanyName,
      companyId,
    } = formDataObject;

    const existingUser = await Firm.findOne({
      $and: [{ email: email }, { adminCompanyName: adminCompanyName }],
    });
    if (existingUser) {
      return NextResponse.json({
        error: "Firm Already Exist with this email And Company Name",
        status: 400,
      });
    }

    // Create and save the new blog entry
    const newFirm = new Firm({
      name,
      description,
      imageName,
      companyNo,
      vatNo,
      insurancePolicyNo,
      website,
      email,
      tel1,
      tel2,
      address,
      city,
      country,
      postcode,
      employmentLetter,
      coverLetter,
      signature,
      imageFile,
      imagepublicId,
      adminCreatedBy,
      adminCompanyName,
      companyId,
      // imageNote,
      isActive: isActive || false,
    });

    const savedFirm = await newFirm.save();
    if (!savedFirm) {
      return NextResponse.json({ message: "Firm not added", status: 400 });
    } else {
      return NextResponse.json({
        message: "Firm created successfully",
        success: true,
        status: 200,
      });
    }
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: error.message, status: 500 });
  }
}

export const GET = catchAsyncErrors(async () => {
  await connect();
  const allFirm = await Firm.find().populate("companyId").sort({ createdAt: -1 });
  const FirmCount = await Firm.countDocuments();
  if (!allFirm || allFirm.length === 0) {
    return NextResponse.json({ result: allFirm });
  } else {
    return NextResponse.json({ result: allFirm, count: FirmCount });
  }
});
