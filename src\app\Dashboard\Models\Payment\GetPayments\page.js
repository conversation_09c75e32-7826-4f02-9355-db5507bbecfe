"use client";

import React, { useState, useEffect } from "react";
import DataTable from "react-data-table-component";
import Header from "../../../Components/Header";
import Sidebar from "../../../Components/Sidebar";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { FaEdit, FaTrash } from "react-icons/fa";
import AddPaymentModel from "../AddPayment/AddPaymentModel";
import UpdatePaymentModel from "../UpdatePayment/UpdatePaymentModel";
import axios from "axios";
import { API_URL_Payment } from "@/app/Dashboard/Components/ApiUrl/ApiUrls";
import { GetPayment } from "@/app/Dashboard/Components/ApiUrl/ShowApiDatas/ShowApiDatas";
import { getCompanyName } from "@/utils/storageUtils";
import Image from "next/image";

const Page = () => {
  const columns = [
    {
      name: "Payment",
      selector: (row) => row.name,
      sortable: true,
    },
    {
      name: "Payment Description",
      selector: (row) => row.description,
      sortable: true,
    },
    {
      name: "Company",
      selector: (row) => row.adminCompanyName,
      sortable: true,
    },
    {
      name: "Payment Status",
      selector: (row) => (row.isActive ? "Active" : "InActive"),
      sortable: true,
    },
    {
      name: "Actions",
      cell: (row) => (
        <div className="flex gap-2">
          <button
            onClick={() => handleEdit(row._id)}
            className="text-blue-500 hover:text-blue-700"
          >
            <FaEdit />
          </button>
          <button
            onClick={() => handleDelete(row._id)}
            className="text-red-500 hover:text-red-700"
          >
            <FaTrash />
          </button>
        </div>
      ),
      allowOverflow: true,
      button: true,
    },
  ];
  const [data, setData] = useState([]); 
  const [filteredData, setFilteredData] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isMounted, setIsMounted] = useState(false);
  const [isOpenPayment, setIsOpenPayment] = useState(false);
  const [selectedCompanyName, setSelectedCompanyName] = useState("");
  const [selectedUserId, setSelectedUserId] = useState(null);
  const [isOpenVehicleUpdate, setIsOpenVehcleUpdate] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    const companyNameFromStorage = getCompanyName(); 
    if (companyNameFromStorage) {
      setSelectedCompanyName(companyNameFromStorage); 
    }
  }, []);

  const fetchData = async () => {
    try {
      GetPayment().then(({ result }) => {

        setData(result);
        setFilteredData(result);
      });
    } catch (error) {
      console.error("Error fetching data:", error);
      setData([]);
    }
  };
  
  useEffect(() => {
    fetchData();
  }, []);
  const handleDelete = async (id) => {
    try {
      const response = await axios.delete(`${API_URL_Payment}/${id}`);
      const { data } = response; 


      if (data.status === 200) {
        setData((prevData) => prevData.filter((item) => item._id !== id));
        setFilteredData((prevFilteredData) =>
          prevFilteredData.filter((item) => item._id !== id)
        );
        toast.success(data.message || "Payment deleted successfully."); 
      } else {
        toast.warn(data.message || "Failed to delete the Payment."); 
      }
    } catch (error) {
      console.error("Error deleting Payment:", error);
      toast.error(
        error.response?.data?.message ||
          "An error occurred while deleting the Payment. Please try again."
      );
    }
  };

  useEffect(() => {
    const filtered = data.filter((item) => {
      const companyMatch =
        item.adminCompanyName &&
        selectedCompanyName &&
        item.adminCompanyName.toLowerCase() ===
          selectedCompanyName.toLowerCase();

      const usernameMatch =
        item.name && item.name.toLowerCase().includes(searchTerm.toLowerCase());

      return companyMatch && usernameMatch;
    });
    setFilteredData(filtered); 
  }, [searchTerm, data, selectedCompanyName]);

  const handleEdit = (id) => {
    toast.info(`Edit item with ID: ${id}`);
    setSelectedUserId(id); 
    setIsOpenVehcleUpdate(true);
  };

  if (!isMounted) {
    return null; 
  }

  const OpenPaymentModle = () => {
    setIsOpenPayment(!isOpenPayment);
  };
  const OpenVehicleUpdateModle = () => {
    setIsOpenVehcleUpdate(!isOpenVehicleUpdate); 
  };

  return (
    <>
      <Header className="min-w-full" />
      <div className="flex gap-4">
        <Sidebar />
        <div className="container mx-auto p-4 ">
          <div className="justify-between mx-auto items-center border-2 mt-3 w-[78%]">
            <div className="flex justify-between">
              <div className="justify-start">
                <input
                  type="text"
                  placeholder="Search by title"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="border rounded px-4 py-2 w-64" 
                />
              </div>
              <div className="justify-end">
                <button
                  onClick={OpenPaymentModle}
                  className="bg-custom-bg text-white px-4 py-2 rounded hover:bg-blue-600  flex items-center justify-center gap-2"
                >
                  <Image width={15} height={15}  src="/plus.png" alt="Add Company" className="w-4 h-4" />
                  Add Payment
                </button>
              </div>
            </div>

            <DataTable
              title="Payments List"
              columns={columns}
              data={filteredData} 
              pagination
            />
          </div>
        </div>
      </div>
      <AddPaymentModel
        isOpen={isOpenPayment}
        onClose={OpenPaymentModle}
        fetchData={fetchData}
      />
      <UpdatePaymentModel
        isOpen={isOpenVehicleUpdate}
        onClose={OpenVehicleUpdateModle}
        fetchData={fetchData}
        paymentid={selectedUserId} 
      />
    </>
  );
};

export default Page;
