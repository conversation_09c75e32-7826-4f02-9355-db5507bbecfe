/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: [
      "res.cloudinary.com",
      "static.thenounproject.com",
      "encrypted-tbn0.gstatic.com",
      "static.vecteezy.com",
      "www.freeiconspng.com",
      "via.placeholder.com",
      "www.smartcaptech.com",
      "media.istockphoto.com"
    ], // Combined both domains into a single array
  },
 staticPageGenerationTimeout: 180, // Increase to 3 minutes
  // Prevent API routes from being statically generated
  async rewrites() {
    return {
      beforeFiles: [],
      afterFiles: [],
      fallback: []
    };
  },

  // Generate static pages configuration
  async generateStaticParams() {
    return [];
  },
  
  // Better approach: Configure API route timeouts
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store, must-revalidate',
          },
        ],
      },
    ];
  },

  // Experimental features to handle the timeout issue
  experimental: {
    esmExternals: false,
  },

  // Webpack configuration to handle modules properly
  webpack: (config, { isServer }) => {
    if (isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
      };
    }
    return config;
  },
};

export default nextConfig;