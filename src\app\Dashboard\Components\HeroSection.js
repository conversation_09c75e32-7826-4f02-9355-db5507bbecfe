"use client";
import AdminDashBDoughnut from "../Components/AdminDashBDoughnut.jsx"
import React, {
  useState,
  useEffect,
} from "react";
import { Bar } from "react-chartjs-2";
import {
  isAuthenticated,
} from "@/utils/verifytoken";
import { useRouter } from "next/navigation";
import {
  Chart as ChartJS,
  BarElement,
  CategoryScale,
  LinearScale,
  Tooltip,
  Legend,
} from "chart.js";
ChartJS.register(BarElement, CategoryScale, LinearScale, Tooltip, Legend);
const HeroSection = ({ TotalCar, carsOnRent, standby }) => {
  const router = useRouter();
  const [timeRange, setTimeRange] = useState("weekly");


  useEffect(() => {
    if (isAuthenticated()) {
      console.log("Total Cars", TotalCar);
    } else {
      router.push("/");
      return;
    }
  }, []);

  const chartData = {
    weekly: {
      labels: ["<PERSON>", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "Fri", "Sat", "Sun"],
      datasets: [
        {
          label: "Weekly Data",
          data: [12, 30, 3, 5, 2, 3, 7],
          backgroundColor: "rgba(54, 162, 235, 0.5)",
          borderColor: "rgba(54, 162, 235, 1)",
          borderWidth: 1,
        },
      ],
    },
    monthly: {
      labels: ["Week 1", "Week 2", "Week 3", "Week 4"],
      datasets: [
        {
          label: "Monthly Data",
          data: [30, 50, 70, 20],
          backgroundColor: "rgba(75, 192, 192, 0.5)",
          borderColor: "rgba(75, 192, 192, 1)",
          borderWidth: 1,
        },
      ],
    },
    yearly: {
      labels: [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
        "Nov",
        "Dec",
      ],
      datasets: [
        {
          label: "Yearly Data",
          data: [120, 190, 300, 500, 200, 300, 400, 100, 150, 250, 300, 450],
          backgroundColor: "rgba(255, 206, 86, 0.5)",
          borderColor: "rgba(255, 206, 86, 1)",
          borderWidth: 1,
        },
      ],
    },
  };



  const carsDetails = {
    labels: ["Hired Cars", "Ready for Hiring", "Cars in Repair", "Car for sale"],
    labels: ["Hired Cars", "Ready for Hiring"],
    datasets: [
      {
        label: "Cars Status",
        data: [carsOnRent, standby, 0, 0],
        backgroundColor: ["#7483F3", "#404CA0", "#27273AEB"],
        borderWidth: 1
      },
    ]
  };

  const Data = {
    labels: ["Total Number of cars", "Rented Cars"],
    datasets: [
      {
        label: "Cars Status",
        data: [TotalCar, carsOnRent],
        backgroundColor: ["#27273AEB", "#404CA0"],
        borderWidth: 1
      },
    ]
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
    }
  }

  return (
    <>
      <h1 className="text-[#313342] font-medium text-2xl mb-5 underline decoration-[#AEADEB] underline-offset-8">Dashboard</h1>
      <div className="flex w-full  gap-10 flex-wrap">
        <AdminDashBDoughnut title="Car Details" data={carsDetails} option={options} extra={"Car Deatils"}  ></AdminDashBDoughnut>
        <div className="flex flex-col h-[310px] sm:h-[250px] md:h-auto sm:max-w-[386px] max-w-full w-full drop-shadow-custom3 rounded-[10px] bg-white justify-between">
          <div className="flex justify-between py-2 sm:py-3 px-3 items-center rounded-[10px]">
            <h3 className="text-base sm:text-lg font-semibold">Statistics</h3>
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e?.target?.value)}
              className="rounded-2xl px-2 text-xs sm:text-sm outline-none bg-[#F8F8FF] w-[80px] sm:w-[91px] h-[28px] sm:h-[30px]"
            >
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
              <option value="yearly">Yearly</option>
            </select>
          </div>
          <div className="flex items-center justify-center px-2 sm:px-3 py-2 sm:py-3 rounded-[10px]">
            <Bar data={chartData[timeRange]} options={options} />
          </div>
        </div>
        <AdminDashBDoughnut title="Data" data={Data} option={options} extra={"Data"}  ></AdminDashBDoughnut>
      </div>
    </>
  );
};

export default HeroSection;