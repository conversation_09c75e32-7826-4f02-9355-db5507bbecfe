"use client";
import React, { useState, useEffect } from "react";
import { API_URL_Company } from "@/app/Dashboard/Components/ApiUrl/ApiUrls";
import axios from "axios";
import { toast } from "react-toastify";
import { AiOutlineEye, AiOutlineEyeInvisible } from "react-icons/ai";
import { getUserId } from '@/utils/storageUtils';
import Image from "next/image";

const UpdateCompanyModel = ({
  isOpen,
  onClose,
  fetchData,
  existingCompanyId,
}) => {
  const [formData, setFormData] = useState({
    CompanyName: "",
    email: "",
    password: "",
    confirmPassword: "",
    isActive: false,
    CreatedBy: "",
    CompanyRegistrationNumber: "",
    vatnumber: "",
    userId: getUserId(),
    mailingAddress: "",
    physical_Address: "",
    phoneNumber: "",
    fax_Number: "",
    generalEmail: "",
    accountsPayableEmail: "",
    specificContactEmail: "",
    title: "",
    accountsPayableContactName: "",
    accountsPayableContactLastName: "",
    accountsPayableContactPhoneNumberandEmail: "",
    billingAddress: "",
    paymentTermsAgreedPaymentSchedule: "",
    paymentTermsPreferredPaymentMethod: "",
    bankingInformationBankName: "",
    bankingInformationBankAccountNumber: "",
    bankingInformationBankIBANSWIFTCode: "",
    bankingInformationBankAddress: "",
    specificDepartmentContactInformationBillingFinanceDepartment: "",
    specificDepartmentContactInformationProcurementPurchasingContact: "",
    specificDepartmentContactInformationPrimaryContactfortheProject: "",
    image: null,
    Postcode: "",
    BuildingAndStreetOne: "",
    BuildingAndStreetTwo: "",
    Town_City: "",
    Country: "",
    isSameAddress: false,

  });

  const autoFillAll = false;
  const [imagePreview, setImagePreview] = useState(null);
  const [step, setStep] = useState(1);
  const nextStep = () => setStep(step + 1);
  const prevStep = () => setStep(step - 1);
  const [showPassword, setShowPassword] = useState(false);
  const strongPasswordRegex = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[^A-Za-z\d])[A-Za-z\d\S]{6,}$/;
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/;

  const [validation, setValidation] = useState({
    emailValid: null,
    passwordMatch: null,
    passwordValid: null,
  });

  useEffect(() => {
    const fetchCompanyDetails = async () => {
      if (existingCompanyId) {
        try {
          const response = await axios.get(
            `${API_URL_Company}/${existingCompanyId}`
          );
          const company = response?.data?.result;
          setFormData({
            CompanyName: company?.CompanyName || "",
            email: company?.email || "",
            password: company?.confirmPassword,
            confirmPassword: company?.confirmPassword,
            CompanyRegistrationNumber: company?.CompanyRegistrationNumber,
            vatnumber: company?.vatnumber,
            isActive: company?.isActive || false,
            CreatedBy: company?.CreatedBy || "",
            mailingAddress: company?.mailingAddress || "",
            physical_Address: company?.physical_Address || "",
            phoneNumber: company?.phoneNumber || "",
            fax_Number: company?.fax_Number || "",
            generalEmail: company?.generalEmail || "",
            accountsPayableEmail: company?.accountsPayableEmail || "",
            specificContactEmail: company?.specificContactEmail || "",
            accountsPayableContactName:
              company?.accountsPayableContactName || "",
            accountsPayableContactLastName: company?.accountsPayableContactLastName || "",
            title: company?.title || "",
            accountsPayableContactPhoneNumberandEmail:
              company?.accountsPayableContactPhoneNumberandEmail || "",
            billingAddress: company?.billingAddress || "",
            paymentTermsAgreedPaymentSchedule:
              company?.paymentTermsAgreedPaymentSchedule || "",
            paymentTermsPreferredPaymentMethod:
              company?.paymentTermsPreferredPaymentMethod || "",
            bankingInformationBankName:
              company?.bankingInformationBankName || "",
            bankingInformationBankAccountNumber:
              company?.bankingInformationBankAccountNumber || "",
            bankingInformationBankIBANSWIFTCode:
              company?.bankingInformationBankIBANSWIFTCode || "",
            bankingInformationBankAddress:
              company?.bankingInformationBankAddress || "",
            specificDepartmentContactInformationBillingFinanceDepartment:
              company?.specificDepartmentContactInformationBillingFinanceDepartment ||
              "",
            specificDepartmentContactInformationProcurementPurchasingContact:
              company?.specificDepartmentContactInformationProcurementPurchasingContact ||
              "",
            specificDepartmentContactInformationPrimaryContactfortheProject:
              company?.specificDepartmentContactInformationPrimaryContactfortheProject ||
              "",
            image: company?.image,

            Postcode: company?.Postcode,
            BuildingAndStreetOne: company?.BuildingAndStreetOne,
            BuildingAndStreetTwo: company?.BuildingAndStreetTwo,
            Town_City: company?.Town_City,
            Country: company?.Country,
          });
          setImagePreview(company?.image);
        } catch (error) {
          console.error("Error fetching company details:", error);
          toast.error("Error fetching company details");
        }
      }
    };

    if (isOpen && existingCompanyId) {
      fetchCompanyDetails();
    }
  }, [existingCompanyId, isOpen]);

  const handleChange = (e) => {
    const { name, value, type, checked, files } = e.target;
    let updatedValue = type === 'checkbox' ? checked : type === 'file' ? files[0] : value;
    if (name === 'username' || name === 'CompanyName') {
      updatedValue = updatedValue.replace(/\s/g, '');
    }

    setFormData((prevData) => ({
      ...prevData,
      [name]: updatedValue,
      ...(name === 'mailingAddress' && autoFillAll
        ? {
          billingAddress: value,
          bankingInformationBankAddress: value,
          physical_Address: value,
        }
        : {}),
    }));

    if (type === 'file' && files && files[0]) {
      const file = files[0];
      setImagePreview(URL.createObjectURL(file));
    }

    if (name === 'email') {
      setValidation((prev) => ({
        ...prev,
        emailValid: emailRegex.test(updatedValue),
      }));
    }

    if (name === 'password' || name === 'confirmPassword') {
      const password = name === 'password' ? updatedValue : formData?.password;
      const confirmPassword = name === 'confirmPassword' ? updatedValue : formData?.confirmPassword;

      setValidation((prev) => ({
        ...prev,
        passwordValid: strongPasswordRegex.test(password),
        passwordMatch: password && confirmPassword ? password === confirmPassword : null,
      }));
    }
  };

  const fileInputRef = React.useRef();
  const handleRemoveImage = () => {
    setFormData((prevData) => ({
      ...prevData,
      image: null,
    }));
    setImagePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };


  useEffect(() => {
    if (isOpen) {
      const passwordValid = strongPasswordRegex?.test(formData?.password);
      const passwordMatch = formData?.password === formData?.confirmPassword;

      setValidation((prev) => ({
        ...prev,
        passwordValid,
        passwordMatch,
      }));
    }
  }, [isOpen, formData.password, formData.confirmPassword]);

  const handleCheckboxChange = (e) => {
    const isChecked = e?.target?.checked;

    setFormData((prev) => {

      const combinedAddress = `${prev?.BuildingAndStreetOne || ""}, ${prev?.BuildingAndStreetTwo || ""}, ${prev?.Town_City || ""}, ${prev?.Country || "UK"}`;

      return {
        ...prev,
        isSameAddress: isChecked,
        billingAddress: isChecked ? combinedAddress : "",
      };
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (formData.password && formData.password !== formData.confirmPassword) {
      toast.warn("Passwords do not match!");
      return;
    }

    const data = new FormData();
    data.append("Postcode", formData.Postcode);
    data.append("BuildingAndStreetOne", formData?.BuildingAndStreetOne);
    data.append("BuildingAndStreetTwo", formData?.BuildingAndStreetTwo);
    data.append("Town_City", formData?.Town_City);
    data.append("Country", formData?.Country);
    data.append("CompanyName", formData?.CompanyName);
    data.append("email", formData?.email);
    if (formData?.password) data.append("password", formData?.password);
    data.append("confirmPassword", formData?.confirmPassword);
    data.append(
      "CompanyRegistrationNumber",
      formData?.CompanyRegistrationNumber
    );
    data.append("vatnumber", formData?.vatnumber);
    data.append("isActive", formData?.isActive);
    data.append("CreatedBy", formData?.CreatedBy || "");
    data.append("mailingAddress", formData?.mailingAddress);
    data.append("physical_Address", formData?.physical_Address);
    data.append("phoneNumber", formData?.phoneNumber);
    data.append("fax_Number", formData?.fax_Number);
    data.append("generalEmail", formData?.generalEmail);
    data.append("accountsPayableEmail", formData?.accountsPayableEmail);
    data.append("specificContactEmail", formData?.specificContactEmail);
    data.append(
      "title",
      formData?.title
    );
    data.append(
      "accountsPayableContactName",
      formData?.accountsPayableContactName
    );
    data.append(
      "accountsPayableContactLastName",
      formData?.accountsPayableContactLastName
    );
    data.append(
      "accountsPayableContactPhoneNumberandEmail",
      formData?.accountsPayableContactPhoneNumberandEmail
    );
    data.append("billingAddress", formData?.billingAddress);
    data.append(
      "paymentTermsAgreedPaymentSchedule",
      formData?.paymentTermsAgreedPaymentSchedule
    );
    data.append(
      "paymentTermsPreferredPaymentMethod",
      formData?.paymentTermsPreferredPaymentMethod
    );
    data.append(
      "bankingInformationBankName",
      formData?.bankingInformationBankName
    );
    data.append(
      "bankingInformationBankAccountNumber",
      formData?.bankingInformationBankAccountNumber
    );
    data.append(
      "bankingInformationBankIBANSWIFTCode",
      formData?.bankingInformationBankIBANSWIFTCode
    );
    data.append(
      "bankingInformationBankAddress",
      formData?.bankingInformationBankAddress
    );
    data.append(
      "specificDepartmentContactInformationBillingFinanceDepartment",
      formData?.specificDepartmentContactInformationBillingFinanceDepartment
    );
    data.append(
      "specificDepartmentContactInformationProcurementPurchasingContact",
      formData?.specificDepartmentContactInformationProcurementPurchasingContact
    );
    data.append(
      "specificDepartmentContactInformationPrimaryContactfortheProject",
      formData?.specificDepartmentContactInformationPrimaryContactfortheProject
    );
    //
    if (formData?.image) data.append("image", formData?.image);

    try {
      const response = await axios.put(
        `${API_URL_Company}/${existingCompanyId}`,
        data,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      if (response?.data?.success) {
        toast.success(response?.data?.message);
        fetchData();
        onClose();
        setStep(1);
      } else {
        toast.success(response?.data?.error);
      }
    } catch (error) {
      console.error("Error updating company details:", error);
    }
  };


  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-gray-800 bg-opacity-60 z-50">
      <div className="bg-white px-12 py-5 rounded-xl shadow-lg w-full max-w-4xl overflow-y-auto">
        <div className=" max-h-[600px] ">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-2xl font-bold">
              Update Company
            </h2>

            <Image width={15} height={15} alt="crossIcon" src="/crossIcon.svg" className="cursor-pointer" onClick={() => {
              onClose();
              setStep(1);
            }} />

          </div>

          <form
            onSubmit={handleSubmit}
            className="space-y-3"
            encType="multipart/form-data"
          >
            {step === 1 && (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-2 ">
                  <div>
                    <div className="flex gap-1 items-center justify-start">
                      <label
                        htmlFor="CompanyName"
                        className="text-[10px] "
                      >
                        Company Name <span className="text-red-600">*</span>
                      </label>
                    </div>

                    <input
                      type="text"
                      id="CompanyName"
                      name="CompanyName"
                      value={formData?.CompanyName}
                      onChange={handleChange}
                      className="block w-full p-2 mt-1 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      required placeholder="Company name"
                    />
                  </div>

                  <div>
                    <div className="flex gap-1 ">
                      <label
                        htmlFor="CompanyRegistrationNumber"
                        className="text-[10px] "
                      >
                        Company Registration Number
                      </label>
                    </div>

                    <input
                      type="text"
                      id="CompanyRegistrationNumber"
                      name="CompanyRegistrationNumber"
                      value={formData?.CompanyRegistrationNumber}
                      onChange={handleChange}
                      placeholder="Company Registration Number"
                      className=" block w-full p-2 border mt-1 border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <div className="flex gap-1">
                      <label
                        htmlFor="vatnumber"
                        className="text-[10px] "
                      >
                        Vat Number
                      </label>
                    </div>

                    <input
                      type="text"
                      id="vatnumber"
                      name="vatnumber"
                      value={formData?.vatnumber}
                      placeholder="VAT number"
                      onChange={handleChange}
                      className="mt-1 block w-full p-2 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <div className="flex gap-1 items-center justify-start">
                      <label
                        htmlFor="email"
                        className="text-[10px] "
                      >
                        Email <span className="text-red-600">*</span>
                      </label>
                    </div>

                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData?.email}
                      onChange={handleChange}
                      placeholder="Email"
                      className={`mt-1 block w-full p-2 border rounded ${validation?.emailValid === null
                        ? 'border-[#********]'
                        : validation?.emailValid
                          ? 'border-green-700'
                          : 'border-red-700'
                        } focus:outline-none`}
                      required
                    />
                    {validation?.emailValid === false && (
                      <p className="text-[8px] text-red-700">Invalid email format</p>
                    )}
                    {validation?.emailValid === true && (
                      <p className="text-[8px] text-green-700">Valid email format</p>
                    )}
                  </div>

                  <div className="relative">
                    <div className="flex gap-1 items-center justify-start">
                      <label
                        htmlFor="password"
                        className="text-[10px] "
                      >
                        Password <span className="text-red-600">*</span>
                      </label>
                    </div>

                    <input
                      type={showPassword ? "text" : "password"}
                      id="password"
                      name="password"
                      value={formData?.password}
                      onChange={handleChange}
                      className="mt-1 block w-full p-2 pr-10 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      required
                      placeholder="Password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword((prev) => !prev)}
                      className="absolute inset-y-0 right-2 flex items-center text-gray-500"
                    >
                      {showPassword ? (
                        <AiOutlineEye size={20} />
                      ) : (
                        <AiOutlineEyeInvisible size={20} />
                      )}
                    </button>
                    <p
                      className={`text-[8px] ${formData?.password?.length > 0 && !validation?.passwordValid
                        ? "text-red-700"
                        : validation?.passwordMatch === false
                          ? "text-red-700"
                          : validation?.passwordMatch && validation?.passwordValid
                            ? "text-green-700"
                            : "text-gray-500"
                        }`}
                    >
                      {formData?.password?.length > 0 && !validation?.passwordValid
                        ? "Use 6+ characters with letters, numbers & symbols"
                        : validation?.passwordMatch === false
                          ? "Confirm Password does not match"
                          : validation?.passwordMatch && validation?.passwordValid
                            ? "Confirm Password matched"
                            : "Enter and confirm your password"}
                    </p>
                  </div>

                  <div>
                    <div className="flex gap-1">
                      <label
                        htmlFor="phoneNumber"
                        className="text-[10px] "
                      >
                        Phone Number <span className="text-red-600">*</span>
                      </label>

                    </div>
                    <input
                      type="text"
                      id="phoneNumber"
                      name="phoneNumber"
                      value={formData?.phoneNumber}
                      onChange={handleChange}
                      className="mt-1 block w-full p-2 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      required placeholder="Phone Number"
                    />
                  </div>

                  <div>
                    <div className="flex gap-1 items-center justify-start">
                      <label
                        htmlFor="confirmPassword"
                        className="text-[10px] "
                      >
                        Confirm Password <span className="text-red-600">*</span>
                      </label>
                    </div>

                    <div className="relative">
                      <input
                        type={showPassword ? "text" : "password"}
                        id="confirmPassword"
                        name="confirmPassword"
                        value={formData?.confirmPassword}
                        onChange={handleChange}
                        className="mt-1 block w-full p-2 pr-10 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"

                        required
                        placeholder="Confirm password"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword((prev) => !prev)}
                        className="absolute inset-y-0 right-2 flex items-center text-gray-500"
                      >
                        {showPassword ? (
                          <AiOutlineEye size={20} />
                        ) : (
                          <AiOutlineEyeInvisible size={20} />
                        )}
                      </button>
                    </div>
                  </div>
                </div>

                <h2 className="font-bold">Address</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                  <div>
                    <div className="flex gap-1 items-center justify-start">
                      <label
                        htmlFor="Building&Street"
                        className="text-[10px] "
                      >
                        Building and Street (Line 1of 2)
                      </label>
                    </div>

                    <input
                      type="text"
                      id="BuildingAndStreetOne"
                      name="BuildingAndStreetOne"
                      value={formData?.BuildingAndStreetOne}
                      onChange={handleChange}
                      className="mt-1 block w-full p-2 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      required placeholder="Building and street"
                    />
                  </div>

                  <div>
                    <div className="flex gap-1">
                      <label
                        htmlFor="Building&Street2"
                        className="text-[10px] "
                      >
                        Building and Street (Line 2 of 2)
                      </label>
                    </div>

                    <input
                      type="text"
                      id="Building&Street2"
                      name="BuildingAndStreetTwo"
                      value={formData?.BuildingAndStreetTwo}
                      onChange={handleChange}
                      className="mt-1 block w-full p-2 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      required placeholder="Building and street"
                    />
                  </div>

                  <div>
                    <select
                      id="Country"
                      name="Country"
                      value={formData?.Country === 'UK' ? 'UK' : 'other'}
                      onChange={(e) => {
                        const selected = e?.target?.value;
                        setFormData((prev) => ({
                          ...prev,
                          Country: selected === 'UK' ? 'UK' : '',
                        }));
                      }}
                      className="mt-1 block w-full p-2 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      required
                    >
                      <option value="UK">UK</option>
                      <option value="other">Other</option>
                    </select>

                    {formData.Country !== 'UK' && (
                      <input
                        type="text"
                        name="Country"
                        placeholder="Enter country"
                        value={formData?.Country}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            Country: e?.target?.value,
                          }))
                        }
                        className="mt-2 block w-full p-2 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                    )}
                  </div>

                  <div>
                    <div className="flex gap-1">
                      <label htmlFor="Town_City" className="text-[10px]">
                        Town/City
                      </label>
                    </div>

                    {formData?.Country === 'other' || formData?.Country !== 'UK' ? (
                      <input
                        type="text"
                        id="Town_City"
                        name="Town_City"
                        placeholder="Enter Town/City"
                        value={formData?.Town_City}
                        onChange={handleChange}
                        className="mt-1 block w-full p-2 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                    ) : (
                      <select
                        id="Town_City"
                        name="Town_City"
                        value={formData?.Town_City}
                        onChange={handleChange}
                        className="mt-1 block w-full p-2 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        required
                      >
                        <option value="">Select Town/City</option>
                        <option value="London">London</option>
                        <option value="Birmingham">Birmingham</option>
                        <option value="Manchester">Manchester</option>
                        <option value="Glasgow">Glasgow</option>
                        <option value="Liverpool">Liverpool</option>
                        <option value="Leeds">Leeds</option>
                        <option value="Sheffield">Sheffield</option>
                        <option value="Edinburgh">Edinburgh</option>
                        <option value="Bristol">Bristol</option>
                        <option value="Cardiff">Cardiff</option>
                        <option value="Nottingham">Nottingham</option>
                        <option value="Leicester">Leicester</option>
                        <option value="Coventry">Coventry</option>
                        <option value="Kingston upon Hull">Kingston upon Hull</option>
                        <option value="Newcastle upon Tyne">Newcastle upon Tyne</option>
                        <option value="Brighton">Brighton</option>
                        <option value="Southampton">Southampton</option>
                        <option value="Portsmouth">Portsmouth</option>
                        <option value="Derby">Derby</option>
                        <option value="Stoke-on-Trent">Stoke-on-Trent</option>
                        <option value="Wolverhampton">Wolverhampton</option>
                        <option value="Plymouth">Plymouth</option>
                        <option value="Aberdeen">Aberdeen</option>
                        <option value="Norwich">Norwich</option>
                        <option value="Luton">Luton</option>
                      </select>
                    )}
                  </div>


                  <div>
                    <div className="flex gap-1">
                      <label
                        htmlFor="CompanyName"
                        className="text-[10px] "
                      >
                        Postcode
                      </label>
                    </div>

                    <input
                      type="text"
                      id="Building&Street"
                      name="Postcode"
                      value={formData?.Postcode}
                      onChange={handleChange}
                      className="mt-1 block w-full p-2 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      required placeholder="Postcode"
                    />
                  </div>
                </div>

                <div className="flex gap-[10px] justify-end pb-5">
                  <button
                    type="button"
                    onClick={() => {
                      onClose();
                      setStep(1);
                    }}
                    className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={nextStep}
                    disabled={
                      !formData?.CompanyName?.trim() ||
                      !formData?.email?.trim() ||
                      !formData?.phoneNumber?.trim()
                    }
                    className={`bg-[#313342] text-white rounded-4 hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 py-1 px-8 ${!formData?.CompanyName?.trim() ||
                      !formData?.email?.trim() ||
                      !formData?.phoneNumber?.trim()
                      ? 'opacity-50 cursor-not-allowed'
                      : ''
                      }`}
                  >
                    Next
                  </button>

                </div>
              </>
            )}
            {step === 2 && (
              <>
                <div className="">
                  <h2 className="font-bold">
                    Accounts Payable Contact:
                  </h2>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-2">
                    <div>
                      <div className="flex gap-1 items-center justify-start">
                        <label
                          htmlFor="taxiFirm"
                          className="text-[10px]"
                        >
                          Title <span className="text-red-600">*</span>
                        </label>
                      </div>

                      <select
                        id="title"
                        name="title"
                        value={formData?.title}
                        onChange={handleChange}
                        className="mt-1 block w-full p-2 border border-[#********] rounded shadow"
                        required
                      >
                        <option value="">Select Title</option>
                        <option value="Mr">Mr</option>
                        <option value="Miss">Miss</option>
                        <option value="Mrs">Mrs</option>
                      </select>
                    </div>

                    <div>
                      <div className="flex gap-1">
                        <label
                          htmlFor="accountsPayableContactName"
                          className="text-[10px] "
                        >
                          First Name
                        </label>
                      </div>
                      <input
                        type="text"
                        id="accountsPayableContactName"
                        name="accountsPayableContactName"
                        value={formData?.accountsPayableContactName}
                        onChange={handleChange}
                        className="mt-1 block w-full p-2 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        placeholder="First Name"
                      />
                    </div>

                    <div>
                      <div className="flex gap-1">
                        <label
                          htmlFor="accountsPayableContactLastName"
                          className="text-[10px] "
                        >
                          Last Name
                        </label>
                      </div>
                      <input
                        type="text"
                        id="accountsPayableContactLastName"
                        name="accountsPayableContactLastName"
                        value={formData?.accountsPayableContactLastName}
                        onChange={handleChange}
                        className="mt-1 block w-full p-2 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Last Name"
                      />
                    </div>

                   
                    <div>
                      <div className="flex gap-1">
                        <label
                          htmlFor="billingAddress"
                          className="text-[10px] "
                        >
                          Billing Address
                        </label>
                      </div>
                      <input
                        type="text"
                        id="billingAddress"
                        name="billingAddress"
                        value={formData?.billingAddress}
                        onChange={handleChange}
                        className="mt-1 block w-full p-2 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Billing Address"
                        readOnly
                      />
                    </div>
                    <div className="mt-2">
                      <label className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={formData?.isSameAddress}
                          onChange={handleCheckboxChange}
                        />
                        Same as Old address
                      </label>
                    </div>

                    <div>
                      <div className="flex gap-1">
                        <label
                          htmlFor="paymentTermsAgreedPaymentSchedule"
                          className="text-[10px] "
                        >
                          Agreed Payment Schedule
                        </label>
                      </div>
                      <select
                        id="paymentTermsAgreedPaymentSchedule"
                        name="paymentTermsAgreedPaymentSchedule"
                        value={formData?.paymentTermsAgreedPaymentSchedule}
                        onChange={handleChange}
                        className="mt-1 block w-full p-2 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="">Select</option>
                        <option value="30">30 days</option>
                        <option value="60">60 days</option>
                        <option value="90">90 days</option>
                        <option value="120">120 days</option>
                        <option value="150">150 days</option>
                      </select>
                    </div>

                    <div>
                      <div className="flex gap-1">
                        <label
                          htmlFor="paymentTermsPreferredPaymentMethod"
                          className="text-[10px] "
                        >
                          Preferred Payment Method
                        </label>
                      </div>
                      <select
                        id="paymentTermsPreferredPaymentMethod"
                        name="paymentTermsPreferredPaymentMethod"
                        value={formData?.paymentTermsPreferredPaymentMethod}
                        onChange={handleChange}
                        className="mt-1 block w-full p-2 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="">Select</option>
                        <option value="Cash">Cash</option>
                        <option value="CardPayment">Card Payment</option>
                        <option value="BankTransfer">Bank Transfer</option>
                        <option value="Crypto">Crypto</option>
                      </select>
                    </div>
                  </div>

                  <h2 className="font-bold mt-3">
                    Banking Information
                  </h2>

                  <div className="grid grid-cols-2 sm:grid-cols-2  gap-x-6 gap-y-4 mt-1">
                    <div>
                      <div className="flex gap-1">
                        <label
                          htmlFor="bankingInformationBankName"
                          className="text-[10px] "
                        >
                          Bank Name
                        </label>
                      </div>
                      <input
                        type="text"
                        id="bankingInformationBankName"
                        name="bankingInformationBankName"
                        value={formData?.bankingInformationBankName}
                        onChange={handleChange}
                        className="mt-1 block w-full p-2 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Bank name"
                      />
                    </div>

                    <div>
                      <div className="flex gap-1">
                        <label
                          htmlFor="bankingInformationBankAccountNumber"
                          className="text-[10px] "
                        >
                          Bank Account Number
                        </label>
                      </div>
                      <input
                        type="text"
                        id="bankingInformationBankAccountNumber"
                        name="bankingInformationBankAccountNumber"
                        value={formData?.bankingInformationBankAccountNumber}
                        onChange={handleChange}
                        className="mt-1 block w-full p-2 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Bank account number"
                      />
                    </div>

                    <div>
                      <div className="flex gap-1">
                        <label
                          htmlFor="bankingInformationBankIBANSWIFTCode"
                          className="text-[10px] "
                        >
                          IBAN / SWIFT Code
                        </label>
                      </div>
                      <input
                        type="text"
                        id="bankingInformationBankIBANSWIFTCode"
                        name="bankingInformationBankIBANSWIFTCode"
                        value={formData?.bankingInformationBankIBANSWIFTCode}
                        onChange={handleChange}
                        className="mt-1 block w-full p-2 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        placeholder="IBAN / SWIFT Code"
                      />
                    </div>

                    <div>
                      <div className="flex gap-1">
                        <label
                          htmlFor="bankingInformationBankAddress"
                          className="text-[10px] "
                        >
                          Bank Address
                        </label>
                      </div>
                      <input
                        type="text"
                        id="bankingInformationBankAddress"
                        name="bankingInformationBankAddress"
                        value={formData?.bankingInformationBankAddress}
                        onChange={handleChange}
                        className="mt-1 block w-full p-2 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Bank address"
                      />
                    </div>
                  </div>

                  <h2 className="font-bold  mt-3">Specific Department Contact Information</h2>

                  <div className="grid grid-cols-2 sm:grid-cols-2  gap-x-6 gap-y-4 mt-1">
                    <div>
                      <div className="flex gap-1">
                        <label
                          htmlFor="specificDepartmentContactInformationBillingFinanceDepartment"
                          className="text-[10px]"
                        >
                          Billing / Finance Department
                        </label>
                      </div>
                      <input
                        type="text"
                        id="specificDepartmentContactInformationBillingFinanceDepartment"
                        name="specificDepartmentContactInformationBillingFinanceDepartment"
                        value={
                          formData?.specificDepartmentContactInformationBillingFinanceDepartment
                        }
                        onChange={handleChange}
                        className="mt-1 block w-full p-2 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Billing / Finance Department"
                      />
                    </div>

                    <div>
                      <div className="flex gap-1">
                        <label
                          htmlFor="specificDepartmentContactInformationProcurementPurchasingContact"
                          className="text-[10px] "
                        >
                          Procurement / Purchasing Contact
                        </label>
                      </div>
                      <input
                        type="text"
                        id="specificDepartmentContactInformationProcurementPurchasingContact"
                        name="specificDepartmentContactInformationProcurementPurchasingContact"
                        value={
                          formData?.specificDepartmentContactInformationProcurementPurchasingContact
                        }
                        onChange={handleChange}
                        className="mt-1 block w-full p-2 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Procurement / Purchasing Contact"
                      />
                    </div>

                    <div>
                      <div className="flex gap-1">
                        <label
                          htmlFor="specificDepartmentContactInformationPrimaryContactfortheProject"
                          className="text-[10px] "
                        >
                          Primary Contact for the Project
                        </label>
                      </div>
                      <input
                        type="text"
                        id="specificDepartmentContactInformationPrimaryContactfortheProject"
                        name="specificDepartmentContactInformationPrimaryContactfortheProject"
                        value={
                          formData?.specificDepartmentContactInformationPrimaryContactfortheProject
                        }
                        onChange={handleChange}
                        className="mt-1 block w-full p-2 border border-[#********] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Primary Contact for the Project"
                      />
                    </div>

                    <div>
                      <label className="text-[10px]">Status</label>
                      <div className="flex gap-6 p-2">
                        <label className="flex items-center gap-2">
                          <input
                            type="radio"
                            name="isActive"
                            value="true"
                            checked={formData?.isActive === true}
                            onChange={() =>
                              handleChange({
                                target: { name: "isActive", value: true },
                              })
                            }
                            className="accent-green-700"
                          />
                          <span className="text-xs">Active</span>
                        </label>

                        <label className="flex items-center gap-2">
                          <input
                            type="radio"
                            name="isActive"
                            value="false"
                            checked={formData?.isActive === false}
                            onChange={() =>
                              handleChange({
                                target: { name: "isActive", value: false },
                              })
                            }
                            className="accent-black"
                          />
                          <span className="text-xs">Inactive</span>
                        </label>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <label
                        htmlFor="image"
                        className="text-[10px]"
                      >
                        Upload Image:
                      </label>
                      <input
                        type="file"
                        id="image"
                        name="image"
                        ref={fileInputRef}
                        accept="image/*"
                        onChange={handleChange}
                        className="mt-1 block w-48 text-[8px] text-gray-400 file:mr-4 file:py-1 p-2 file:px-4 file:rounded-lg file:border file:text-[10px] file:font-semibold file:bg-white hover:file:bg-blue-100 border border-[#0885864D] rounded-[10px] border-dashed "
                      />
                    </div>
                    {imagePreview && (
                      <div className="relative w-32 mt-2">
                        <Image width={100} height={100} src={imagePreview} alt="Preview" className="w-full h-auto rounded-md border" />
                        <button
                          type="button"
                          onClick={handleRemoveImage}
                          className="absolute top-0 right-0 bg-red-500 text-white rounded-full w-5 h-5 text-xs flex items-center justify-center transform translate-x-1/2 -translate-y-1/2"
                        >
                          ×
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                <div className="mt-6 flex  justify-between pb-5">
                  <button
                    type="button"
                    onClick={prevStep}
                    className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                  >
                    Back
                  </button>

                  <div className="flex gap-[10px]">
                    <button
                      type="button"
                      onClick={() => {
                        onClose();
                        setStep(1);
                      }}
                      className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                    >
                      Close
                    </button>


                    <button
                      type="submit"
                      disabled={!formData?.accountsPayableContactPhoneNumberandEmail}
                      className={`bg-[#313342] text-white rounded-4 hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 py-1 px-8 ${!formData?.accountsPayableContactPhoneNumberandEmail ? 'opacity-50 cursor-not-allowed' : ''
                        }`}
                    >
                      Submit
                    </button>
                  </div>
                </div>


              </>
            )}
          </form>
        </div>
      </div>
    </div>
  );
};

export default UpdateCompanyModel;
