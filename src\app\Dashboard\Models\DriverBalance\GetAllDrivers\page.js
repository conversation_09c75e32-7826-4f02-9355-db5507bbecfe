"use client";
import React, { useState, useEffect, useCallback } from "react";
import Header from "../../../Components/Header";
import Sidebar from "../../../Components/Sidebar";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import AddDriverModel from "../../../../Dashboard/Driver/AddDriver/AddDriverModel";
import UpdateDriverModel from "../../../../Dashboard/Driver/UpdateDriver/UpdateDriverModel";
import axios from "axios";
import { API_URL_Driver } from "../../../Components/ApiUrl/ApiUrls";
import {
  getCompanyName, getUserName
} from "@/utils/storageUtils"; import Link from "next/link";
import { isAuthenticated } from "@/utils/verifytoken";
import { useRouter } from "next/navigation";
import DeleteModal from "../../../Components/DeleteModal";
import Image from "next/image";
import {Count} from "@/utils/count";
import SearchAndAddBar from "@/utils/searchAndAddBar";
import Pagination from "@/utils/pagination";

const Page = () => {
  const router = useRouter();
  const [drivers, setDrivers] = useState([]);
  const [baseFilteredData, setBaseFilteredData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [paginatedData, setPaginatedData] = useState([]);
  const [isMounted, setIsMounted] = useState(false);
  const [isOpenDriver, setIsOpenDriver] = useState(false);
  const [selectedCompanyName, setSelectedCompanyName] = useState("");
  const [itemperpage, setitemperpage] = useState(Count);

  const handleFilterChange = useCallback((filtered) => {
    setFilteredData(filtered);
  }, []);

  const handleItemsPerPageChange = useCallback((newItemsPerPage) => {
    setitemperpage(newItemsPerPage);
  }, []);

  useEffect(() => {
    if (!isAuthenticated()) {
      router.push("/");
      return;
    }
  }, [router]);

  useEffect(() => {
    setIsMounted(true);
    const companyNameFromStorage = (() => {
      const name1 = getCompanyName();
      if (name1) return name1;

      const name2 = getUserName();
      if (name2) return name2;
    })();
    if (companyNameFromStorage) {
      setSelectedCompanyName(companyNameFromStorage);
    }
  }, []);

  const fetchData = useCallback(async () => {
    try {
      const response = await axios.get(`${API_URL_Driver}`);
      setDrivers(response?.data?.result);
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("An error occurred while fetching drivers.");
    }
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    if (drivers.length > 0 && selectedCompanyName) {
      const companyFiltered = drivers?.filter((driver) => {
        return driver?.adminCompanyName &&
          driver?.adminCompanyName?.toLowerCase() === selectedCompanyName?.toLowerCase();
      });
      setBaseFilteredData(companyFiltered);
    } else {
      setBaseFilteredData([]);
    }
  }, [drivers, selectedCompanyName]);

  useEffect(() => {
    setFilteredData(baseFilteredData);
  }, [baseFilteredData]);

  const handleDelete = async (id) => {
    try {
      const response = await axios.delete(`${API_URL_Driver}/${id}`);
      const { data } = response;
      if (data?.success) {
        setDrivers((prevData) => prevData?.filter((item) => item?._id !== id));
      } else {
        toast.warn(data?.message || "Failed to delete the driver.");
      }
    } catch (error) {
      console.error("Error deleting driver:", error);
      toast.error(
        "An error occurred while deleting the driver. Please try again."
      );
    }
  };

  if (!isMounted) return null;

  const OpenDriverModel = () => {
    setIsOpenDriver(!isOpenDriver);
  };

  const formatDate = (dateString) => {
    const dateObject = new Date(dateString);
    return `${(dateObject.getMonth() + 1)
      .toString()
      .padStart(2, "0")}/${dateObject
        .getDate()
        .toString()
        .padStart(2, "0")}/${dateObject.getFullYear()}`;
  };

  const totalBalance = paginatedData?.reduce(
    (acc, driver) => acc + (Number(driver?.totalamount) || 0),
    0
  );

  return (
    <div className="h-[100vh] overflow-hidden">
      <Header className="min-w-full" />
      <div className="flex gap-4">
        <Sidebar />
        <div
          className="w-[80%] xl:w-[85%] h-screen flex flex-col justify-start overflow-y-auto pr-4"
          style={{
            height: "calc(100vh - 90px)",
          }}
        >
          <h1 className="text-[#313342] font-medium text-2xl py-5 underline decoration-[#AEADEB] underline-offset-8">
            Balance
          </h1>

          <div className="py-5">
            <div className="drop-shadow-custom4">
              <SearchAndAddBar
                data={baseFilteredData}
                itemperpage={itemperpage}
                onFilterChange={handleFilterChange}
                onItemsPerPageChange={handleItemsPerPageChange}
                searchPlaceholder="Search by email"
                showDropdown={true}
                showSearch={true}
                showAddButton={false}
              />

              <div className="overflow-x-auto custom-scrollbar">
                <table className="w-full bg-white border table-auto">
                  <thead className="font-sans font-bold text-sm text-center">
                    <tr className="text-white bg-[#38384A]">
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Name
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Email
                      </th>

                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Phone Number
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        License Number
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Ni Number
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Badge Type
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Date Of Birth
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Balance
                      </th>
                      <th className="py-3 px-4 min-w-[180px] w-[180px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="font-sans font-medium text-sm">
                    {paginatedData?.map((driver) => (
                      <tr key={driver?._id} className="border-b text-center">
                        <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-center whitespace-normal break-all overflow-hidden">
                          {driver?.firstName} {driver?.lastName}
                        </td>
                        <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                          {driver?.email}
                        </td>
                        <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                          {driver?.tel1}
                        </td>
                        <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                          {driver?.licenseNumber}
                        </td>
                        <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                          {driver?.niNumber}
                        </td>
                        <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                          {driver?.badgeType}
                        </td>
                        <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                          {formatDate(driver?.dateOfBirth)}
                        </td>
                        <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                          {driver?.totalamount}
                        </td>
                        <td className="py-3 px-4 min-w-[180px] w-[180px] md:w-[16.66%] whitespace-normal break-all overflow-hidden text-center">
                          <div className="flex gap-4 justify-center">
                            
                            <button>
                              <Link
                                passHref
                                href={`/Dashboard/Models/DriverBalance/${driver?._id}`}
                              >
                                <div className="flex items-center gap-3">
                                  <Image width={25} height={25}  src="/bcar.png" alt="info" />
                                </div>
                              </Link>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                    <tr>
                      <td >

                      </td>
                      <td >

                      </td>
                      <td >

                      </td>
                      <td >

                      </td>
                      <td >

                      </td>
                      <td >

                      </td>
                      <td className="text-center" >
                        Company Total :
                      </td>
                      <td className="text-center" >
                        £ {totalBalance?.toFixed(0)}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <Pagination
                data={filteredData}
                itemperpage={itemperpage}
                onPageChange={(currentItems) => {
                  setPaginatedData(currentItems);
                }}
              />
            </div>
          </div>
        </div>
      </div>

      <AddDriverModel
        isOpen={isOpenDriver}
        onClose={OpenDriverModel}
        fetchData={fetchData}
      />
      <UpdateDriverModel
        fetchDataa={fetchData}
      />
      <DeleteModal
        onDelete={handleDelete}
      />
    </div>
  );
};

export default Page;