"use client";

import React, { useState, useEffect, useCallback } from "react";
import Header from "../../Components/Header";
import Sidebar from "../../Components/Sidebar";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import AddVehicleModel from "../AddVehicle/AddVehicleModel";
import UpdateVehicleModel from "../UpdateVehicleModel/UpdateVehicleModel";
import axios from "axios";
import { API_URL_Vehicle } from "../../Components/ApiUrl/ApiUrls";
import { getCompanyName } from "@/utils/storageUtils";
import Link from "next/link";
import { isAuthenticated } from "@/utils/verifytoken";
import { useRouter } from "next/navigation";
import DeleteModal from "../../Components/DeleteModal";
import Image from "next/image";
import {Count} from "@/utils/count";
import SearchAndAddBar from "@/utils/searchAndAddBar";
import Pagination from "@/utils/pagination";

const Page = () => {
  const router = useRouter();
  const [vehicle, setVehicle] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [searchTerm] = useState("");
  const [isMounted, setIsMounted] = useState(false);
  const [isOpenVehicle, setIsOpenVehicle] = useState(false);
  const [selectedCompanyName, setSelectedCompanyName] = useState("");
  const [selectedUserId, setSelectedUserId] = useState(null);
  const [isOpenVehicleUpdate, setIsOpenVehcleUpdate] = useState(false);
  const [openDropdownId, setOpenDropdownId] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleteModalOpenId, setIsDeleteModalOpenId] = useState(null);

  const [itemperpage, setitemperpage] = useState(Count);
  const [paginatedData, setPaginatedData] = useState([]);

  const handleFilterChange = useCallback((filtered) => {
    setFilteredData(filtered);
  }, []);

  const handleItemsPerPageChange = useCallback((newItemsPerPage) => {
    setitemperpage(newItemsPerPage);
  }, []);

  useEffect(() => {
    if (!isAuthenticated()) {
      router.push("/");
      return;
    }
  }, [router]);
  useEffect(() => {
    setIsMounted(true);
    const companyNameFromStorage = getCompanyName();
    if (companyNameFromStorage) {
      setSelectedCompanyName(companyNameFromStorage);
    }
  }, []);


  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const response = await axios.get(`${API_URL_Vehicle}`);
      setVehicle(response?.data?.result);
      setFilteredData(response?.data?.result);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  const isopendeletemodel = (id) => {
    setIsDeleteModalOpenId(id);
    setIsDeleteModalOpen(true);
  };

  const handleDelete = async (id) => {
    try {
      const response = await axios.delete(`${API_URL_Vehicle}/${id}`);
      const { data } = response;
      if (data.success) {
        setVehicle((prevData) => prevData.filter((item) => item._id !== id));
        setFilteredData((prevFilteredData) =>
          prevFilteredData.filter((item) => item._id !== id)
        );
      } else {
        toast.warn(data.message || "Failed to delete the vehicle.");
      }
    } catch (error) {
      console.error("Error deleting vehicle:", error);
      toast.error(
        "An error occurred while deleting the vehicle. Please try again."
      );
    }
  };

  useEffect(() => {
    const filtered = vehicle?.filter((item) => {
      const companyMatch =
        item.adminCompanyName &&
        selectedCompanyName &&
        item.adminCompanyName.toLowerCase() ===
        selectedCompanyName.toLowerCase();

      const usernameMatch =
        item &&
        item.manufacturer &&
        item.manufacturer.toLowerCase().includes(searchTerm.toLowerCase());

      return companyMatch && usernameMatch;
    });
    setFilteredData(filtered);
  }, [searchTerm, vehicle, selectedCompanyName]);

  const handleEdit = (id) => {
    setSelectedUserId(id);
    setIsOpenVehcleUpdate(true);
  };

  if (!isMounted) {
    return null;
  }

  const OpenVehicleModle = () => {
    setIsOpenVehicle(!isOpenVehicle);
  };

  const OpenVehicleUpdateModle = () => {
    setIsOpenVehcleUpdate(!isOpenVehicleUpdate);
  };

  const toggleDropdown = (id) => {
    setOpenDropdownId(openDropdownId === id ? null : id);
  };
  return (
    <div className="h-[100vh] overflow-hidden">
      <Header className="min-w-full" />
      <div className="flex gap-4">
        <Sidebar />
        <div
          className="w-[80%] xl:w-[85%] h-screen flex flex-col justify-start overflow-y-auto pr-4"
          style={{
            height: "calc(100vh - 90px)",
          }}
        >
          <h1 className="text-[#313342] font-medium text-2xl py-5 underline decoration-[#AEADEB] underline-offset-8">
            Vehicle
          </h1>

          <div className="py-5">
            <div className="drop-shadow-custom4">
              <SearchAndAddBar
                data={vehicle}
                itemperpage={itemperpage}
                onAddClick={OpenVehicleModle}
                addLabel="Add Vehicle"
                onFilterChange={handleFilterChange}
                onItemsPerPageChange={handleItemsPerPageChange}
                searchPlaceholder="Search by Manufacturer"
              />

              <div className="overflow-x-auto custom-scrollbar">
                <table className="w-full bg-white border table-auto">
                  <thead className="font-sans font-bold text-sm text-center">
                    <tr className="text-white bg-[#38384A]">
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[12.5%]  text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Manufacturer
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[12.5%]  text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Model
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[12.5%]  text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Year
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[12.5%]  text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Vehicle Status
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[12.5%]  text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Type
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[12.5%]  text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Engine Type
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[12.5%]  text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Documents
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[12.5%]  text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Registration No
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[12.5%]  text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Status
                      </th>
                      <th className="py-3 px-4 min-w-[180px] w-[180px] md:w-[12.5%]  text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="font-sans font-medium text-sm text-center">
                    {paginatedData?.map((row) => (
                      <tr key={row?._id} className="border-b relative">
                        <td className="py-3 px-4 text-center whitespace-normal break-all overflow-hidden">
                          {row?.manufacturer}
                        </td>
                        <td className="py-3 px-4 whitespace-normal text-center break-all overflow-hidden">
                          {row?.model}
                        </td>
                        <td className="py-3 px-4 whitespace-normal break-all overflow-hidden">
                          {row?.year}
                        </td>
                        <td className="py-3 px-4 whitespace-normal break-all overflow-hidden">
                          {row?.vehicleStatus}
                        </td>
                        <td className="py-3 px-4 whitespace-normal break-all overflow-hidden">
                          {row?.type}
                        </td>
                        <td className="py-3 px-4 whitespace-normal break-all overflow-hidden">
                          {row?.engineType}
                        </td>
                        <td className="py-3 px-4 text-center">
                          <span className={`${row?.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'} px-4 py-2 rounded-3xl text-sm`}>
                            {row?.isActive ? "Verified" : "Not Verified "}
                          </span>
                        </td>
                        <td className="py-3 px-4 whitespace-normal break-all overflow-hidden">
                          {row?.registrationNumber}
                        </td>
                        <td className="py-3 px-4 text-center">
                          <span className="bg-gray-400 px-4 py-2 rounded-3xl text-sm">
                            {row?.isActive ? "Active" : "Inactive"}
                          </span>
                        </td>
                        <td className="py-3 px-4 relative">
                          <div className="flex gap-2 justify-center items-center">
                            <div className="relative group flex items-center justify-center">
                              <button
                                onClick={() => handleEdit(row?._id)}
                                className="text-blue-500 hover:text-blue-700"
                              >
                                <Image width={25} height={25}
                                  src="/edit.png"
                                  alt="edit"
                                  className="w-6"
                                  title="Edit Vehicle details"
                                />
                              </button>
                            </div>
                            <div className="relative group flex items-center justify-center">
                              <button
                                onClick={() => toggleDropdown(row?._id)}
                                className="text-gray-500 hover:text-gray-700 underline"
                              >
                                <Image width={25} height={25}
                                  src="/reporticon.svg"
                                  alt="delete"
                                  className="w-6"
                                  title="View Reports"
                                />
                              </button>
                            </div>

                            <div className="relative group flex items-center justify-center">
                              <button
                                onClick={() => isopendeletemodel(row?._id)}
                                className="text-red-500 hover:text-red-700"
                              >
                                <Image width={25} height={25}
                                  src="/trash.png"
                                  alt="delete"
                                  className="w-6"
                                  title="Delete Vehicle"
                                />
                              </button>
                            </div>

                          </div>


                        </td>
                        {openDropdownId === row?._id && (
                          <div className="fixed  right-24   bg-white border border-gray-200 shadow-lg rounded-md  w-[170px] z-50">
                            <div className="h-8 rounded-t-md">
                              <Link
                                href={`/Dashboard/Vehicle/VehicleReports/${row?._id}`}
                                className="w-full px-2 rounded-t-md h-full flex justify-start items-center  hover:bg-[#313342] opacity-80 hover:text-white"
                              >
                                Car Details
                              </Link>
                            </div>
                            <div className="h-8">
                              <Link
                                href={`/Dashboard/Vehicle/AddMaintenanceReport/${row?._id}`}
                                className="w-full px-2 h-full flex justify-start items-center  hover:bg-[#313342] opacity-80 hover:text-white"
                              >
                                Maintenance Report
                              </Link>
                            </div>
                            <div className="h-8">
                              <Link
                                href={`/Dashboard/Vehicle/AddMOTReport/${row?._id}`}
                                className="w-full px-2 h-full flex justify-start items-center  hover:bg-[#313342] opacity-80 hover:text-white"
                              >
                                MOT Report
                              </Link>
                            </div>
                            <div className="h-8">
                              <Link
                                href={`/Dashboard/Vehicle/AddServiceReport/${row?._id}`}
                                className="w-full px-2 h-full flex justify-start items-center  hover:bg-[#313342] opacity-80 hover:text-white"
                              >
                                Service Report
                              </Link>
                            </div>
                            <div className="h-8 rounded-b-md">
                              <Link
                                href={`/Dashboard/Vehicle/AddRoadTaxReport/${row?._id}`}
                                className="w-full rounded-b-md px-2 h-full flex justify-start items-center  hover:bg-[#313342] opacity-80 hover:text-white"
                              >
                                Road Tax Report
                              </Link>
                            </div>
                          </div>
                        )}

                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <Pagination
                data={filteredData}
                itemperpage={itemperpage}
                onPageChange={(currentItems) => {
                  setPaginatedData(currentItems);
                }}
              />
            </div>
          </div>
        </div>
      </div>
      <AddVehicleModel
        isOpen={isOpenVehicle}
        onClose={OpenVehicleModle}
        fetchData={fetchData}
      />
      <UpdateVehicleModel
        isOpen={isOpenVehicleUpdate}
        onClose={OpenVehicleUpdateModle}
        fetchData={fetchData}
        vehicleId={selectedUserId}
      />
      <DeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onDelete={handleDelete}
        Id={isDeleteModalOpenId}
      />
    </div>
  );
};

export default Page;
