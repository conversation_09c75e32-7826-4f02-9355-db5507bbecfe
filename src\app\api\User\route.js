import { connect } from "@config/db.js";
import User from "@models/User/User.Model.js";
import { catchAsyncErrors } from "@middlewares/catchAsyncErrors.js";
import { NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import { uploadImage } from "services/uploadImage.js"; // Import the global uploadImage utility
export async function POST(request) {
  try {
    await connect();
    const data = await request.formData();

    // Handling the uploaded files
    let file1 = data.get("useravatar");

    // Use the global uploadImage utility
    const { imageFile: useravatar, imagePublicId: useravatarId } = await uploadImage(file1);

    // Constructing formDataObject excluding the files
    const formDataObject = {};
    for (const [key, value] of data.entries()) {
      if (key !== "useravatar") {
        formDataObject[key] = value;
      }
    }

    const {
      title,
      firstName,
      lastName,
      email,
      tel1,
      tel2,
      postcode,
      postalAddress,
      permanentAddress,
      city,
      county,
      // accessLevel,
      dateOfBirth,
      position,
      reportsTo,
      username,
      password,
      passwordExpires,
      // passwordExpiresEvery,
      confirmpassword,
      companyName,
      CreatedBy,
      isActive,
      role,
      companyId,
      Postcode,
    BuildingAndStreetOne,
    BuildingAndStreetTwo,
    Town_City,
    Country,
    } = formDataObject;
    if (password !== confirmpassword) {
      return NextResponse.json({
        error: "Passwords does not match",
        status: 400,
      });
    }

// Check if a user with the same email or company name already exists
const existingUser = await User.findOne({
  $or: [{ email: email }, { companyName: companyName }, {confirmpassword : password}],
});

const existingUsername = await User.findOne({
  username: username,
  companyName: companyName,
});

if (existingUsername) {
  return NextResponse.json({
    error: "User with this username and in this company already exists",
    status: 400,
  });
}


if (existingUser) {
  // Check if the email exists alone or the company name exists
  if (existingUser.email === email && existingUser.companyName === companyName) {
    return NextResponse.json({
      error: "User with this email and in this company already in use",
      status: 400,
    });
  } else if (existingUser.email === email && existingUser.companyName === companyName && existingUser.confirmpassword === password) {
    return NextResponse.json({
      error: "User with this email And Password already in use",
      status: 400,
    });
  }
  else if (existingUser.email === email) {
    return NextResponse.json({
      error: "User with this email already in use",
      status: 400,
    });
  }
}

// Continue with user registration logic if no conflict is found

const hashedPassword = await bcrypt.hash(password, 10);
    // Create and save the new blog entry
    const newUser = new User({
      title,
      firstName,
      lastName,
      email,
      tel1,
      tel2,
      postcode,
      postalAddress,
      permanentAddress,
      city,
      county,
      // accessLevel,
      dateOfBirth,
      position,
      reportsTo,
      username,
      password: hashedPassword,
      passwordExpires,
      // passwordExpiresEvery,
      confirmpassword,
      companyName,
      CreatedBy, // Ensure this field matches your schema
      useravatar: useravatar, // Use the uploaded or dummy image
      userPublicId: useravatarId,
      isActive: isActive || false,
      adminCreatedBy: CreatedBy,
      role: role || "user", // Default to "user" if no role is specified
      companyId,
      Postcode,
    BuildingAndStreetOne,
    BuildingAndStreetTwo,
    Town_City,
    Country,
    });


    // return;

    const savedUser = await newUser.save();
    if (!savedUser) {
      return NextResponse.json({ message: "User not added", status: 400 });
    } else {
      return NextResponse.json({
        message: "User created successfully",
        success: true,
        status: 200,
      });
    }
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: error.message, status: 500 });
  }
}

export const GET = catchAsyncErrors(async () => {
  await connect();
  const allUsers = await User.find().sort({ createdAt: -1 }).populate("companyId");
  const userCount = await User.countDocuments();
  if (!allUsers || allUsers.length === 0) {
    return NextResponse.json({ result: allUsers });
  } else {
    return NextResponse.json({ result: allUsers, count: userCount });
  }
});
