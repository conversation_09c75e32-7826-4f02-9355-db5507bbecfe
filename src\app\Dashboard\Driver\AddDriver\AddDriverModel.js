"use client";
import React, { useEffect, useState, useRef } from "react";
import axios from "axios";
import { toast } from "react-toastify";
import { AiOutlineEye, AiOutlineEyeInvisible } from "react-icons/ai";
import {
  fetchInsurence,
  fetchLocalAuth,
} from "../../Components/DropdownData/taxiFirm/taxiFirmService";
import {
  API_URL_Driver,
} from "@/app/Dashboard/Components/ApiUrl/ApiUrls";

import {
  getCompanyName,
  getUserId,
  getUserName, getflag, getcompanyId
} from "@/utils/storageUtils";
import Image from "next/image";
const AddDriverModal = ({ isOpen, onClose, fetchData }) => {
  const [showPasswords, setShowPasswords] = useState(false);
  const [imagePreview, setImagePreview] = useState(null);
  const fileInputRef = useRef(null);
  const initialFormData = {
    title: "",
    First_Name: "",
    lastName: "",
    firstName: "",
    email: "",
    tel1: "",
    tel2: "",
    postcode: "",
    postalAddress: "",
    city: "",
    county: "",
    dateOfBirth: "",
    licenseNumber: "",
    niNumber: "",
    driverNumber: "",
    taxiFirm: "",
    insurance: "",
    startDate: "",
    driverRent: 0,
    licenseExpiryDate: "",
    taxiBadgeDate: "",
    rentPaymentCycle: "",
    isActive: false,
    imageFile: null,

    LocalAuth: "",
    vehicle: "",
    calculation: 0,
    adminCreatedBy: "",
    adminCompanyName: "",
    companyId: null,
    password: "",
    confirmPassword: "",
    BuildingAndStreetOne: "",
    BuildingAndStreetTwo: "",

  };

  const [formData, setFormData] = useState(initialFormData);
  const [loading, setLoading] = useState(false);
  const [insurance, setInsurance] = useState([]);
  const [localAuth, setlocalAuth] = useState([]);
  const [superadmin, setSuperadmin] = useState(null);
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const passwordRegex = /^\d{4,}$/;

  const [validation, setValidation] = useState({
    emailValid: false,
    passwordValid: false,
  });


  const [step, setStep] = useState(1);
  const nextStep = () => setStep(step + 1);
  const prevStep = () => setStep(step - 1);

  useEffect(() => {
    let storedCompanyName = "";
    let storedSuperadmin = "";

    if (typeof window !== "undefined") {
      storedCompanyName = localStorage.getItem("companyName") || "";
      storedSuperadmin = localStorage.getItem("role") || "";
    }

    if (storedSuperadmin) {
      setSuperadmin(storedSuperadmin);
    }

    if (storedCompanyName) {
      setFormData((prevData) => ({
        ...prevData,
        adminCompanyName: storedCompanyName,
        confirmPassword: prevData?.password
      }));
    }
  }, []);

  useEffect(() => {
    const storedcompanyName = (() => {
      const name1 = getCompanyName();
      if (name1) return name1;

      const name2 = getUserName();
      if (name2) return name2;
    })();
    const userId = getUserId()
    const flag = getflag();
    const compID = getcompanyId();

    if (storedcompanyName && userId) {

      if (storedcompanyName.toLowerCase() === "superadmin" && flag === "true" && compID) {
        setFormData((prevData) => ({
          ...prevData,
          companyName: storedcompanyName,
          companyId: compID,
        }));
      } else {
        setFormData((prevData) => ({
          ...prevData,
          companyName: storedcompanyName,
          companyId: userId,
        }));
      }
    } else {
      console.error("Missing required fields:", { storedcompanyName, userId, flag, compID });
    }
  }, [])


  useEffect(() => {
    const loadDropdownData = async () => {
      try {
        const [
          insuranceData,
          local,
        ] = await Promise.all([
          fetchInsurence(),
          fetchLocalAuth(),
        ]);

        const storedCompanyName = formData?.adminCompanyName;
        const filteredInsurance =
          superadmin === "superadmin"
            ? insuranceData.Result
            : insuranceData.Result.filter(
              (insurance) =>
                insurance.adminCompanyName === storedCompanyName ||
                insurance.adminCompanyName === "superadmin"
            );
        const filteredlocalAuth =
          superadmin === "superadmin"
            ? local.Result
            : local.Result.filter(
              (l) =>
                l.adminCompanyName === storedCompanyName ||
                l.adminCompanyName === "superadmin"
            );

        setInsurance(filteredInsurance);
        setlocalAuth(filteredlocalAuth);

      } catch (err) {
        console.error("Error loading dropdown data:", err);
      }
    };

    loadDropdownData();
  }, [superadmin, formData.adminCompanyName]);

  const [errors, setErrors] = useState({
    dateOfBirth: "",
  });

  const handleChange = (e) => {
    const { name, value, type, checked, files } = e.target;

    let updatedValue;
    if (type === "checkbox") {
      updatedValue = checked;
    } else if (type === "file") {
      updatedValue = files[0];
    } else if (name === "firstName") {
      updatedValue = value.replace(/\s+/g, '');
    } else {
      updatedValue = value;
    }

    if (name === "dateOfBirth") {
      const selectedDate = new Date(value);
      const today = new Date();
      let age = today.getFullYear() - selectedDate.getFullYear();

      const monthDiff = today.getMonth() - selectedDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < selectedDate.getDate())) {
        age--;
      }

      if (age < 18) {
        setErrors((prev) => ({ ...prev, dateOfBirth: "You must be at least 18 years old." }));
        updatedValue = "";
      } else {
        setErrors((prev) => ({ ...prev, dateOfBirth: "" }));
      }
    }

    if (name === "licenseExpiryDate") {
      const selectedDate = new Date(value);
      const today = new Date();
      selectedDate.setHours(0, 0, 0, 0);
      today.setHours(0, 0, 0, 0);

      if (selectedDate < today) {
        setErrors((prev) => ({ ...prev, licenseExpiryDate: "License expiry date cannot be in the past." }));
        updatedValue = "";
      } else {
        setErrors((prev) => ({ ...prev, licenseExpiryDate: "" }));
      }
    }

    if (name === "email") {
      setValidation((prev) => ({
        ...prev,
        emailValid: emailRegex.test(updatedValue),
      }));
    }


    if (name === "password") {
      const isValid = passwordRegex.test(updatedValue);

      setValidation((prev) => ({
        ...prev,
        passwordValid: isValid,
      }));

      setErrors((prev) => ({
        ...prev,
        password: isValid ? "" : "Password must be at least 4 digits and contain only numbers.",
      }));
    }




    if (type === "file" && files[0]) {
      const file = files[0];
      const reader = new FileReader();

      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);

      updatedValue = file;
    }

    setFormData((prev) => ({
      ...prev,
      [name]: updatedValue,
    }));
  };

  const pageonerequiredfeilds = [
    "firstName",
    "email",
    "tel1",
  ];

  const areFieldsFilled = (fields) =>
    fields.every((field) => formData[field]?.trim() !== "");
  const isNextDisabled1st =
    !validation.emailValid || !areFieldsFilled(pageonerequiredfeilds);


  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    const formDataToSend = new FormData();
    Object.keys(formData).forEach((key) => {
      formDataToSend.append(key, formData[key]);
    });

    try {
      const response = await axios.post(`${API_URL_Driver}`, formDataToSend, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });


      if (response?.data?.success) {
        toast.success(response?.data?.message);
        fetchData();
        onClose();

        const initialFormData = {
          title: "",
          First_Name: "",
          firstName: "",
          lastName: "",
          email: "",
          tel1: "",
          tel2: "",
          postcode: "",
          postalAddress: "",
          city: "",
          county: "",
          dateOfBirth: "",
          licenseNumber: "",
          niNumber: "",
          driverNumber: "",
          taxiFirm: "",
          badgeType: "",
          insurance: "",
          startDate: "",
          driverRent: 0,
          licenseExpiryDate: "",
          taxiBadgeDate: "",
          rentPaymentCycle: "",
          isActive: false,
          imageFile: null,
          LocalAuth: "",
          vehicle: "",
          calculation: "",
          adminCreatedBy: "",
          password: "",
          confirmPassword: "",
          Postcode: "",
          BuildingAndStreetOne: "",
          BuildingAndStreetTwo: "",
          adminCompanyName: formData.adminCompanyName,
        };
        setFormData(initialFormData);
        setStep(1);
        setImagePreview(null);

        if (fileInputRef.current) {
          fileInputRef.current.value = null;
        }

      } else {
        toast.warn(response?.data?.error);
      }
    } catch (err) {
      console.error("Submission Error:", err);
      toast.error("An error occurred during submission.");
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveImage = () => {
    setImagePreview(null);
    setFormData((prevData) => ({
      ...prevData,
      image: null,
    }));
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };


  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50 z-50">
      <div className="bg-white p-12 rounded-xl shadow-lg w-full max-w-4xl overflow-y-auto max-h-screen">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold">
              Add Driver
            </h2>

            <Image width={15} height={15} alt="cross" src="/crossIcon.svg" className="cursor-pointer" onClick={() => {
              onClose();
              setFormData(initialFormData);
              setStep(1);
            }} />
          </div>
          {step === 1 && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                <div>
                  <div className="flex gap-1 items-center justify-start">
                    <label
                      htmlFor="taxiFirm"
                      className="text-[10px]"
                    >
                      Title <span className="text-red-600">*</span>
                    </label>
                  </div>

                  <select
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleChange}
                    className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow"
                    required
                  >
                    <option value="">Select Title</option>
                    <option value="Mr">Mr</option>
                    <option value="Miss">Miss</option>
                    <option value="Mrs">Mrs</option>
                  </select>
                </div>

                <div>
                  <div className="flex gap-1 items-center justify-start">
                    <label
                      htmlFor="First_Name"
                      className="text-[10px]"
                    >
                      First Name <span className="text-red-600">*</span>
                    </label>
                  </div>

                  <input
                    type="text"
                    id="First_Name"
                    name="First_Name"
                    value={formData?.First_Name}
                    onChange={handleChange}
                    className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow"
                    required
                    placeholder="First name"
                  />
                </div>

                <div>
                  <div className="flex gap-1 items-center justify-start">
                    <label
                      htmlFor="lastName"
                      className="text-[10px]"
                    >
                      Last Name <span className="text-red-600">*</span>
                    </label>
                  </div>

                  <input
                    type="text"
                    id="lastName"
                    name="lastName"
                    value={formData?.lastName}
                    onChange={handleChange}
                    className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow"
                    required
                    placeholder="Last name"
                  />
                </div>
                <div>

                  <div className="flex gap-1 items-center justify-start">
                    <label
                      htmlFor="firstName"
                      className="text-[10px]"

                    >
                      User Name <span className="text-red-600">*</span>
                    </label>
                  </div>

                  <input
                    type="text"
                    id="firstName"
                    name="firstName"
                    placeholder="Name"
                    value={formData?.firstName}
                    onChange={handleChange}
                    className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow"
                    required
                  />
                </div>
                <div>
                  <div className="flex gap-1 items-center justify-start">
                    <label htmlFor="dateOfBirth" className="text-[10px]">
                      Date of Birth
                    </label>
                  </div>

                  <input
                    type="date"
                    id="dateOfBirth"
                    name="dateOfBirth"
                    value={formData?.dateOfBirth}
                    onChange={handleChange}
                    className={`mt-1 block w-full p-2 border ${errors.dateOfBirth ? "border-red-500" : "border-[#42506666]"} rounded shadow`}
                    required
                    max={new Date(new Date().setFullYear(new Date().getFullYear() - 18))
                      .toISOString()
                      .split("T")[0]}
                  />

                  {errors.dateOfBirth && (
                    <p className="text-red-500 text-[10px] mt-1">{errors?.dateOfBirth}</p>
                  )}
                </div>

                <div>
                  <div className="flex gap-1">
                    <label
                      htmlFor="niNumber"
                      className="text-[10px]"
                    >
                      NI Number
                    </label>
                  </div>
                  <input
                    type="text"
                    id="niNumber"
                    name="niNumber"
                    value={formData?.niNumber}
                    onChange={handleChange}
                    className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow"
                    placeholder="Ni Number"
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label
                      htmlFor="licenseNumber"
                      className="text-[10px]"
                    >
                      License Number
                    </label>

                  </div>
                  <input
                    type="text"
                    id="licenseNumber"
                    name="licenseNumber"
                    value={formData?.licenseNumber}
                    onChange={handleChange}
                    className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow"
                    placeholder="License Number"
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <div className="flex gap-1">
                      <label
                        htmlFor="email"
                        className="text-[10px]"
                      >
                        Email <span className="text-red-600">*</span>
                      </label>
                    </div>
                  </div>

                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData?.email}
                    onChange={handleChange}
                    className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow"
                    required
                    placeholder="Email"
                  />
                  {validation?.emailValid === false && formData?.email && (
                    <p className="text-red-600 text-[10px] mt-1">Please enter a valid email address.</p>
                  )}

                  {validation?.emailValid === true && (
                    <p className="text-green-600 text-[10px] mt-1">Email looks good!</p>
                  )}

                </div>

                <div>
                  <div className="relative">
                    <div className="flex gap-1 items-center justify-start">
                      <label
                        htmlFor="password"
                        className="text-[10px]"
                      >
                        Password <span className="text-red-600">*</span>
                      </label>
                    </div>

                    <input
                      type={showPasswords ? "text" : "password"}
                      id="password"
                      name="password"
                      value={formData?.password}
                      onChange={handleChange}
                      className="mt-1 block w-full p-2 border border-[#42506666] rounded"
                      required
                      placeholder="Password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPasswords((prev) => !prev)}
                      className="absolute right-2 top-7"
                    >
                      {showPasswords ? (
                        <AiOutlineEye size={20} />
                      ) : (
                        <AiOutlineEyeInvisible size={20} />
                      )}
                    </button>
                    
                    {formData?.password && (
                      <p className={`text-[9px] ${validation.passwordValid ? "text-green-700" : "text-red-700"}`}>
                        {errors.password || "Strong password"}
                      </p>
                    )}
                  </div>
                </div>
                <div>
                  <div className="flex gap-1">
                    <div className="flex gap-1">
                      <label
                        htmlFor="tel1"
                        className="text-[10px]"
                      >
                        Phone number  <span className="text-red-600">*</span>
                      </label>
                    </div>
                  </div>

                  <input
                    type="tel"
                    id="tel1"
                    name="tel1"
                    placeholder="Phone Number"
                    value={formData?.tel1}
                    onChange={handleChange}
                    className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow"
                    required
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label
                      htmlFor="driverNumber"
                      className="text-[10px]"
                    >
                      Driver Number
                    </label>
                  </div>
                  <input
                    type="text"
                    id="driverNumber"
                    name="driverNumber"
                    placeholder="Driver Number"
                    value={formData?.driverNumber}
                    onChange={handleChange}
                    className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow"
                  />
                </div>

                <div>
                  <label
                    htmlFor="LocalAuth"
                    className="text-[10px]"
                  >
                    Local Authority
                  </label>
                  <select
                    id="LocalAuth"
                    name="LocalAuth"
                    value={formData?.LocalAuth}
                    onChange={handleChange}
                    className="mt-1 block w-full p-2 border border-gray-300 rounded-lg"
                    required
                  >
                    <option value="null">Select Local Authority</option>
                    {localAuth
                      .filter((a) => a.isActive)
                      .map((auth) => (
                        <option key={auth?._id} value={auth?.name}>
                          {auth?.name}
                        </option>
                      ))}
                  </select>
                </div>

                <div>
                  <label
                    htmlFor="insurance"
                    className="text-[10px]"
                  >
                    Insurance
                  </label>
                  <select
                    id="insurance"
                    name="insurance"
                    value={formData?.insurance}
                    onChange={handleChange}
                    className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow"
                  >
                    <option value="null">Select insurance </option>


                    {insurance
                      .filter((insurence) => insurence?.isActive)
                      .map((insurence) => (
                        <option key={insurence?._id} value={insurence?.name}>
                          {insurence?.name}
                        </option>
                      ))}

                  </select>
                </div>

                <div>
                  <div className="flex gap-1">
                    <label
                      htmlFor="licenseExpiryDate"
                      className="text-[10px]"
                    >
                      License Expiry Date
                    </label>
                  </div>

                  <input
                    type="date"
                    id="licenseExpiryDate"
                    name="licenseExpiryDate"
                    value={formData?.licenseExpiryDate}
                    onChange={handleChange}
                    className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow"
                    required
                    min={new Date().toISOString().split("T")[0]}
                  />
                </div>
              </div>

              <div className="mt-6 flex gap-[10px] justify-end">
                <button
                  type="button"
                  onClick={() => {
                    onClose();
                    setFormData(initialFormData);
                  }}
                  className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                >
                  Cancel
                </button>
                <button
                  onClick={nextStep}
                  className={`bg-[#313342] text-white rounded-4 hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 py-1 px-8 ${isNextDisabled1st
                    ? "bg-gray-400 text-white cursor-not-allowed"
                    : "bg-custom-bg text-white hover:bg-gray-600"
                    }`}
                  disabled={isNextDisabled1st}
                >
                  Next
                </button>
              </div>
            </>
          )}
          {step === 2 && (
            <>
              <h2 className="font-bold">Address</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">
                      Apartment <span className="text-red-600">*</span>
                    </label>
                  </div>
                  <input
                    type="text"
                    id="Building&Street"
                    name="BuildingAndStreetOne"
                    value={formData?.BuildingAndStreetOne}
                    onChange={handleChange}
                    className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow"
                    required
                    placeholder="Apartment"
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label className="text-[10px]">
                      Building and Street <span className="text-red-600">*</span>
                    </label>

                  </div>
                  <input
                    type="text"
                    id="Building&Street2"
                    name="BuildingAndStreetTwo"
                    value={formData?.BuildingAndStreetTwo}
                    onChange={handleChange}
                    className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow"
                    placeholder="Building and Street"
                    required
                  />
                </div>

                <div>
                  <div className="flex gap-1">
                    <label htmlFor="city" className="text-[10px]">
                      Town/City  <span className="text-red-600">*</span>
                    </label>
                  </div>

                  {formData.countyOption === 'other' ? (
                    <input
                      type="text"
                      id="city"
                      name="city"
                      placeholder="Enter Town/city"
                      value={formData?.city}
                      onChange={handleChange}
                      className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  ) : (
                    <select
                      id="city"
                      name="city"
                      value={formData?.city}
                      onChange={handleChange}
                      className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      required
                    >
                      <option value="">Select City</option>
                      <option value="London">London</option>
                      <option value="Birmingham">Birmingham</option>
                      <option value="Manchester">Manchester</option>
                      <option value="Glasgow">Glasgow</option>
                      <option value="Liverpool">Liverpool</option>
                      <option value="Leeds">Leeds</option>
                      <option value="Sheffield">Sheffield</option>
                      <option value="Edinburgh">Edinburgh</option>
                      <option value="Bristol">Bristol</option>
                      <option value="Cardiff">Cardiff</option>
                      <option value="Nottingham">Nottingham</option>
                      <option value="Leicester">Leicester</option>
                      <option value="Coventry">Coventry</option>
                      <option value="Kingston upon Hull">Kingston upon Hull</option>
                      <option value="Newcastle upon Tyne">Newcastle upon Tyne</option>
                      <option value="Brighton">Brighton</option>
                      <option value="Southampton">Southampton</option>
                      <option value="Portsmouth">Portsmouth</option>
                      <option value="Derby">Derby</option>
                      <option value="Stoke-on-Trent">Stoke-on-Trent</option>
                      <option value="Wolverhampton">Wolverhampton</option>
                      <option value="Plymouth">Plymouth</option>
                      <option value="Aberdeen">Aberdeen</option>
                      <option value="Norwich">Norwich</option>
                      <option value="Luton">Luton</option>
                    </select>
                  )}
                </div>

                <div>
                  <div className="flex gap-1">
                    <label htmlFor="county" className="text-[10px]">
                      Country   <span className="text-red-600">*</span>
                    </label>
                  </div>

                  <select
                    id="county"
                    name="countyOption"
                    value={formData?.countyOption || 'UK'}
                    onChange={(e) => {
                      const selected = e.target.value;
                      setFormData((prev) => ({
                        ...prev,
                        countyOption: selected,
                        county: selected === 'UK' ? 'United Kingdom' : '',
                        city: '',
                      }));
                    }}
                    className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    required
                  >
                    <option value="UK">UK</option>
                    <option value="other">Other</option>
                  </select>

                  {formData.countyOption === 'other' && (
                    <input
                      type="text"
                      name="county"
                      placeholder="Enter country"
                      value={formData?.county}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          county: e.target.value,
                        }))
                      }
                      className="mt-2 block w-full p-2 border border-[#42506666] rounded shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  )}
                </div>

                <div>
                  <div className="flex gap-1">
                    <label
                      htmlFor="postcode"
                      className="text-[10px]"
                    >
                      Postcode
                    </label>

                  </div>

                  <input
                    type="text"
                    id="postcode"
                    name="postcode"
                    value={formData?.postcode}
                    onChange={handleChange}
                    className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow"
                  />
                </div>

                <div>
                  <label className="text-[10px]">Status</label>
                  <div className="flex gap-4 mt-2">
                    <label className="flex items-center gap-2">
                      <input
                        type="radio"
                        name="isActive"
                        value="true"
                        checked={formData.isActive === true}
                        onChange={() =>
                          handleChange({
                            target: { name: "isActive", value: true },
                          })
                        }
                        className="accent-green-500"
                      />
                      <span className="text-xs">Active</span>
                    </label>

                    <label className="flex items-center gap-2">
                      <input
                        type="radio"
                        name="isActive"
                        value="false"
                        checked={formData.isActive === false}
                        onChange={() =>
                          handleChange({
                            target: { name: "isActive", value: false },
                          })
                        }
                        className="accent-red-500"
                      />
                      <span className="text-xs">Inactive</span>
                    </label>
                  </div>
                </div>

                <div className="">
                  <div className="flex items-center gap-2">
                    <label htmlFor="image" className="text-[10px]">
                      Upload Image
                    </label>
                    <input
                      type="file"
                      id="image"
                      name="imageFile"
                      accept="image/*"
                      ref={fileInputRef}
                      onChange={handleChange}
                      className="mt-1 block w-48 text-[8px] text-gray-400 file:mr-4 file:py-1 p-2 file:px-4 file:rounded-lg file:border file:text-[10px] file:font-semibold file:bg-white hover:file:bg-blue-100 border border-[#0885864D] rounded-[10px] border-dashed"
                    />
                  </div>

                  <div>
                    {imagePreview && (
                      <div className="relative w-fit mt-2">
                        <Image width={100} height={100}
                          src={imagePreview}
                          alt="Avatar Preview"
                          className="avatar-preview rounded border"
                        />
                        <button
                          type="button"
                          onClick={handleRemoveImage}
                          className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center shadow-md hover:bg-red-600"
                        >
                          ✖
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>


              <div className="mt-10 flex gap-2 justify-between">
                <div>
                  <button
                    onClick={prevStep}
                    className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                  >
                    Back
                  </button>
                </div>
                <div className="flex gap-[10px]">
                  <button
                    type="button"
                    onClick={() => {
                      onClose();
                      setFormData(initialFormData);
                    }}
                    className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className={`bg-[#313342] text-white rounded-4 hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 py-1 px-8 "             
                      }`}
                  >
                    {loading ? "Submitting..." : "Submit"}
                  </button>
                </div>
              </div>
            </>
          )}
        </form>
      </div>
    </div>
  );
};

export default AddDriverModal;
