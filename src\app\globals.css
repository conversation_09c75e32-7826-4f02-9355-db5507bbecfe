@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap');
/* Import both DM Sans and Montserrat from Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&family=Montserrat:wght@400;500;700&display=swap');

/* import poppins */
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');


@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  background-color: #ffffff;
  color: #0a0a0a;
  font-family: "DM Sans", serif;
}

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}

img {
  background: transparent;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* #27273A */



/* scroll bar for tables */

.custom-scrollbar {
  overflow-x: auto;
  /* Enables horizontal scrolling */
  scrollbar-width: thin;
  /* Makes it look custom */
  scrollbar-color: #38384A transparent;
}

/* WebKit-based browsers */
.custom-scrollbar::-webkit-scrollbar {
  width: 20px;
  /* Ensures 20px width */
  height: 20px;
  /* Ensures 20px height for horizontal scrolling */
  position: absolute;
  /* Prevents affecting layout */
}

/* Scrollbar track */
.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  /* Keeps it invisible */
  border-radius: 10px;
}

/* Scrollbar thumb (the draggable part) */
.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #38384A;
  border-radius: 10px;
  /* Ensures rounded corners */
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
}

/* Scrollbar thumb on hover */
.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #48485a;
}

/* Scrollbar thumb when active (dragged) */
.custom-scrollbar:active::-webkit-scrollbar-thumb {
  background-color: #58586a;
}


/* for vertical */
.custom-scrollbary {
  overflow-y: auto; /* Enables horizontal scrolling */
  scrollbar-width: thin; /* Makes it look custom */
  scrollbar-color: #38384A transparent;
}

/* WebKit-based browsers */
.custom-scrollbary::-webkit-scrollbar {
  width: 20px; /* Ensures 20px width */
  height: 20px; /* Ensures 20px height for horizontal scrolling */
  position: absolute; /* Prevents affecting layout */
}

/* Scrollbar track */
.custom-scrollbary::-webkit-scrollbar-track {
  background: transparent; /* Keeps it invisible */
  border-radius: 10px;
}

/* Scrollbar thumb (the draggable part) */
.custom-scrollbary::-webkit-scrollbar-thumb {
  background-color: #38384A;
  border-radius: 10px; /* Ensures rounded corners */
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
}

/* Scrollbar thumb on hover */
.custom-scrollbary::-webkit-scrollbar-thumb:hover {
  background-color: #48485a;
}

/* Scrollbar thumb when active (dragged) */
.custom-scrollbary:active::-webkit-scrollbar-thumb {
  background-color: #58586a;
}

.myborder{
  border-bottom: 2px solid #AEADEB;
}

.myborder2{
  border-bottom: none;
}

@media screen and (max-width:500px) {
  .myborder2{
    border-bottom: 2px solid #AEADEB;
}  
.myborder{
  border-bottom: none;
  flex-direction: column;
}
}