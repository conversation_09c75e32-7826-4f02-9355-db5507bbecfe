"use client";

import React, { useState, useEffect, useCallback } from "react";
import Header from "../../../Components/Header";
import Sidebar from "../../../Components/Sidebar";
import AddMaintenanceModel from "../AddMaintenanceModal/AddmaintenanceModel";
import { API_URL_Maintainance } from "../../../Components/ApiUrl/ApiUrls";
import { getCompanyName, getsuperadmincompanyname, getUserName } from "@/utils/storageUtils";
import axios from "axios";
import jsPDF from "jspdf";
import BackButton from "@/app/Dashboard/Components/BackButton";
import Image from "next/image";
import {Count} from "@/utils/count";
import SearchAndAddBar from "@/utils/searchAndAddBar";
import Pagination from "@/utils/pagination";

const Page = ({ params }) => {
  const addmaintenancereportId = params?.AddMaintenanceReport;
  const [data, setData] = useState([]);
  const [baseFilteredData, setBaseFilteredData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [isOpenTitle, setIsOpenTitle] = useState(false);
  const [selectedCompanyName, setSelectedCompanyName] = useState("");
  const [itemperpage, setitemperpage] = useState(Count);
  const [paginatedData, setPaginatedData] = useState([]);
  const [selectedid, setselectedid] = useState(null);

  
  const handleFilterChange = useCallback((filtered) => {
    setFilteredData(filtered);
  }, []);

  const handleItemsPerPageChange = useCallback((newItemsPerPage) => {
    setitemperpage(newItemsPerPage);
  }, []);

  useEffect(() => {
    const companyNameFromStorage = getCompanyName();
    if (companyNameFromStorage) {
      setSelectedCompanyName(companyNameFromStorage);
    }
  }, []);

  const fetchData = async () => {
    try {
      const response = await axios.get(`${API_URL_Maintainance}`);
      setData(response?.data?.result);
    } catch (err) {
      console.error("Error fetching data:", err);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    const companyName = getCompanyName();
    const superAdminName = getsuperadmincompanyname();
    const userName = getUserName();
    const storedcompanyName = companyName || superAdminName || userName;

    if (data.length > 0) {
      let companyFilteredData;

      if (userName?.toLowerCase() === "superadmin") {
        companyFilteredData = data;
      } else {
        companyFilteredData = data?.filter(
          (item) =>
            item?.adminCompanyName?.toLowerCase() === "superadmin" ||
            item?.adminCompanyName?.toLowerCase() === storedcompanyName?.toLowerCase()
        );
      }

      setBaseFilteredData(companyFilteredData);
    } else {
      setBaseFilteredData([]);
    }
  }, [data]);

  useEffect(() => {
    setFilteredData(baseFilteredData);
  }, [baseFilteredData]);

  const toggleTitleModal = () => {
    setIsOpenTitle((prev) => !prev);
    setselectedid(addmaintenancereportId);
  };

  const generatePDF = () => {
    const doc = new jsPDF();

    doc.setFontSize(12);
    doc.text("Maintenance Records Report", 14, 10);

    const reportDate = new Date().toLocaleDateString();
    doc.setFontSize(10);
    doc.text(`Report Generated: ${reportDate}`, 14, 15);
    const VehicleName = filteredData[0]?.vehicleName || "Name Unavailable";
    doc.text(`Vehicle Name: ${VehicleName}`, 14, 20);
    const vehicleRegistration =
      filteredData[0]?.registrationNumber || "Registration Unavailable";
    doc.text(`Registration Number: ${vehicleRegistration}`, 14, 25);
    doc.text(`Company Name: ${selectedCompanyName}`, 14, 30);

    const tableColumn = [
      "Issues",
      "Organisation",
      "Repair Status",
      "Job Number",
      "Memo",
      "Parts",
      "Labour Hours",
      "Cost",
      "Signed Off By",
      "Date",
    ];

    const pageWidth = doc?.internal?.pageSize?.width;
    const startX = 14;
    const startY = 42;
    const padding = 6;
    const lineHeight = 9;
    const columnWidth = 35;
    const pageHeight = doc?.internal?.pageSize?.height;

    const maxColumnsPerRow = Math.floor((pageWidth - startX) / columnWidth);

    let currentY = startY;

    const renderHeaders = (startIndex) => {
      let currentX = startX;
      for (
        let i = startIndex;
        i < tableColumn.length && i < startIndex + maxColumnsPerRow;
        i++
      ) {
        doc.text(tableColumn[i], currentX + padding, currentY);
        doc.rect(currentX, currentY - 4, columnWidth, lineHeight);
        currentX += columnWidth;
      }
      currentY += lineHeight; 
    };

    filteredData.forEach((row) => {
      row.repairHistory.forEach((repair) => {
        const rowData = [
          row?.issues || "N/A",
          row?.organisation || "N/A",
          row?.repairStatus || "N/A",
          row?.jobNumber || "N/A",
          row?.memo || "N/A",

          `${repair?.partNumber || "N/A"}: ${repair?.partName || "N/A"} - $${repair?.price || 0
          } (${repair?.supplier || "N/A"})`,
          row?.labourHours || "N/A",
          `$${row?.cost || 0}`,
          row?.signedOffBy || "N/A",
          (() => {
            const date = new Date(row?.date);
            const formattedDate = `${String(date.getMonth() + 1).padStart(
              2,
              "0"
            )}/${String(date.getDate()).padStart(
              2,
              "0"
            )}/${date.getFullYear()}`;
            return formattedDate;
          })() || "N/A",
        ];

        let currentX = startX;
        for (let i = 0; i < rowData.length; i++) {
          if (i % maxColumnsPerRow === 0) {
            if (currentY + lineHeight > pageHeight - 20) {
              doc.addPage();
              currentY = 20;
            }
            renderHeaders(i); 
          }

          if (currentY + lineHeight > pageHeight - 20) {
            doc.addPage();
            currentY = 20;
            renderHeaders(i);
          }

          const cellText = doc.splitTextToSize(
            rowData[i],
            columnWidth - padding
          );
          doc.text(cellText, currentX + padding, currentY);
          doc.rect(
            currentX,
            currentY - 4,
            columnWidth,
            lineHeight * cellText.length
          );

          currentX += columnWidth;

          if ((i + 1) % maxColumnsPerRow === 0) {
            currentY += lineHeight * cellText.length;
            currentX = startX;
          }
        }
        currentY += lineHeight;
      });
    });

    doc.save("Maintenance_Report.pdf");
  };

  return (
    <div className="h-[100vh] overflow-hidden">
      <Header className="min-w-full" />
      <div className="flex gap-4 w-full">
        <Sidebar />
        <div
          className="w-[80%] xl:w-[85%] h-screen flex flex-col justify-start overflow-y-auto pr-4"
          style={{
            height: "calc(100vh - 90px)",
          }}
        >
          <h1 className="text-[#313342] font-medium text-2xl py-5 pb-8  flex gap-2 items-center">
            <div className="myborder flex gap-3 border-2 border-t-0 border-l-0 border-r-0">
              <span className="opacity-65">Vehicle</span>
              <div className="flex items-center gap-3 myborder2">
                <span>
                  <Image width={2} height={4} 
                    src="/setting_arrow.svg"
                    className="w-2 h-4 object-cover object-center  "
                   alt='arrow' />
                </span>
                <span>Maintenance Report</span>
              </div>
            </div>
          </h1>
          <div className="py-5">
            <div className="drop-shadow-custom4">
              <div className="flex w-full py-2 px-2">
                 <SearchAndAddBar
                data={baseFilteredData}
                itemperpage={itemperpage}
                onFilterChange={handleFilterChange}
                onItemsPerPageChange={handleItemsPerPageChange}
                showSearch={false}
                showAddButton={false}
              />
                <div className="flex justify-center gap-2 items-center">
                  <BackButton />
                  <button
                    onClick={generatePDF}
                    className={`w-[170px] font-sans text-sm font-semibold  px-5 h-10  border-[1px] rounded-lg border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500`}
                  >
                    Download Report
                  </button>
                  <button
                    onClick={toggleTitleModal}
                    className="w-[152px] font-sans  font-bold text-xs bg-[#313342] text-white rounded-lg hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 px-3 flex py-[12px] gap-2 items-center justify-center"
                  >
                    <Image width={15} height={15}  src="/plus.svg" alt="Add Service" />
                    Add Maintenance
                  </button>
                </div>
              </div>

              <div className="overflow-x-auto custom-scrollbar">
                <table className="w-full bg-white border table-auto">
                  <thead className="font-sans font-bold text-sm text-center bg-[#38384A] text-white">
                    <tr>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        Assigned To
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        Vehicle Name
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        Registration No
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        Issues/Damages
                      </th>
                      <th className="py-3 px-4 min-w-[165px] w-[165px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        Organisation
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        Job Number
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        Repair Status
                      </th>

                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        View
                      </th>
                    </tr>
                  </thead>
                  <tbody className="font-sans font-medium text-sm">
                    {paginatedData?.map((row) => (
                      <tr key={row?._id} className="border-b hover:bg-gray-100 text-center">
                        <td className="py-2 px-4 whitespace-normal break-all overflow-hidden">
                          {row?.assign}
                        </td>
                        <td className="py-2 px-4 whitespace-normal break-all overflow-hidden">
                          {row?.vehicleName}
                        </td>
                        <td className="py-2 px-4 whitespace-normal break-all overflow-hidden">
                          {row?.registrationNumber}
                        </td>
                        <td className="py-2 px-4 whitespace-normal break-all overflow-hidden">
                          {row?.issues}
                        </td>
                        <td className="py-2 px-4 whitespace-normal break-all overflow-hidden">
                          {row?.organisation}
                        </td>
                        <td className="py-2 px-4 whitespace-normal break-all overflow-hidden">
                          {row?.jobNumber}
                        </td>
                        <td className="py-2 px-4 whitespace-normal break-all overflow-hidden">
                          {row?.repairStatus}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <Pagination
                data={filteredData}
                itemperpage={itemperpage}
                onPageChange={(currentItems) => {
                  setPaginatedData(currentItems);
                }}
              />
            </div>
          </div>
        </div>
      </div>
      <AddMaintenanceModel
        isOpen={isOpenTitle}
        onClose={toggleTitleModal}
        fetchData={fetchData}
        selectedid={selectedid}
      />
    </div>
  );
};

export default Page;
