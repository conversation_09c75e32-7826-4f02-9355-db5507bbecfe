"use client";

import React, { useState, useEffect, useCallback } from "react";
import Header from "../../Components/Header";
import Sidebar from "../../Components/Sidebar";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import AddUserModel from "../AddUser/AddUserModel";
import UpdateUserModel from "../UpdateUser/UpdateUserModel";
import axios from "axios";
import { API_URL_USER } from "../../Components/ApiUrl/ApiUrls";
import { getAuthData } from "@/utils/verifytoken";
import AdminDashBDoughnut from "../../Components/AdminDashBDoughnut";
import SearchAndAddBar from "@/utils/searchAndAddBar";
import Pagination from "@/utils/pagination";
import {
  getCompanyName,
  getsuperadmincompanyname,
  getUserRole,
  getUserName
} from "@/utils/storageUtils";
import {Count} from '@/utils/count'
import DeleteModal from "../../Components/DeleteModal";
import Image from "next/image";

const Page = () => {
  const [users, setUsers] = useState([]);
  const [usersX, setUsersX] = useState([]);
  const [baseFilteredData, setBaseFilteredData] = useState([]); 
  const [filteredData, setFilteredData] = useState([]);
  const [paginatedData, setPaginatedData] = useState([]);
  const [isMounted, setIsMounted] = useState(false);
  const [isOpenUser, setIsOpenUser] = useState(false);
  const [isOpenUserUpdate, setIsOpenUserUpdate] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleteModalOpenId, setIsDeleteModalOpenId] = useState(null);
  const [itemperpage, setitemperpage] = useState(Count);
  const [activeUsers, setActiveUsers] = useState(0);
  const [inactiveUsers, setInActiveUsers] = useState(0);
  const [trigger, setTrigger] = useState(true);
  const [role, setRole] = useState("");

  useEffect(() => {
    const active = usersX?.filter((user) => user.isActive === true).length;
    const inactive = usersX?.length - active;
    setActiveUsers(active);
    setInActiveUsers(inactive);
  }, [usersX]);

  const companiesData = {
    labels: ["Inactive", "Active"],
    datasets: [
      {
        label: "User Status",
        data: [inactiveUsers, activeUsers],
        backgroundColor: ["#404CA0", "#27273AEB"],
        borderWidth: inactiveUsers === 0 ? 0 : 1,
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        display: false, 
      },
    },
  };

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const response = await axios.get(`${API_URL_USER}`);
      setUsers(response?.data?.result);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  const handleEdit = (id) => {
    setSelectedUserId(id);
    setIsOpenUserUpdate(true);
  };

  const isopendeletemodel = (id) => {
    setIsDeleteModalOpenId(id); 
    setIsDeleteModalOpen(true); 
  };

  const handleDelete = async (id) => {
    try {
      const response = await axios.delete(`${API_URL_USER}/${id}`);
      const data = response.data;
      if (data.success) {
        setUsers((prevData) => prevData.filter((item) => item?._id !== id));
        setUsersX((prevData) => prevData.filter((item) => item?._id !== id));
        toast.success("User deleted successfully!");
      } else {
        toast.warn(data.message || "Failed to delete the User.");
      }
    } catch (error) {
      console.error("Error deleting user:", error);
      toast.error("Error deleting user");
    }
  };

  useEffect(() => {
    const userRole = getUserRole(); 
    setRole(userRole);

    const companyName = (() => {
      const name1 = getCompanyName();
      if (name1) return name1;

      const name2 = getUserName();
      if (name2) return name2;

      const name3 = getsuperadmincompanyname();
      return name3;
    })();

    const authData = getAuthData();

    let filteredUsers = users;
    let Xusers = users;

    if (userRole === "superadmin" && authData.flag === "true" && companyName) {
      filteredUsers = users.filter(
        (item) => item?.companyId?.CompanyName?.toLowerCase() === companyName.toLowerCase()
      );
      Xusers = filteredUsers;
    } else if (userRole === "superadmin" && authData.flag === "false") {
      filteredUsers = users.filter(
        (item) => userRole === "superadmin" ? users : item?.companyName?.toLowerCase() === companyName.toLowerCase()
      );
      Xusers = filteredUsers;
    } else {
      filteredUsers = users.filter(
        (item) => item?.companyId?.CompanyName?.toLowerCase() === companyName.toLowerCase()
      );
      Xusers = filteredUsers;
    }

    setBaseFilteredData(filteredUsers); 
    setUsersX(Xusers);
  }, [users]);

  useEffect(() => {
    setFilteredData(baseFilteredData);
  }, [baseFilteredData]);

  const OpenUserModle = () => {
    setIsOpenUser(!isOpenUser);
  };

  const OpenUserUpdateModle = () => {
    setIsOpenUserUpdate(!isOpenUserUpdate);
  };

  const handleFilterChange = useCallback((filtered) => {
    setFilteredData(filtered);
  }, []);

  const handleItemsPerPageChange = useCallback((newItemsPerPage) => {
    setitemperpage(newItemsPerPage);
  }, []);

  if (!isMounted) {
    return null;
  }

  return (
    <div className="h-[100vh] overflow-hidden">
      <Header className="min-w-full" />
      <div className="flex gap-4">
        <Sidebar />
        <div
          className="w-[80%] xl:w-[85%] h-screen flex flex-col gap-4 justify-start overflow-y-auto pr-4"
          style={{
            height: "calc(100vh - 90px)",
          }}
        >
          <h1 className="text-[#313342] font-medium text-2xl py-5 underline decoration-[#AEADEB] underline-offset-8">
            {role !== "superadmin" ? "Users" : "Super Admins"}
          </h1>
          <AdminDashBDoughnut
            title="Users"
            data={companiesData}
            option={options}
          />
          <div className="py-5">
            <div className="drop-shadow-custom4">
              <SearchAndAddBar
                data={baseFilteredData}
                itemperpage={itemperpage}
                onAddClick={OpenUserModle}
                addLabel="Add User"
                onFilterChange={handleFilterChange}
                onItemsPerPageChange={handleItemsPerPageChange}
              />

              {role !== "superadmin" ? (
                <div className="overflow-x-auto custom-scrollbar">
                  <table className="w-full bg-white border table-auto">
                    <thead className="font-sans font-bold text-sm">
                      <tr className="text-white bg-[#38384A]">
                        <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                          User Name
                        </th>
                        <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden">Image</th>
                        <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">Email</th>
                        <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                          Password
                        </th>
                        <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden">Status</th>
                        <th className="py-3 px-4 min-w-[180px] w-[180px] md:w-[16.66%] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="font-sans font-medium text-sm text-center">
                      {paginatedData?.map((item) => (
                        <tr key={item?._id} className="border-b">
                          <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-center whitespace-normal break-all overflow-hidden">{item?.username}</td>
                          <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-center">
                            <Image
                              width={20} height={20}
                              src={item?.useravatar}
                              alt="Company"
                              className="w-8 h-8 block mx-auto rounded-lg object-cover object-center"
                            />
                          </td>
                          <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">{item?.email}</td>
                          <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">{item?.confirmpassword}</td>
                          <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden text-center">
                            <span className="bg-[#38384A33] px-4 py-2 rounded-[22px] text-xs">
                              {item?.isActive ? "Active" : "Inactive"}
                            </span>
                          </td>
                          <td className="py-3 px-4 min-w-[180px] w-[180px] md:w-[16.66%] whitespace-normal break-all overflow-hidden text-center">
                            <div className="flex gap-4 justify-center">
                              <button onClick={() => handleEdit(item?._id)}>
                                <Image width={20} height={20} src="/edit.png" alt="edit" className="w-6" />
                              </button>
                              <button onClick={() => isopendeletemodel(item?._id)}>
                                <Image width={20} height={20} src="/trash.png" alt="delete" className="w-6" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="overflow-x-auto custom-scrollbar">
                  <table className="w-full bg-white border table-auto">
                    <thead className="font-sans font-bold text-sm text-left">
                      <tr className="text-white bg-[#38384A]">
                        <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                          Username
                        </th>
                        <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                          Email
                        </th>
                        <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                          Role
                        </th>
                        <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                          Status
                        </th>
                        <th className="py-3 px-4 min-w-[180px] w-[180px] md:w-[16.66%] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                          Action
                        </th>
                      </tr>
                    </thead>
                    <tbody className="font-sans font-medium text-sm">
                      {paginatedData
                        ?.filter((user) => user?.username !== "superadmin")
                        ?.map((user) => (
                          <tr key={user?._id} className="border-b">
                            <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-center whitespace-normal break-all overflow-hidden">
                              {user?.username}
                            </td>
                            <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                              {user?.email}
                            </td>
                            <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-center whitespace-normal break-all overflow-hidden">
                              {user?.role}
                            </td>
                            <td className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] text-center whitespace-normal break-all overflow-hidden">
                              <span className="bg-[#38384A33] px-4 py-2 rounded-[22px] text-xs">
                                {user?.isActive ? "Active" : "Inactive"}
                              </span>
                            </td>
                            <td className="py-3 px-4 min-w-[180px] w-[180px] md:w-[16.66%] whitespace-normal break-all overflow-hidden text-center">
                              <div className="flex gap-4 justify-center">
                                <button
                                  onClick={() => handleEdit(user?._id)}
                                  className="text-blue-500 hover:text-blue-700"
                                >
                                  <Image 
                                    width={20} height={20}
                                    src="/edit.png"
                                    alt="edit"
                                    className="w-6"
                                  />
                                </button>
                                <button
                                  onClick={() => isopendeletemodel(user?._id)}
                                  className="text-red-500 hover:text-red-700"
                                >
                                  <Image
                                    width={20} height={20}
                                    src="/trash.png"
                                    alt="delete"
                                    className="w-6"
                                  />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                    </tbody>
                  </table>
                </div>
              )}

              <Pagination
                data={filteredData}
                itemperpage={itemperpage}
                onPageChange={(currentItems) => setPaginatedData(currentItems)}
              />

            </div>
          </div>
        </div>
      </div>
      <AddUserModel
        isOpen={isOpenUser}
        onClose={OpenUserModle}
        fetchData={fetchData}
        setTrigger={setTrigger}
        trigger={trigger}
      />
      <UpdateUserModel
        isOpen={isOpenUserUpdate}
        onClose={OpenUserUpdateModle}
        userId={selectedUserId}
        fetchData={fetchData}
      />
      <DeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onDelete={handleDelete}
        Id={isDeleteModalOpenId}
      />
    </div>
  );
};

export default Page;
