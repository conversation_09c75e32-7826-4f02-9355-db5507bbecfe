import axios from "axios";
import { API_URL_USER, API_URL_Driver } from "../ApiUrls.js";

export const GetUserscount = () => {
  let companyName = "";

  if (typeof window !== "undefined") {
    companyName = localStorage.getItem("companyName") || ""; 
  }
  
  
  return axios
    .get(`${API_URL_USER}`)
    .then((res) => {
      const filteredUsers = res.data.result.filter(
        (user) => user.companyname === companyName
      );

      return { result: filteredUsers, count: filteredUsers.length };
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};

export const GetDrivercount = () => {
  const companyName = localStorage.getItem("companyName");

  return axios
    .get(`${API_URL_Driver}`)
    .then((res) => {
      const filteredDrivers = res.data.Result.filter(
        (driver) => driver.adminCompanyName === companyName
      );

      return { result: filteredDrivers, count: filteredDrivers.length };
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};

export const GetVehiclecount = () => {
  return axios
    .get(`${API_URL_Vehicle}`)
    .then((res) => {
      const filteredVehicle = res.data.Result.filter(
        (driver) => driver.adminCompanyName === companyName
      );

      return { result: filteredVehicle, count: filteredVehicle.length };
    })
    .catch((error) => {
      console.log(`error : ${error}`);
      throw error;
    });
};
