
const net = require("net");
const express = require("express");
const http = require("http");
const { Server } = require("socket.io");

const app = express();
const server = http.createServer(app);
const io = new Server(server, { cors: { origin: "*" } });

// Store connected devices
const connectedDevices = new Map();

const tcpServer = net.createServer((socket) => {
  const clientAddress = `${socket.remoteAddress}:${socket.remotePort}`;
  console.log("📡 Device connected:", clientAddress);

  let deviceImei = null;
  let buffer = Buffer.alloc(0);

  socket.on("data", (data) => {
    buffer = Buffer.concat([buffer, data]);
    
    // First data should be IMEI if not already set
    if (!deviceImei) {
      // IMEI packet: first 2 bytes are the length of IMEI
      if (buffer.length >= 2) {
        const imeiLength = buffer.readUInt16BE(0);
        
        if (buffer.length >= imeiLength + 2) {
          deviceImei = buffer.toString("utf8", 2, imeiLength + 2);
          console.log("✅ IMEI Received:", deviceImei);
          
          // Send acknowledgment (0x01)
          socket.write(Buffer.from([0x01]));
          
          // Remove IMEI data from buffer
          buffer = buffer.slice(imeiLength + 2);
          
          // Add to connected devices
          connectedDevices.set(deviceImei, socket);
          io.emit("devices-update", Array.from(connectedDevices.keys()));
        }
      }
    } else {
      // Handle AVL data packets
      while (buffer.length > 0) {
        // Check if we have at least 8 bytes for the header
        if (buffer.length < 8) break;
        
        const preamble = buffer.readUInt32BE(0);
        const dataLength = buffer.readUInt32BE(4);
        const codecId = buffer[8];
        const recordCount = buffer[9];
        
        // Check if we have the complete packet
        if (buffer.length < dataLength + 12) break;
        
        // Extract the data packet (without header and number of records)
        const packet = buffer.slice(12, 12 + dataLength);
        
        // Parse AVL data
        const parsedData = parseAVLData(packet, codecId, recordCount);
        
        console.log("📥 AVL Data:", {
          imei: deviceImei,
          codecId,
          recordCount,
          data: parsedData
        });
        
        // Send to frontend via socket.io
        io.emit("gps-data", { 
          imei: deviceImei, 
          raw: buffer.slice(0, 12 + dataLength).toString("hex").toUpperCase(),
          parsed: parsedData.length > 0 ? parsedData[0] : null,
          timestamp: new Date().toISOString() 
        });
        
        // Send acknowledgment (number of records)
        const ack = Buffer.alloc(4);
        ack.writeUInt32BE(recordCount, 0);
        socket.write(ack);
        
        // Remove processed data from buffer
        buffer = buffer.slice(12 + dataLength);
      }
    }
  });

  socket.on("close", () => {
    console.log("❌ Device disconnected:", clientAddress);
    if (deviceImei) {
      connectedDevices.delete(deviceImei);
      io.emit("devices-update", Array.from(connectedDevices.keys()));
    }
  });

  socket.on("error", (err) => {
    console.error("Socket error:", err);
    if (deviceImei) {
      connectedDevices.delete(deviceImei);
      io.emit("devices-update", Array.from(connectedDevices.keys()));
    }
  });
});

// Function to parse AVL data
function parseAVLData(data, codecId, recordCount) {
  const records = [];
  let position = 0;
  
  for (let i = 0; i < recordCount; i++) {
    try {
      // Parse timestamp (8 bytes)
      const timestamp = new Date(data.readUInt32BE(position) * 1000);
      position += 8;
      
      // Parse priority (1 byte)
      const priority = data[position];
      position += 1;
      
      // Parse GPS element (15 bytes)
      const longitude = data.readInt32BE(position) / 10000000;
      position += 4;
      const latitude = data.readInt32BE(position) / 10000000;
      position += 4;
      const altitude = data.readUInt16BE(position);
      position += 2;
      const direction = data.readUInt16BE(position);
      position += 2;
      const satellites = data[position] & 0x0F;
      position += 1;
      const speed = data.readUInt16BE(position);
      position += 2;
      
      // Parse IO element
      const eventIOId = data[position];
      position += 1;
      const totalIO = data.readUInt16BE(position);
      position += 2;
      
      // Skip IO data for simplicity
      // In a real implementation, you would parse all IO elements
      position += totalIO * 4; // Assuming each IO is 4 bytes
      
      records.push({
        timestamp,
        priority,
        longitude,
        latitude,
        altitude,
        direction,
        satellites,
        speed,
        eventIOId,
        totalIO
      });
    } catch (err) {
      console.error("Error parsing AVL record:", err);
      break;
    }
  }
  console.log("Parsed Records:", records);
  return records;
}

tcpServer.listen(4002, "0.0.0.0", () => {
  console.log("🚀 TCP server running on 0.0.0.0:4002");
});

server.listen(4001, () => {
  console.log("🌐 WebSocket server running on 4001");
});

// // backend/index.js
// const net = require("net");
// const express = require("express");
// const http = require("http");
// const { Server } = require("socket.io");
// const TeltonikaParser = require("complete-teltonika-parser");

// const app = express();
// const server = http.createServer(app);
// const io = new Server(server, { cors: { origin: "*" } });

// const connectedDevices = new Map();

// const tcpServer = net.createServer((socket) => {
//   const clientAddress = `${socket.remoteAddress}:${socket.remotePort}`;
//   console.log("📡 Device connected:", clientAddress);

//   let deviceImei = null;
//   let buffer = Buffer.alloc(0);

//   socket.on("data", (data) => {
//     buffer = Buffer.concat([buffer, data]);

//     // If IMEI is not yet received
//     if (!deviceImei) {
//       if (buffer.length >= 2) {
//         const imeiLength = buffer.readUInt16BE(0);
//         if (buffer.length >= imeiLength + 2) {
//           deviceImei = buffer.toString("utf8", 2, imeiLength + 2);
//           console.log("✅ IMEI Received:", deviceImei);

//           socket.write(Buffer.from([0x01])); // Acknowledge
//           buffer = buffer.slice(imeiLength + 2);

//           connectedDevices.set(deviceImei, socket);
//           io.emit("devices-update", Array.from(connectedDevices.keys()));
//         }
//       }
//     } else {
//       // Parse AVL data using the library
//       try {
//         const parser = new TeltonikaParser();
//         const parsed = parser.parse(buffer);

//         if (parsed && parsed.records) {
//           console.log("📥 Parsed AVL Data:", {
//             imei: deviceImei,
//             codecId: parsed.codecId,
//             records: parsed.records,
//           });

//           io.emit("gps-data", {
//             imei: deviceImei,
//             parsed: parsed.records[0], // send latest record
//             timestamp: new Date().toISOString(),
//           });

//           // Acknowledge records
//           const ack = Buffer.alloc(4);
//           ack.writeUInt32BE(parsed.records.length, 0);
//           socket.write(ack);

//           buffer = Buffer.alloc(0); // clear buffer after parse
//         }
//       } catch (err) {
//         console.error("❌ Error parsing AVL Data:", err.message);
//       }
//     }
//   });

//   socket.on("close", () => {
//     console.log("❌ Device disconnected:", clientAddress);
//     if (deviceImei) {
//       connectedDevices.delete(deviceImei);
//       io.emit("devices-update", Array.from(connectedDevices.keys()));
//     }
//   });

//   socket.on("error", (err) => {
//     console.error("⚠️ Socket error:", err.message);
//     if (deviceImei) {
//       connectedDevices.delete(deviceImei);
//       io.emit("devices-update", Array.from(connectedDevices.keys()));
//     }
//   });
// });

// tcpServer.listen(4002, "0.0.0.0", () => {
//   console.log("🚀 TCP server running on 0.0.0.0:4002");
// });

// server.listen(4001, () => {
//   console.log("🌐 WebSocket server running on 4001");
// });
