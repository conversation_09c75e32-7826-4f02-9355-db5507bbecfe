import { connect } from "@config/db.js";
import Signature from "@models/Signature/Signature.Model.js";
import { catchAsyncErrors } from "@middlewares/catchAsyncErrors.js";
import { uploadImage } from "services/uploadImage.js";
import { NextResponse } from "next/server";

export async function POST(request) {
  try {
    await connect();
    const data = await request.formData();

    // accept multiple frontend keys for file
    const file1 = data.get("imageFile") || data.get("useravatar") || data.get("image");

    let imageFile;
    let imagepublicId;

    try {
      const { imageFile: uploadedUrl, imagePublicId } = await uploadImage(file1);
      imageFile = uploadedUrl;
      imagepublicId = imagePublicId;
    } catch (err) {
      console.error("uploadImage helper failed:", err);
      return NextResponse.json({ error: "Image upload failed", status: 500 });
    }

    // Constructing formDataObject excluding the files
    const formDataObject = {};
    for (const [key, value] of data.entries()) {
      if (key === "imageFile" || key === "useravatar" || key === "image") continue;
      formDataObject[key] = value;
    }

    const {
      name,
      description,
      imageName,
      isActive,
      adminCreatedBy,
      adminCompanyName,
      companyId,
    } = formDataObject;

    const existingUser = await Signature.findOne({
      $and: [{ name: name }, { adminCompanyName: adminCompanyName }],
    });
    if (existingUser) {
      return NextResponse.json({
        error: "Signature Already Exist",
        status: 400,
      });
    }

    // Create and save the new signature
    const newSignature = new Signature({
      name,
      description,
      imageName,
      imagepublicId,
      imageFile,
      adminCreatedBy,
      adminCompanyName,
      companyId,
      isActive: isActive || false,
    });

    const savedSignature = await newSignature.save();
    if (!savedSignature) {
      return NextResponse.json({ message: "Signature not added", status: 400 });
    } else {
      return NextResponse.json({
        message: "Signature created successfully",
        success: true,
        status: 200,
      });
    }
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: error.message, status: 500 });
  }
}

export const GET = catchAsyncErrors(async () => {
  await connect();
  const allSignature = await Signature.find().populate("companyId").sort({ createdAt: -1 });
  const SignatureCount = await Signature.countDocuments();
  if (!allSignature || allSignature.length === 0) {
    return NextResponse.json({ Result: allSignature });
  } else {
    return NextResponse.json({
      Result: allSignature,
      count: SignatureCount,
    });
  }
});