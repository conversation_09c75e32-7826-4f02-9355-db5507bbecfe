import { connect } from "@config/db.js";
import Signature from "@models/Signature/Signature.Model.js";
import cloudinary from "@middlewares/cloudinary.js";
import { NextResponse } from "next/server";
import { uploadImage } from "services/uploadImage.js";// Import the uploadImage service

export const PUT = async (request, context) => {
  await connect();
  const id = context.params.SignatureID;
  const data = await request.formData();

  // Get existing signature by ID
  const existingSignature = await Signature.findById({ _id: id });
  if (!existingSignature) {
    return NextResponse.json({ error: "Signature not found", status: 404 });
  }

  // accept multiple frontend keys and use shared helper
  const file1 = data.get("imageFile") || data.get("useravatar") || data.get("image");
  let imageFile = existingSignature.imageFile;
  let imagepublicId = existingSignature.imagepublicId;

  if (file1 && typeof file1 === "object" && file1.name) {
    try {
      // upload via shared helper (returns default values if no file)
      const { imageFile: uploadedUrl, imagePublicId } = await uploadImage(file1);

      // if upload succeeded and returned a new public id, delete old image (best-effort)
      if (uploadedUrl && imagePublicId) {
        if (imagepublicId) {
          try {
            await cloudinary.uploader.destroy(imagepublicId);
          } catch (err) {
            console.error("Failed to delete old image from Cloudinary:", err);
          }
        }
        imageFile = uploadedUrl;
        imagepublicId = imagePublicId;
      }
    } catch (err) {
      console.error("uploadImage failed:", err);
      return NextResponse.json({ error: "Image upload failed", status: 500 });
    }
  }

  // Constructing the updated data
  const formDataObject = {};
  for (const [key, value] of data.entries()) {
    if (key !== "imageFile" && key !== "useravatar" && key !== "image") {
      formDataObject[key] = value;
    }
  }

  const {
    name,
    description,
    imageName,
    isActive,
    adminCreatedBy,
    adminCompanyName,
    companyId,
  } = formDataObject;

  // Update the existing signature
  existingSignature.companyId = companyId || existingSignature.companyId;
  existingSignature.name = name || existingSignature.name;
  existingSignature.description = description || existingSignature.description;
  existingSignature.imageName = imageName || existingSignature.imageName;
  existingSignature.imageFile = imageFile;
  existingSignature.imagepublicId = imagepublicId;
  existingSignature.isActive =
    isActive !== undefined ? isActive : existingSignature.isActive;
  existingSignature.adminCreatedBy =
    adminCreatedBy || existingSignature.adminCreatedBy;
  existingSignature.adminCompanyName =
    adminCompanyName || existingSignature.adminCompanyName;

  const updatedSignature = await existingSignature.save();

  return NextResponse.json({
    message: "Signature updated successfully",
    success: true,
    status: 200,
    data: updatedSignature,
  });
};

// GET handler for retrieving a specific signature by ID
export const GET = async (request, context) => {
  try {
    // Connect to the database
    await connect();

    // Extract the Signature ID from the request parameters
    const { SignatureID } = context.params; // Correctly destructuring SignatureID from context.params

    // Find the signature by ID
    const foundSignature = await Signature.findById(SignatureID);

    // Check if the signature exists
    if (!foundSignature) {
      return NextResponse.json({ result: "No Signature Found", status: 404 });
    }

    // Return the found signature as a JSON response
    return NextResponse.json({ result: foundSignature, status: 200 });
  } catch (error) {
    console.error("Error fetching signature:", error); // Log the error for debugging
    return NextResponse.json({ error: "Internal Server Error", status: 500 }); // Return an error response
  }
};

// DELETE handler for deleting a manufacturer
export const DELETE = async (request, { params }) => {
  try {
    // Connect to the database
    await connect();

    const { SignatureID } = params; // Access the ManufacturerID from params

    // Find and delete the manufacturer
    const deletedSignatureID = await Signature.findByIdAndDelete(SignatureID);

    if (!deletedSignatureID) {
      return NextResponse.json({
        error: "Supplier not found",
        status: 404,
      });
    }

    return NextResponse.json({
      message: "Signature deleted successfully",
      success: true,
      status: 200,
    });
  } catch (error) {
    console.error("Error deleting Singature:", error);
    return NextResponse.json({
      error: "An error occurred while deleting the Supplier",
      status: 500,
    });
  }
};
