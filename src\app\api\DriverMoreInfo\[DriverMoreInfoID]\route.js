import { connect } from "@config/db.js";
import DriverMoreInfo from "@models/DriverMoreInfo/DriverMoreInfo.model.js";
import { NextResponse } from "next/server";

export async function GET(request, context) {
  try {
    // Connect to the database
    await connect();

    // Extract the product ID from the request parameters
    const id = context.params.DriverMoreInfoID;

    // Find all records related to the driverId
    const find_user_all = await DriverMoreInfo.find({
      $or: [
        { vehicleId: id, driverId: { $ne: id } }, // Match vehicleId but not driverId
        { driverId: id, vehicleId: { $ne: id } }  // Match driverId but not vehicleId
      ]
    });
    

    // If there are records associated with driverId
    if (find_user_all.length > 0) {
      // Return all records as a JSON response
      return NextResponse.json({ result: find_user_all, status: 200 });
    } else {
      // If no records found for driverId, try to find by _id
      const Find_User = await DriverMoreInfo.findById(id);

      // Check if the product exists
      if (!Find_User) {
        return NextResponse.json({ result: [], status: 404 });
      } else {
        // Return the found product as a JSON response
        return NextResponse.json({ result: Find_User, status: 200 });
      }
    }
  } catch (error) {
    console.error("Error retrieving product:", error);
    // Return an error response
    return NextResponse.json({ message: "Internal Server Error", status: 500 });
  }
}

export const DELETE = async (request, context) => {
  try {
    await connect();

    const id = context.params.DriverMoreInfoID; // Ensure DriverMoreInfoID is correctly passed

    let deletedDriverMoreInfo = await DriverMoreInfo.findOneAndDelete({
      _id: id,
    });

    if (!deletedDriverMoreInfo) {
      return NextResponse.json({
        message: "DriverMoreInfo not found",
        status: 404,
      });
    }

    return NextResponse.json({
      message: "DriverMoreInfo deleted successfully",
      success: true,
      status: 200,
    });
  } catch (error) {
    console.error("Error deleting DriverMoreInfo:", error); // Log the error for debugging
    return NextResponse.json({
      message: "An error occurred while deleting the DriverMoreInfo",
      error: error.message, // Provide error details for easier debugging
      status: 500,
    });
  }
};


export async function PUT(request) {
  try {
    await connect(); // Connect to the database

    const data = await request.formData(); // Using formData since frontend is sending multipart form-data
    const formDataObject = Object.fromEntries(data.entries());
    const {driverId, vehicleId, startDate, cost, pay, discription } = formDataObject;
    if (!vehicleId) {
      return NextResponse.json({
        error: "vehicleId is required",
        status: 400,
      });
    }
    const startdt = new Date(startDate);
    if (isNaN(startdt.getTime())) {
      return NextResponse.json(
        { error: "Invalid Start Date format", status: 400 },
        { status: 400 }
      );
    }
  

    // Find the driver by driverId and startDate
    const driver = await DriverMoreInfo.findOne({
      driverId: driverId,
      vehicleId: vehicleId,
      startDate: startdt
    });

    if (!driver) {
      return NextResponse.json({ error: "Driver not found", status: 404 });
    }

    if(cost === 0 || cost === null){
      driver.cost = driver.cost;
    }
    else{
      driver.cost += Number(cost);
    }

    if(pay === 0 || pay === null){
      driver.pay = driver.pay;
    }
    else{
      driver.pay += Number(pay);
    }

    if(discription === "" || discription === null){
      driver.discription = driver.discription;
    }
    else{
      driver.discription = discription;
  }

    // Save updated driver details
    await driver.save();

    return NextResponse.json({
      message: "Driver details updated successfully",
      status: 200,
    });
  } catch (error) {
    console.error("Error updating driver details:", error);
    return NextResponse.json({
      error: "Failed to update driver details",
      status: 500,
    });
  }
}

