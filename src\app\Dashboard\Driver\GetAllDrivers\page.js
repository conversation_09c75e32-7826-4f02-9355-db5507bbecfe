"use client";

import React, { useState, useEffect, useCallback } from "react";
import Header from "../../Components/Header";
import Sidebar from "../../Components/Sidebar";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import AddDriverModel from "../AddDriver/AddDriverModel";
import UpdateDriverModel from "../UpdateDriver/UpdateDriverModel";
import axios from "axios";
import { API_URL_Driver } from "../../Components/ApiUrl/ApiUrls";
import {
  getCompanyName, getsuperadmincompanyname, getUserName
} from "@/utils/storageUtils"; 
import Link from "next/link";
import DeleteModal from "../../Components/DeleteModal";
import Image from "next/image";
import {Count} from "@/utils/count";
import SearchAndAddBar from "@/utils/searchAndAddBar";
import Pagination from "@/utils/pagination";

const Page = () => {
  const columns = [
    { name: "Name", accessor: (row) => `${row.First_Name} ${row.lastName}` },
    { name: "Email", accessor: "email" },
    { name: "Phone Number", accessor: "tel1" },
    { name: "License Number", accessor: "licenseNumber" },
    { name: "NI Number", accessor: "niNumber" },
    { name: "Local Authority", accessor: (row) => row.LocalAuth || "N/A" },
    { name: "Date Of Birth", accessor: (row) => formatDate(row.dateOfBirth) },
    { name: "Balance", accessor: "totalamount" },
  ];

  const [drivers, setDrivers] = useState([]);
  const [baseFilteredData, setBaseFilteredData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [isMounted, setIsMounted] = useState(false);
  const [isOpenDriver, setIsOpenDriver] = useState(false);
  const [isOpenDriverUpdate, setIsOpenDriverUpdate] = useState(false);
  const [selectedCompanyName, setSelectedCompanyName] = useState("");
  const [selectedUserId, setSelectedUserId] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleteModalOpenId, setIsDeleteModalOpenId] = useState(null);
  const [itemperpage, setitemperpage] = useState(Count);
  const [paginatedData, setPaginatedData] = useState([]);

  const fetchData = useCallback(async () => {
    try {
      const response = await axios.get(`${API_URL_Driver}`);
      setDrivers(response?.data?.result);
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("An error occurred while fetching drivers.");
      setDrivers([]);
    }
  }, []);

  useEffect(() => {
    setIsMounted(true);
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    const companyName = getCompanyName();
    const superAdminName = getsuperadmincompanyname();
    const userName = getUserName();
    const storedcompanyName = companyName || superAdminName || userName;
    setSelectedCompanyName(storedcompanyName);
  }, []);

  const isopendeletemodel = (id) => {
    setIsDeleteModalOpenId(id); 
    setIsDeleteModalOpen(true); 
  };
  const handleDelete = async (id) => {
    try {
      const response = await axios.delete(`${API_URL_Driver}/${id}`);
      const { data } = response;
      if (data?.success) {
        setDrivers((prevData) => prevData?.filter((item) => item?._id !== id));
        toast.success("Driver deleted successfully.");
      } else {
        toast.warn(data?.message || "Failed to delete the driver.");
      }
    } catch (error) {
      console.error("Error deleting driver:", error);
      toast.error(
        "An error occurred while deleting the driver. Please try again."
      );
    }
  };

  useEffect(() => {
    if (drivers.length > 0) {
      let companyFilteredData;

      if (selectedCompanyName?.toLowerCase() === "superadmin") {
        companyFilteredData = drivers;
      } else {
        companyFilteredData = drivers?.filter(
          (item) =>
            item?.adminCompanyName?.toLowerCase() === "superadmin" ||
            item?.adminCompanyName?.toLowerCase() === selectedCompanyName?.toLowerCase()
        );
      }

      setBaseFilteredData(companyFilteredData);
    } else {
      setBaseFilteredData([]);
    }
  }, [drivers, selectedCompanyName]);

  useEffect(() => {
    setFilteredData(baseFilteredData);
  }, [baseFilteredData]);

  const handleEdit = (id) => {
    setSelectedUserId(id);
    setIsOpenDriverUpdate(true);
  };

  const handleFilterChange = useCallback((filtered) => {
    setFilteredData(filtered);
  }, []);

  const handleItemsPerPageChange = useCallback((newItemsPerPage) => {
    setitemperpage(newItemsPerPage);
  }, []);

  const formatDate = (dateString) => {
    const dateObject = new Date(dateString);
    return `${(dateObject.getMonth() + 1)
      .toString()
      .padStart(2, "0")}/${dateObject
        .getDate()
        .toString()
        .padStart(2, "0")}/${dateObject.getFullYear()}`;
  };

  if (!isMounted) return null;

  const OpenDriverModel = () => {
    setIsOpenDriver(!isOpenDriver);
  };

  return (
    <div className="h-[100vh] overflow-hidden">
      <Header className="min-w-full" />
      <div className="flex gap-4">
        <Sidebar />
        <div
          className="w-[80%] xl:w-[85%] h-screen flex flex-col justify-start overflow-y-auto pr-4"
          style={{
            height: "calc(100vh - 90px)",
          }}
        >
          <h1 className="text-[#313342] font-medium text-2xl py-5 pb-8  flex gap-2 items-center">
            <div className="myborder flex gap-3 border-2 border-t-0 border-l-0 border-r-0">
              <span className="opacity-65" >Driver Management</span>
              <div className="flex items-center gap-3 myborder2">
                <span><Image width={15} height={15} src="/setting_arrow.svg" className="w-2 h-4 object-cover object-center  " alt="arrow"/></span>
                <span>All Drivers</span>
              </div>
            </div>
          </h1>

          <div className="w-full py-5">
            <div className="drop-shadow-custom4 ">
              <SearchAndAddBar
                data={baseFilteredData}
                itemperpage={itemperpage}
                onAddClick={OpenDriverModel}
                addLabel="Add Driver"
                onFilterChange={handleFilterChange}
                onItemsPerPageChange={handleItemsPerPageChange}
                searchPlaceholder="Search by email..."
              />

              <div className="overflow-x-auto overflow-y-hidden custom-scrollbar">
                <table className="w-full bg-white border  table-fixed">
                  <thead className="font-sans font-bold text-sm text-left">
                    <tr className="text-white bg-[#38384A]">
                      {columns?.map((column) => (
                        <th
                          key={column?.name}
                          className="px-4 py-3 min-w-[150px] w-[150px] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden"
                        >
                          {column?.name}
                        </th>
                      ))}
                      <th className="px-4 py-3 min-w-[150px] w-[150px] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="font-sans font-medium text-sm">
                    {paginatedData?.map((row) => (
                      <tr key={row?._id} className="border-b text-center">
                        {columns?.map((column) => (
                          <td
                            key={column?.name}
                            className={`py-3 px-4 min-w-[150px] w-[150px] whitespace-normal break-all overflow-hidden`}
                          >
                            {typeof column?.accessor === "function"
                              ? column.accessor(row)
                              : row[column?.accessor]}
                          </td>
                        ))}
                        <td className="py-3 px-4 min-w-[150px] w-[150px] whitespace-normal break-all overflow-hidden">
                          {(
                            getUserName()?.toLowerCase() === "superadmin" ||
                            (selectedCompanyName && getCompanyName()?.toLowerCase() === selectedCompanyName.toLowerCase() &&
                              row?.adminCompanyName?.toLowerCase() === selectedCompanyName.toLowerCase()
                            )
                          ) ? (
                            <div className="flex gap-4 justify-center">
                              <div className="relative group">
                                <button onClick={() => handleEdit(row?._id)}>
                                  <Image width={25} height={25} src="/edit.png" alt="edit" className="w-6" />
                                </button>
                              </div>
                              <div className="relative group">
                                <button onClick={() => isopendeletemodel(row?._id)}>
                                  <Image width={25} height={25} src="/trash.png" alt="delete" className="w-6" />
                                </button>
                              </div>
                              <div className="relative group">
                                <Link
                                  passHref
                                  href={`/Dashboard/Driver/CombineDriverAndVehicle/${row?._id}`}
                                >
                                  <div className="flex items-center gap-3">
                                    <Image width={24} height={24} src="/bcar.png" alt="info" />
                                  </div>
                                </Link>
                              </div>
                            </div>
                          ) : (
                            <span className="text-gray-500 italic">Added by Admin</span>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>


              <Pagination
                data={filteredData}
                itemperpage={itemperpage}
                onPageChange={(currentItems) => {
                  setPaginatedData(currentItems);
                }}
              />
            </div>
          </div>
        </div>
      </div>

      <AddDriverModel
        isOpen={isOpenDriver}
        onClose={OpenDriverModel}
        fetchData={fetchData}
      />
      <UpdateDriverModel
        isOpen={isOpenDriverUpdate}
        onClose={() => setIsOpenDriverUpdate(false)}
        selectedUserId={selectedUserId}
        fetchDataa={fetchData}
      />
      <DeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onDelete={handleDelete}
        Id={isDeleteModalOpenId}
      />
    </div>
  );
};

export default Page;
