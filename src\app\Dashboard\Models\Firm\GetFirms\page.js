"use client";

import React, { useState, useEffect, useCallback } from "react";
import Header from "../../../Components/Header";
import Sidebar from "../../../Components/Sidebar";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import AddFirmModel from "../AddFirm/AddFirmModel";
import UpdateFirmModel from "../UpdateFirm/UpdateFirmModel";
import { API_URL_Firm } from "@/app/Dashboard/Components/ApiUrl/ApiUrls";
import { GetFirm } from "@/app/Dashboard/Components/ApiUrl/ShowApiDatas/ShowApiDatas";
import { getCompanyName, getsuperadmincompanyname, getUserName } from "@/utils/storageUtils";
import axios from "axios";
import DeleteModal from "@/app/Dashboard/Components/DeleteModal";
import Image from "next/image";
import {Count} from "@/utils/count";
import SearchAndAddBar from "@/utils/searchAndAddBar";
import Pagination from "@/utils/pagination";


const Page = () => {
  const [data, setData] = useState([]);
  const [baseFilteredData, setBaseFilteredData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [isMounted, setIsMounted] = useState(false);
  const [isOpenFirm, setIsOpenFirm] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState(null);
  const [isOpenDriverUpdate, setIsOpenDriverUpdate] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleteModalOpenId, setIsDeleteModalOpenId] = useState(null);
  const [currentCompany, setCurrentCompany] = useState(null);
  const [itemperpage, setitemperpage] = useState(Count);
  const [paginatedData, setPaginatedData] = useState([]);

  useEffect(() => {
    setIsMounted(true);
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const { result } = await GetFirm();
      setData(result);
    } catch (error) {
      console.error("Error fetching data:", error);
      setData([]);
    }
  };

  useEffect(() => {
    const companyName = getCompanyName();
    const superAdminName = getsuperadmincompanyname();
    const userName = getUserName();
    const storedcompanyName = companyName || superAdminName || userName;
    setCurrentCompany(storedcompanyName);

    if (data.length > 0) {
      let companyFilteredData;

      if (userName?.toLowerCase() === "superadmin") {
        companyFilteredData = data;
      } else {
        companyFilteredData = data?.filter(
          (item) =>
            item?.adminCompanyName?.toLowerCase() === "superadmin" ||
            item?.adminCompanyName?.toLowerCase() === storedcompanyName?.toLowerCase()
        );
      }

      setBaseFilteredData(companyFilteredData);
    } else {
      setBaseFilteredData([]);
    }
  }, [data]);

  useEffect(() => {
    setFilteredData(baseFilteredData);
  }, [baseFilteredData]);


  const isopendeletemodel = (id) => {
    setIsDeleteModalOpenId(id);
    setIsDeleteModalOpen(true);
  };

  const handleDelete = async (id) => {
    try {
      const response = await axios.delete(`${API_URL_Firm}/${id}`);
      const { data } = response;
      
      if (data.status === 200) {
        setData((prevData) => prevData?.filter((item) => item?._id !== id));
        toast.success("Firm deleted successfully!");
      } else {
        toast.warn(data.message || "Failed to delete the Firm.");
      }
    } catch (error) {
      console.error("Error deleting Firm:", error);
      toast.error(
        error.response?.data?.message ||
        "An error occurred while deleting the Firm. Please try again."
      );
    }
  };

  const handleEdit = (id) => {
    setSelectedUserId(id);
    setIsOpenDriverUpdate(true);
    OpenDriverUpdateModle();
  };

  const handleFilterChange = useCallback((filtered) => {
    setFilteredData(filtered);
  }, []);

  const handleItemsPerPageChange = useCallback((newItemsPerPage) => {
    setitemperpage(newItemsPerPage);
  }, []);

  if (!isMounted) {
    return null;
  }

  const OpenFirmModle = () => {
    setIsOpenFirm(!isOpenFirm);
  };

  const OpenDriverUpdateModle = () => {
    setIsOpenDriverUpdate(!isOpenDriverUpdate);
  };

  return (
    <div div className="h-[100vh] overflow-hidden">
      <Header className="min-w-full" />
      <div className="flex gap-4">
        <Sidebar />
        <div className="w-[80%] xl:w-[85%] min-h-screen">
          <div
            className="justify-between mx-auto items-center  w-full overflow-y-auto pr-4 "
            style={{ height: "calc(100vh - 90px)" }}
          >
            <h1 className="text-[#313342] font-medium text-2xl py-5 pb-8  flex gap-2 items-center">
              <div className="myborder flex gap-3 border-2 border-t-0 border-l-0 border-r-0">
                <span className="opacity-65">Vehicle Setting</span>
                <div className="flex items-center gap-3 myborder2">
                  <span>
                    <Image width={2} height={4}
                      src="/setting_arrow.svg"
                      className="w-2 h-4 object-cover object-center  "
                      alt="arrow"
                    />
                  </span>
                  <span>Firms</span>
                </div>
              </div>
            </h1>

            <div className="w-full py-5">
              <div className="drop-shadow-custom4 ">
                <SearchAndAddBar
                  data={baseFilteredData}
                  itemperpage={itemperpage}
                  onAddClick={OpenFirmModle}
                  addLabel="Add Firm"
                  onFilterChange={handleFilterChange}
                  onItemsPerPageChange={handleItemsPerPageChange}
                  searchPlaceholder="Search by firm name..."
                />

                <div className="overflow-x-auto overflow-y-hidden custom-scrollbar">
                  <table className="w-full bg-white border table-fixed">
                    <thead className="font-sans font-bold text-sm text-left">
                      <tr className="text-white bg-[#38384A]">
                        <th className="px-4 py-3 min-w-[150px] w-[150px] text-center text-white bg-custom-bg">
                          Firm Name
                        </th>
                        <th className="px-4 py-3 min-w-[150px] w-[150px] text-center text-white bg-custom-bg">
                          Firm Description
                        </th>
                        <th className="px-4 py-3 min-w-[150px] w-[150px] text-center text-white bg-custom-bg">
                          Status
                        </th>
                        <th className="px-4 py-3 min-w-[150px] w-[150px] text-center text-white bg-custom-bg">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="font-sans font-medium text-sm">
                      {paginatedData?.length > 0 ? (
                        paginatedData?.map((row) => (
                          <tr key={row?._id} className="border-b text-center">
                            <td className="py-3 px-4 min-w-[150px] w-[150px] whitespace-normal break-all overflow-hidden">
                              {row?.name}
                            </td>

                            <td className="py-3 px-4 min-w-[150px] w-[150px] whitespace-normal break-all overflow-hidden">
                              {row?.description}
                            </td>

                            <td className="py-3 px-4 min-w-[150px] w-[150px] whitespace-normal break-all overflow-hidden">
                              <span
                                className={`px-4 py-2 rounded-[22px] text-xs ${row?.isActive || row?.isActive === false
                                    ? "bg-[#38384A33]"
                                    : ""
                                  }`}
                              >
                                {row.isActive ? "Active" : "Inactive"}
                              </span>
                            </td>
                            <td className="py-3 px-4 min-w-[150px] w-[150px] whitespace-normal break-all overflow-hidden">
                              {(
                                getUserName()?.toLowerCase() === "superadmin" ||
                                (currentCompany && getCompanyName()?.toLowerCase() === currentCompany.toLowerCase() &&
                                  row?.adminCompanyName?.toLowerCase() === currentCompany?.toLowerCase()
                                )
                              ) ? (
                                <div className="flex gap-4 justify-center">
                                  <div className="relative group">
                                    <button onClick={() => handleEdit(row._id)}>
                                      <Image width={25} height={25}
                                        src="/edit.png"
                                        alt="edit"
                                        className="w-6"
                                      />
                                    </button>
                                  </div>

                                  <div className="relative group">
                                    <button
                                      onClick={() => isopendeletemodel(row._id)}
                                    >
                                      <Image width={25} height={25}
                                        src="/trash.png"
                                        alt="delete"
                                        className="w-6"
                                      />
                                    </button>
                                  </div>
                                </div>
                              ) : (
                                <span className="text-gray-500 italic">Added by Admin</span>
                              )}
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan="4" className="py-3 px-4 text-center">
                            No data found
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>

                <Pagination
                  data={filteredData}
                  itemperpage={itemperpage}
                  onPageChange={(currentItems) => {
                    setPaginatedData(currentItems);
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <AddFirmModel
        isOpen={isOpenFirm}
        onClose={OpenFirmModle}
        fetchData={fetchData}
      />
      <UpdateFirmModel
        isOpen={isOpenDriverUpdate}
        onClose={OpenDriverUpdateModle}
        firmId={selectedUserId}
        fetchData={fetchData}
      />
      <DeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onDelete={handleDelete}
        Id={isDeleteModalOpenId}
      />
    </div>
  );
};

export default Page;
