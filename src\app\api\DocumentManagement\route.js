import jwt from "jsonwebtoken";
import { connect } from "@config/db.js";
import DocumentManagement from "@models/DocumentManagement/DocumentManagement.Model.js";
import { catchAsyncErrors } from "@middlewares/catchAsyncErrors.js";
import { NextResponse } from "next/server";

export const POST = catchAsyncErrors(async (request) => {
  await connect();

  /* ---------------- JWT AUTH ---------------- */
  const authHeader = request.headers.get("Authorization");
  if (!authHeader) {
    return NextResponse.json({ error: "Unauthorized - No Authorization header", status: 401 });
  }

  // Fix: Use && instead of || to properly extract token from "Bearer TOKEN" format
  const token = authHeader && authHeader.split(" ")[1];

  if (!token) {
    return NextResponse.json({ error: "Invalid Authorization header format", status: 401 });
  }

  const secret = process.env.JWT_SECRET;
  let decoded;
  try {
    decoded = jwt.verify(token, secret);
  } catch (error) {
    console.error("JWT verification error:", error.message);
    return NextResponse.json({ error: "Invalid token", status: 401 });
  }

  // 👉 pull company data from the token
  const companyId   = decoded?.userId;
  const companyName = decoded?.company || decoded?.companyRelated;

  if (!companyId || !companyName) {
    return NextResponse.json({ error: "Company info missing in token", status: 400 });
  }

  /* ---------------- INPUT ---------------- */
  const {
    documentname,
    entityType,  // Changed from description to entityType to match frontend
    isActive,
    adminCreatedBy,
    adminCompanyName,
    companyId: frontendCompanyId,
  } = await request.json();

  /* ---------------- VALIDATION ---------------- */
  if (!entityType || !Array.isArray(entityType) || entityType.length === 0) {
    return NextResponse.json({
      error: "Please select at least one entity.",
      status: 400,
    });
  }

  if (!documentname || documentname.trim() === "") {
    return NextResponse.json({
      error: "Please add a document type.",
      status: 400,
    });
  }

  /* ---------------- DUPLICATE CHECK ---------------- */
  // Check if the same document type name already exists for any of the selected entities
  const duplicateCheck = await DocumentManagement.findOne({
    $and: [
      { documentname: documentname.trim() },
      { adminCompanyId: companyId },
      { adminCompanyName: companyName },
      { entityType: { $in: entityType } } // Check if any of the selected entities already have this document type
    ]
  });

  if (duplicateCheck) {
    return NextResponse.json({
      error: "This document type already exists for the selected entity.",
      status: 400,
    });
  }

  /* ---------------- CREATE ---------------- */
  const newDoc = new DocumentManagement({
    documentname: documentname.trim(),
    entityType,  // Store entityType array
    isActive,
    adminCreatedBy: adminCreatedBy || companyName,
    adminCompanyId: companyId,       // <‑‑ store ID from token
    adminCompanyName: companyName,   // <‑‑ store name from token
  });

  const saved = await newDoc.save();

  return NextResponse.json(
    saved
      ? { message: "Document type has been added.", success: true, status: 200 }
      : { error: "Document not added", status: 400 }
  );
});

/* ---------------- GET ALL ---------------- */
export const GET = catchAsyncErrors(async (request) => {
  await connect();

  // Optional: Add authentication for GET requests too
  const authHeader = request.headers.get("Authorization");

  if (authHeader) {
    // If authorization header is present, filter by company
    const token = authHeader && authHeader.split(" ")[1];

    if (token) {
      const secret = process.env.JWT_SECRET;
      try {
        const decoded = jwt.verify(token, secret);
        const companyId = decoded?.userId;
        const companyName = decoded?.company || decoded?.companyRelated;

        // Filter documents by company
        const result = await DocumentManagement.find({
          $or: [
            { adminCompanyId: companyId },
            { adminCompanyName: companyName }
          ]
        });
        const count = await DocumentManagement.countDocuments({
          $or: [
            { adminCompanyId: companyId },
            { adminCompanyName: companyName }
          ]
        });

        return NextResponse.json({ result, count, success: true });
      } catch (error) {
        console.error("Token verification error in GET:", error.message);
        // If token is invalid, return all documents (fallback)
      }
    }
  }

  // If no auth header or invalid token, return all documents
  const result = await DocumentManagement.find();
  const count = await DocumentManagement.countDocuments();
  return NextResponse.json({ result, count, success: true });
});
