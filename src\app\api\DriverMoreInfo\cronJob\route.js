import { NextResponse } from "next/server";
import { connect } from "@config/db.js";
import DriverMoreInfo from "@models/DriverMoreInfo/DriverMoreInfo.model.js";

export const GET = async () => {
  try {
    await connect();

    const drivers = await DriverMoreInfo.find({});
    for (const driver of drivers) {
      try {
        const firstRecord = await DriverMoreInfo.findOne({
          driverId: driver.driverId,
          vehicleId: driver.vehicleId,
          adminCompanyName: driver.adminCompanyName,
        }).sort({ startDate: -1 });

        const lastRecord = await DriverMoreInfo.findOne({
          driverId: driver.driverId,
          vehicleId: driver.vehicleId,
          adminCompanyName: driver.adminCompanyName,
          // description: { $exists: true, $ne: "" } // Ensures description exists and is not empty
        }).sort({ startDate: -1, createdAt: -1 }).limit(1);


        const lastDate = new Date(firstRecord.startDate);
        const currentDate = new Date();
        const daysDifference = Math.floor(
          (currentDate - lastDate) / (1000 * 60 * 60 * 24)
        );

        let shouldInsert = false;
        let totalamount = driver.payment;

        if (
          (driver.paymentcycle === "perday" && daysDifference >= 1) ||
          (driver.paymentcycle === "perweek" && daysDifference >= 7)
        ) {
          shouldInsert = true;
        }

        if (shouldInsert) {
          let newStartDate = new Date(lastDate);

          while (newStartDate < currentDate) {
            newStartDate.setDate(newStartDate.getDate() + 1);

            if (newStartDate >= currentDate) {
              break;
            }

            if (lastRecord && lastRecord.cost === 0 && lastRecord.pay === 0) {
              totalamount = firstRecord.totalamount + driver.payment;
            } else {
              totalamount = lastRecord.totalamount + driver.payment;
            }
            
            const newDriverMoreInfo = new DriverMoreInfo({
              driverId: driver.driverId,
              driverName: driver.driverName,
              vehicle: driver.vehicle,
              vehicleId: driver.vehicleId,
              startDate: new Date(newStartDate),
              paymentcycle: driver.paymentcycle,
              payment: driver.payment,
              totalamount,
              adminCreatedBy: driver.adminCreatedBy,
              adminCompanyName: driver.adminCompanyName,
              adminCompanyId: driver.adminCompanyId,
            });

            await newDriverMoreInfo.save();
          }
        }
      } catch (driverError) {
        console.error(`Error processing driverId: ${driver.driverId}`, driverError);
      }
    }

    return NextResponse.json({ message: "Records checked & updated" });

  } catch (error) {
    console.error("Error in GET request:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
};
