"use client";

import React, { useState, useEffect, useCallback } from "react";
import { toast } from "react-toastify";
import Header from "../../../Components/Header";
import Sidebar from "../../../Components/Sidebar";
import AddServiceModal from "../AddServiceModal/AddServiceModal";
import { API_URL_VehicleService } from "../../../Components/ApiUrl/ApiUrls";
import { getCompanyName, getsuperadmincompanyname, getUserName } from "@/utils/storageUtils";
import axios from "axios";
import jsPDF from "jspdf";
import { isAuthenticated } from "@/utils/verifytoken";
import { useRouter } from "next/navigation";
import BackButton from "@/app/Dashboard/Components/BackButton";
import Image from "next/image";
import {Count} from "@/utils/count";
import SearchAndAddBar from "@/utils/searchAndAddBar";
import Pagination from "@/utils/pagination";


const Page = ({ params }) => {
  const router = useRouter();
  const addServiceId = params?.AddServiceReport;
  
  const [data, setData] = useState([]);
  const [baseFilteredData, setBaseFilteredData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [isOpenTitle, setIsOpenTitle] = useState(false);
  const [selectedCompanyName, setSelectedCompanyName] = useState("");
  const [itemperpage, setitemperpage] = useState(Count);
  const [paginatedData, setPaginatedData] = useState([]);
  const [selectedid, setselectedid] = useState(null);

  
  const handleFilterChange = useCallback((filtered) => {
    setFilteredData(filtered);
  }, []);

  const handleItemsPerPageChange = useCallback((newItemsPerPage) => {
    setitemperpage(newItemsPerPage);
  }, []);

  useEffect(() => {
    if (!isAuthenticated()) {
      router.push("/");
      return;
    }
  }, [router]);

  useEffect(() => {
    const companyNameFromStorage = getCompanyName();
    if (companyNameFromStorage) {
      setSelectedCompanyName(companyNameFromStorage);
    }
  }, []);

  const fetchData = useCallback(async () => {
    try {
      const response = await axios.get(`${API_URL_VehicleService}`);
      
      const allServiceData = response?.data?.Result || response?.data?.result || [];
      
      if (Array.isArray(allServiceData)) {
        // Filter service data by vehicle ID
        const vehicleServiceData = allServiceData.filter(service => 
          service.VehicleId === addServiceId || 
          service.vehicleId === addServiceId ||
          service._id === addServiceId
        );
        setData(vehicleServiceData);
      } else {
        setData([]);
      }
    } catch (error) {
      setData([]);
    }
  }, [addServiceId]);

  const handleDelete = async (id) => {
    try {
      const response = await axios.delete(`${API_URL_VehicleService}/${id}`);
      if (response?.data?.success) {
        setData((prev) => prev.filter((item) => item?._id !== id));
        toast.success(response.data.message);
      } else {
        toast.warn(response.data.message);
      }
    } catch (error) {
    }
  };

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    const companyName = getCompanyName();
    const superAdminName = getsuperadmincompanyname();
    const userName = getUserName();
    const storedcompanyName = companyName || superAdminName || userName;

    if (data.length > 0) {
      let companyFilteredData;

      if (userName?.toLowerCase() === "superadmin") {
        companyFilteredData = data;
      } else {
        companyFilteredData = data?.filter(
          (item) =>
            item?.adminCompanyName?.toLowerCase() === "superadmin" ||
            item?.adminCompanyName?.toLowerCase() === storedcompanyName?.toLowerCase()
        );
      }

      setBaseFilteredData(companyFilteredData);
    } else {
      setBaseFilteredData([]);
    }
  }, [data]);

  useEffect(() => {
    setFilteredData(baseFilteredData);
  }, [baseFilteredData]);

  const toggleTitleModal = () => {
    setIsOpenTitle((prev) => !prev);
    setselectedid(addServiceId);
  };

  const generatePDF = () => {
    const doc = new jsPDF();

    const formatDate = (date) => {
      const formattedDate = new Date(date);
      return `${String(formattedDate.getMonth() + 1).padStart(2, "0")}/${String(
        formattedDate.getDate()
      ).padStart(2, "0")}/${formattedDate.getFullYear()}`;
    };

    const pageWidth = doc?.internal?.pageSize?.width;
    const pageHeight = doc?.internal?.pageSize?.height;
    const margin = 14; 
    const footerHeight = 10;
    const rowHeight = 10;
    const padding = 2; 

    const tableColumn = [
      { name: "Service Dates", width: 30 },
      { name: "Due Dates", width: 25 },
      { name: "Service Miles", width: 25 },
      { name: "Status", width: 35 },
      { name: "Service Assign", width: 65 },
    ];

    const addHeader = () => {
      doc.setFontSize(14);
      doc.setFont("helvetica", "bold"); 
      doc.text("Services Records Report", pageWidth / 2, 10, {
        align: "center",
      });

      doc.setFontSize(10);
      doc.setFont("helvetica", "normal");
      const reportDate = new Date().toLocaleDateString();
      doc.text(`Report Generated: ${reportDate}`, margin, 20);

      const vehicleName = filteredData[0]?.VehicleName || "Name Unavailable";
      const vehicleRegistration =
        filteredData[0]?.registrationNumber || "Registration Unavailable";

      doc.text(`Vehicle Name: ${vehicleName}`, margin, 25);
      doc.text(`Registration Number: ${vehicleRegistration}`, margin, 30);
      doc.text(`Company Name: ${selectedCompanyName}`, margin, 35);
    };

    const addFooter = (pageNumber) => {
      doc.setFontSize(10);
      doc.text(`Page ${pageNumber}`, pageWidth / 2, pageHeight - footerHeight, {
        align: "center",
      });
    };

    const drawTableHeader = (startY) => {
      let currentX = margin;
      doc.setFont("helvetica", "bold");
      tableColumn.forEach((col) => {
        doc.text(col.name, currentX + padding, startY + 5);
        doc.rect(currentX, startY, col.width, rowHeight); 
        currentX += col.width;
      });
      return startY + rowHeight; 
    };

    const drawTableRow = (row, startY) => {
      let currentX = margin;
      const cellData = [
        row?.serviceCurrentDate ? formatDate(row.serviceCurrentDate) : "N/A", 
        row?.serviceDueDate ? formatDate(row.serviceDueDate) : "N/A", 
        row?.servicemailes || "N/A",
        row?.serviceStatus || "N/A",
        row?.asignto || "N/A",
      ];

      doc.setFont("helvetica", "normal"); 
      cellData?.forEach((text, index) => {
        const cellText = doc.splitTextToSize(
          text,
          tableColumn[index].width - padding * 2
        );

        const verticalAlign =
          startY + rowHeight / 2 - doc.getTextDimensions(cellText).h / 2;
        const horizontalAlign =
          index === 1 || index === 2
            ? currentX + padding
            : currentX +
            (tableColumn[index].width - doc.getTextDimensions(cellText).w) /
            2;

        doc.text(cellText, horizontalAlign, verticalAlign);
        doc.rect(currentX, startY, tableColumn[index].width, rowHeight); 
        currentX += tableColumn[index].width;
      });

      return rowHeight; 
    };

    let currentY = 45; 
    let pageNumber = 1;
    addHeader();

    currentY = drawTableHeader(currentY);

    filteredData.forEach((row) => {
      const cellHeight = drawTableRow(row, currentY);

      if (currentY + cellHeight + footerHeight > pageHeight) {
        addFooter(pageNumber);
        doc.addPage();
        pageNumber++;
        addHeader();
        currentY = 45; 
        currentY = drawTableHeader(currentY);
      }

      currentY += cellHeight;
    });

    addFooter(pageNumber);

    doc.save("Services_Records_Report.pdf");
  };

  return (
    <div className="h-[100vh] overflow-hidden">
      <Header className="min-w-full" />
      <div className="flex gap-4 w-full">
        <Sidebar />
        <div
          className="w-[80%] xl:w-[85%] h-screen flex flex-col justify-start overflow-y-auto pr-4"
          style={{
            height: "calc(100vh - 90px)",
          }}
        >
          <h1 className="text-[#313342] font-medium text-2xl py-5 pb-8  flex gap-2 items-center">
            <div className="myborder flex gap-3 border-2 border-t-0 border-l-0 border-r-0">
              <span className="opacity-65">Vehicle</span>
              <div className="flex items-center gap-3 myborder2">
                <span>
                  <Image width={2} height={4}
                    src="/setting_arrow.svg"
                    className="w-2 h-4 object-cover object-center  "
                    alt="arrow" />
                </span>
                <span>Service Report</span>
              </div>
            </div>
          </h1>

          <div className="py-5">
            <div className="drop-shadow-custom4">

              <div className="flex w-full py-2 px-2">
                <SearchAndAddBar
                  data={baseFilteredData}
                  itemperpage={itemperpage}
                  onFilterChange={handleFilterChange}
                  onItemsPerPageChange={handleItemsPerPageChange}
                  showSearch={false}
                  showAddButton={false}
                />
                <div className="flex justify-center gap-2 items-center">
                  <BackButton />
                  <button
                    onClick={generatePDF}
                    className={`w-[170px] font-sans text-sm font-semibold  px-5 h-10  border-[1px] rounded-lg border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500`}
                  >
                    Download Report
                  </button>
                  <button
                    onClick={toggleTitleModal}
                    className="w-[132px] font-sans  font-bold text-xs bg-[#313342] text-white rounded-lg hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 px-3 flex py-[12px] gap-2 items-center justify-center"
                  >
                    <Image width={15} height={15} src="/plus.svg" alt="Add Service" />
                    Add Service
                  </button>
                </div>
              </div>

              <div className="overflow-x-auto custom-scrollbar">
                <table className="w-full bg-white border table-auto">
                  <thead className="font-sans font-bold text-sm text-center bg-[#38384A] text-white">
                    <tr>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        Assigned To
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        Vehicle Name
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        Registration No
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        Service Dates
                      </th>
                      <th className="py-3 px-4 min-w-[158px] w-[158px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        Service Due Dates
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        Service Miles
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        Service Status
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] md:w-[16.66%] bg-[#38384A] text-white whitespace-normal break-all overflow-hidden">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="font-sans font-medium text-sm">
                    {paginatedData?.length > 0 ? (
                      paginatedData?.map((row) => (
                        <tr key={row?._id} className="border-b hover:bg-gray-100 text-center">
                          <td className="py-2 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                            {row?.asignto || "N/A"}
                          </td>
                          <td className="py-2 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                            {row?.VehicleName}
                          </td>
                          <td className="py-2 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                            {row?.registrationNumber}
                          </td>
                          <td className="py-2 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                            {(() => {
                              const date = new Date(row?.serviceCurrentDate);
                              return `${String(date.getMonth() + 1).padStart(
                                2,
                                "0"
                              )}/${String(date.getDate()).padStart(
                                2,
                                "0"
                              )}/${date.getFullYear()}`;
                            })() || "N/A"}
                          </td>
                          <td className="py-2 px-4 min-w-[150px] w-[150px] md:w-[16.66%]  whitespace-normal break-all overflow-hidden">
                            {(() => {
                              const date = new Date(row?.serviceDueDate);
                              return `${String(date.getMonth() + 1).padStart(
                                2,
                                "0"
                              )}/${String(date.getDate()).padStart(
                                2,
                                "0"
                              )}/${date.getFullYear()}`;
                            })() || "N/A"}
                          </td>
                          <td className="py-2 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                            {row?.servicemailes || "N/A"}
                          </td>
                          <td className="py-2 px-4 min-w-[150px] w-[150px] md:w-[16.66%] whitespace-normal break-all overflow-hidden">
                            {row?.serviceStatus || "N/A"}
                          </td>
                          <td className="py-2 px-4 min-w-[150px] w-[150px] md:w-[16.66%]  whitespace-normal break-all overflow-hidden">
                            <button
                              onClick={() => handleDelete(row._id)}
                              className="text-red-500 hover:text-red-700"
                            >
                              <Image width={25} height={25}
                                src="/trash.png"
                                alt="delete"
                                className="w-6"
                              />
                            </button>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan="8" className="py-8 text-center text-gray-500">
                          No service records found for this vehicle. Click &quot;Add Service&quot; to create a new record.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              <Pagination
                data={filteredData}
                itemperpage={itemperpage}
                onPageChange={(currentItems) => {
                  setPaginatedData(currentItems);
                }}
              />
            </div>
          </div>
        </div>
      </div>
      <AddServiceModal
        isOpen={isOpenTitle}
        onClose={toggleTitleModal}
        fetchData={fetchData}
        selectedid={selectedid}
      />
    </div>
  );
};

export default Page;
