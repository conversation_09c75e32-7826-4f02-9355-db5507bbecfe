import { NextResponse } from 'next/server';
import { IncomingForm } from 'formidable';
import { Readable } from 'stream';
import fs from 'fs';
import path from 'path';

// ---------- App‑router route config ----------
export const dynamic = 'force-dynamic'; // disable static optimization
export const runtime = 'nodejs';        // ensure Node runtime (not edge)


// Make sure /public/uploads exists
const uploadDir = path.join(process.cwd(), 'public', 'uploads');
fs.mkdirSync(uploadDir, { recursive: true });

/** Convert the Web Request (req.body is a ReadableStream) to a Node Readable */
function toNodeReadable(webRequest) {
  if (!webRequest.body) throw new Error('No request body');
  const headers = {};
  webRequest.headers.forEach((v, k) => (headers[k.toLowerCase()] = v));

  const nodeStream = Readable.fromWeb(webRequest.body);
  nodeStream.headers = headers;
  return nodeStream;
}

export async function POST(req) {
  try {
    // --- 1. convert request ---
    const nodeReq = toNodeReadable(req);

    // --- 2. configure formidable ---
    const form = new IncomingForm({
      uploadDir,
      keepExtensions: true,
      multiples: false,
      maxFileSize: 10 * 1024 ** 2, // 10 MB
    });

    // --- 3. parse ---
    const { files } = await new Promise((res, rej) =>
      form.parse(nodeReq, (err, fields, files) =>
        err ? rej(err) : res({ fields, files })
      )
    );

    const uploaded = files.file?.[0] ?? files.file; // Handle both single & array

    if (!uploaded)
      return NextResponse.json({ error: 'No file sent' }, { status: 400 });

    const fileUrl = `/uploads/${path.basename(uploaded.filepath)}`;

    return NextResponse.json(
      {
        message: 'File uploaded successfully',
        fileUrl,
        originalName: uploaded.originalFilename,
      },
      { status: 200 }
    );
  } catch (err) {
    console.error('Upload error:', err);
    return NextResponse.json({ error: 'Upload failed' }, { status: 500 });
  }
}
