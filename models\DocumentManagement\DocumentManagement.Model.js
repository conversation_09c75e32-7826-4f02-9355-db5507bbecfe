// models/DocumentManagement.Model.js
import mongoose from "mongoose";

// Define the schema
const DocumentManagementSchema = new mongoose.Schema({
  documentname: {
    type: String,
    required: true,
    trim: true,
  },
  entityType: {
    type: [String],
    required: true,
    enum: ["Driver", "Vehicle", "Admins"],
  },
  description: {
    type: String,
    trim: true,
  },
  isActive: {
    type: Boolean,
    default: false, // Defaults to false if not specified
  },
  adminCreatedBy: { type: String },
  adminCompanyName: { type: String },
  adminCompanyId: { type: String },
}, {
  timestamps: true // Adds createdAt and updatedAt fields
});

// Create the model
export default mongoose.models.DocumentManagement || mongoose.model("DocumentManagement", DocumentManagementSchema);
